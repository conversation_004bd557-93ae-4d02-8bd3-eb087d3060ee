import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  category: z.string().optional(),
  level: z.string().optional(),
  minPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  maxPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  isPublished: z.enum(['true', 'false']).optional().default('true')
})

// GET /api/courses - Get all courses with optional filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        category,
        level,
        minPrice,
        maxPrice,
        isPublished
      } = validatedQuery

      // Build where clause for filtering
      const where: any = {
        isActive: true,
        isPublished: isPublished === 'true'
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { shortDescription: { contains: search, mode: 'insensitive' } },
          { instructor: { name: { contains: search, mode: 'insensitive' } } }
        ]
      }

      if (category) {
        where.category = { equals: category, mode: 'insensitive' }
      }

      if (level) {
        where.level = { equals: level, mode: 'insensitive' }
      }

      if (minPrice !== undefined || maxPrice !== undefined) {
        where.price = {}
        if (minPrice !== undefined) where.price.gte = minPrice
        if (maxPrice !== undefined) where.price.lte = maxPrice
      }

      // Get total count for pagination
      const total = await prisma.course.count({ where })

      // Get courses with pagination
      const courses = await prisma.course.findMany({
        where,
        orderBy: [
          { rating: 'desc' },
          { studentsCount: 'desc' },
          { publishedAt: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true
            }
          },
          sections: {
            where: { isPublished: true },
            include: {
              chapters: {
                where: { isPublished: true },
                include: {
                  lessons: {
                    where: { isPublished: true },
                    select: { id: true, duration: true }
                  }
                }
              }
            }
          },
          _count: {
            select: { enrollments: true }
          }
        }
      })

      // Check which courses the user is enrolled in (active or completed)
      const userEnrollments = await prisma.courseEnrollment.findMany({
        where: {
          userId: user.id,
          courseId: { in: courses.map(c => c.id) },
          status: {
            in: ['active', 'completed']
          }
        },
        select: { courseId: true }
      })

      const enrolledCourseIds = new Set(userEnrollments.map(e => e.courseId))

      // Add enrollment status and calculated fields to courses
      const coursesWithEnrollment = courses.map(course => {
        // Calculate total lessons and duration
        const totalLessons = course.sections.reduce((acc, section) =>
          acc + section.chapters.reduce((chapterAcc, chapter) =>
            chapterAcc + chapter.lessons.length, 0), 0)

        const totalDuration = course.sections.reduce((acc, section) =>
          acc + section.chapters.reduce((chapterAcc, chapter) =>
            chapterAcc + chapter.lessons.reduce((lessonAcc, lesson) =>
              lessonAcc + (lesson.duration || 0), 0), 0), 0)

        return {
          ...course,
          isEnrolled: enrolledCourseIds.has(course.id),
          enrollmentCount: course._count.enrollments,
          totalLessons,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          sections: undefined // Remove sections from response to keep it clean
        }
      })

      return APIResponse.success({
        courses: coursesWithEnrollment,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } catch (error) {
      console.error('Error fetching courses:', error)
      return APIResponse.error('Failed to fetch courses', 500)
    }
  }
)
