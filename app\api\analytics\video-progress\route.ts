import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const videoProgressSchema = z.object({
  sessionId: z.string(),
  lessonId: z.string(),
  totalWatchTime: z.number(),
  completionPercentage: z.number(),
  timestamp: z.number()
})

// POST /api/analytics/video-progress - Update video progress in real-time
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: videoProgressSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      // Verify the session belongs to the user
      const session = await prisma.videoAnalytics.findFirst({
        where: {
          sessionId: validatedBody.sessionId,
          userId: user.id
        }
      })

      if (!session) {
        return APIResponse.error('Session not found or access denied', 404)
      }

      // Update the session with latest progress
      await prisma.videoAnalytics.update({
        where: { sessionId: validatedBody.sessionId },
        data: {
          totalWatchTime: validatedBody.totalWatchTime,
          completionPercentage: validatedBody.completionPercentage,
          updatedAt: new Date(validatedBody.timestamp)
        }
      })

      // Also update the lesson progress for the user
      await prisma.courseProgress.upsert({
        where: {
          userId_lessonId: {
            userId: user.id,
            lessonId: validatedBody.lessonId
          }
        },
        update: {
          watchTime: validatedBody.totalWatchTime,
          lastPosition: Math.floor(validatedBody.totalWatchTime),
          isCompleted: validatedBody.completionPercentage >= 90,
          lastAccessAt: new Date()
        },
        create: {
          userId: user.id,
          lessonId: validatedBody.lessonId,
          watchTime: validatedBody.totalWatchTime,
          lastPosition: Math.floor(validatedBody.totalWatchTime),
          isCompleted: validatedBody.completionPercentage >= 90,
          lastAccessAt: new Date()
        }
      })

      return APIResponse.success({
        message: 'Video progress updated successfully'
      })
    } catch (error) {
      console.error('Error updating video progress:', error)
      return APIResponse.error('Failed to update video progress', 500)
    }
  }
)

// GET /api/analytics/video-progress - Get video progress analytics (admin only)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const { searchParams } = new URL(request.url)
      const courseId = searchParams.get('courseId')
      const lessonId = searchParams.get('lessonId')
      const userId = searchParams.get('userId')
      const timeframe = searchParams.get('timeframe') || '7d' // 7d, 30d, 90d

      // Calculate date range based on timeframe
      const now = new Date()
      const startDate = new Date()
      
      switch (timeframe) {
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case '90d':
          startDate.setDate(now.getDate() - 90)
          break
        default:
          startDate.setDate(now.getDate() - 7)
      }

      // Build where clause
      const where: any = {
        startTime: {
          gte: startDate,
          lte: now
        }
      }
      
      if (courseId) where.courseId = courseId
      if (lessonId) where.lessonId = lessonId
      if (userId) where.userId = userId

      // Get progress data grouped by day
      const progressData = await prisma.videoAnalytics.groupBy({
        by: ['lessonId'],
        where,
        _avg: {
          completionPercentage: true,
          totalWatchTime: true
        },
        _count: {
          sessionId: true
        },
        _sum: {
          totalWatchTime: true
        }
      })

      // Get lesson completion rates
      const completionRates = await prisma.videoAnalytics.groupBy({
        by: ['lessonId'],
        where: {
          ...where,
          completionPercentage: {
            gte: 90
          }
        },
        _count: {
          sessionId: true
        }
      })

      // Get total sessions per lesson
      const totalSessions = await prisma.videoAnalytics.groupBy({
        by: ['lessonId'],
        where,
        _count: {
          sessionId: true
        }
      })

      // Calculate completion rates
      const lessonStats = progressData.map(lesson => {
        const completed = completionRates.find(c => c.lessonId === lesson.lessonId)?._count.sessionId || 0
        const total = totalSessions.find(t => t.lessonId === lesson.lessonId)?._count.sessionId || 0
        
        return {
          lessonId: lesson.lessonId,
          averageCompletion: lesson._avg.completionPercentage || 0,
          averageWatchTime: lesson._avg.totalWatchTime || 0,
          totalWatchTime: lesson._sum.totalWatchTime || 0,
          totalSessions: lesson._count.sessionId,
          completionRate: total > 0 ? (completed / total) * 100 : 0,
          completedSessions: completed
        }
      })

      // Get overall statistics
      const overallStats = await prisma.videoAnalytics.aggregate({
        where,
        _avg: {
          completionPercentage: true,
          totalWatchTime: true,
          qualityChanges: true,
          bufferEvents: true
        },
        _sum: {
          totalWatchTime: true
        },
        _count: {
          sessionId: true
        }
      })

      return APIResponse.success({
        timeframe,
        lessonStatistics: lessonStats,
        overallStatistics: {
          totalSessions: overallStats._count.sessionId,
          averageCompletion: overallStats._avg.completionPercentage || 0,
          averageWatchTime: overallStats._avg.totalWatchTime || 0,
          totalWatchTime: overallStats._sum.totalWatchTime || 0,
          averageQualityChanges: overallStats._avg.qualityChanges || 0,
          averageBufferEvents: overallStats._avg.bufferEvents || 0
        }
      })
    } catch (error) {
      console.error('Error fetching video progress analytics:', error)
      return APIResponse.error('Failed to fetch video progress analytics', 500)
    }
  }
)
