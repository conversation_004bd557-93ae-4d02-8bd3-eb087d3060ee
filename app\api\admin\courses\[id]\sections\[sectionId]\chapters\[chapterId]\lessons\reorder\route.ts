import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reorderSchema = z.object({
  lessons: z.array(z.object({
    id: z.string()
  }))
})

export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: reorderSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      // Verify chapter exists
      const chapter = await prisma.courseChapter.findUnique({
        where: {
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        },
        select: { id: true }
      })

      if (!chapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      // Update lesson timestamps to reflect new order
      const updatePromises = validatedBody.lessons.map((lesson: any, index: number) => {
        const timestamp = new Date(Date.now() + index * 1000)
        return prisma.courseLesson.update({
          where: { id: lesson.id },
          data: { updatedAt: timestamp }
        })
      })

      await Promise.all(updatePromises)

      return APIResponse.success({
        message: 'Lessons reordered successfully'
      })
    } catch (error) {
      console.error('Error reordering lessons:', error)
      return APIResponse.error('Failed to reorder lessons', 500)
    }
  }
)
