"use client"

import { useSession } from "next-auth/react"
import { useMaintenanceMode } from "@/lib/settings-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Al<PERSON><PERSON>riangle, <PERSON>tings, RefreshCw } from "lucide-react"
import Link from "next/link"

export function MaintenanceMode({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession()
  const isMaintenanceMode = useMaintenanceMode()

  // If maintenance mode is not active, render children normally
  if (!isMaintenanceMode) {
    return <>{children}</>
  }

  // If user is admin, show maintenance notice but allow access
  if (session?.user?.role === 'ADMIN') {
    return (
      <div className="space-y-4">
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Maintenance Mode Active</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            The platform is in maintenance mode. Only administrators can access the system.
          </p>
          <Link href="/admin/settings">
            <Button variant="outline" size="sm" className="mt-2 gap-2">
              <Settings className="h-4 w-4" />
              Manage Settings
            </Button>
          </Link>
        </div>
        {children}
      </div>
    )
  }

  // For non-admin users, show maintenance page
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
          <CardTitle className="text-2xl">System Maintenance</CardTitle>
          <CardDescription>
            We&apos;re currently performing scheduled maintenance to improve your experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            Our platform is temporarily unavailable while we make important updates. 
            We apologize for any inconvenience and appreciate your patience.
          </p>
          <div className="space-y-2">
            <p className="text-sm font-medium">What&apos;s happening?</p>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• System updates and improvements</li>
              <li>• Database optimization</li>
              <li>• Security enhancements</li>
            </ul>
          </div>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            className="gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Check Again
          </Button>
          <p className="text-xs text-muted-foreground">
            Expected completion: We&apos;ll be back shortly
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
