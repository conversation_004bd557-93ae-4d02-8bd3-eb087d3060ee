'use client';

import { useEffect, useRef, useState } from 'react';

interface UseIntersectionObserverProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  freezeOnceVisible?: boolean;
}

export function useIntersectionObserver({
  threshold = 0.1,
  root = null,
  rootMargin = '0%',
  freezeOnceVisible = false,
}: UseIntersectionObserverProps = {}) {
  const [entry, setEntry] = useState<IntersectionObserverEntry>();
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  const frozen = entry?.isIntersecting && freezeOnceVisible;

  const updateEntry = ([entry]: IntersectionObserverEntry[]): void => {
    setEntry(entry);
    setIsVisible(entry.isIntersecting);
  };

  useEffect(() => {
    const node = elementRef?.current; // DOM Ref
    const hasIOSupport = !!window.IntersectionObserver;

    // Fallback: if IntersectionObserver is not supported, show content
    if (!hasIOSupport) {
      setIsVisible(true);
      return;
    }

    if (frozen || !node) return;

    const observerParams = { threshold, root, rootMargin };
    const observer = new IntersectionObserver(updateEntry, observerParams);

    observer.observe(node);

    return () => observer.disconnect();
  }, [elementRef?.current, JSON.stringify(threshold), root, rootMargin, frozen]);

  return { elementRef, entry, isVisible };
}

// Hook for multiple elements
export function useIntersectionObservers(
  elementsCount: number,
  options?: UseIntersectionObserverProps
) {
  const [visibleElements, setVisibleElements] = useState<boolean[]>(
    new Array(elementsCount).fill(false)
  );
  const elementRefs = useRef<(Element | null)[]>(new Array(elementsCount).fill(null));

  useEffect(() => {
    const hasIOSupport = !!window.IntersectionObserver;
    if (!hasIOSupport) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = elementRefs.current.findIndex((ref) => ref === entry.target);
          if (index !== -1) {
            setVisibleElements((prev) => {
              const newState = [...prev];
              newState[index] = entry.isIntersecting;
              return newState;
            });
          }
        });
      },
      {
        threshold: options?.threshold || 0.1,
        root: options?.root || null,
        rootMargin: options?.rootMargin || '0%',
      }
    );

    elementRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, [elementsCount, options]);

  const setElementRef = (index: number) => (element: Element | null) => {
    elementRefs.current[index] = element;
  };

  return { setElementRef, visibleElements };
}

// Hook for scroll-triggered animations
export function useScrollAnimation(delay = 0) {
  const { elementRef, isVisible } = useIntersectionObserver({
    threshold: 0.1,
    freezeOnceVisible: true,
  });

  const [shouldAnimate, setShouldAnimate] = useState(false);

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        setShouldAnimate(true);
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [isVisible, delay]);

  return { elementRef, isVisible, shouldAnimate };
}
