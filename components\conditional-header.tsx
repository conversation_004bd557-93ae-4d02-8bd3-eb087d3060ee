"use client"

import { usePathname } from "next/navigation"
import { Head<PERSON> } from "./header"

export function ConditionalHeader() {
  const pathname = usePathname()

  // Don't show header on pages that have their own layout systems or are standalone pages
  const hideHeader = pathname === '/' ||
                    pathname?.startsWith('/student') ||
                    pathname?.startsWith('/admin') ||
                    pathname?.startsWith('/auth/') ||
                    pathname === '/quiz-generator' ||
                    pathname === '/test-thumbnail' ||
                    pathname === '/unauthorized' ||
                    pathname === '/dashboard'

  if (hideHeader) {
    return null
  }

  return <Header />
}
