/**
 * Course File Manager
 * 
 * Utility for managing course-specific file organization in Bunny CDN
 */

import { getBunnyStorageWithTest } from './bunny-storage'

export interface CourseFileStructure {
  courseId: string
  images: string[]
  files: string[]
  videos: string[]
}

export interface FileUploadOptions {
  courseId: string
  lessonId?: string
  type: 'image' | 'file' | 'video'
  category?: string // e.g., 'thumbnail', 'attachment', 'lesson-video'
}

export class CourseFileManager {
  private storage: any

  constructor() {
    this.storage = null
  }

  private async getStorage() {
    if (!this.storage) {
      this.storage = await getBunnyStorageWithTest()
    }
    return this.storage
  }

  /**
   * Get the appropriate folder path for a course file
   */
  getCourseFolderPath(courseId: string, fileType: 'images' | 'files' | 'videos'): string {
    return `courses/${courseId}/${fileType}`
  }

  /**
   * Generate a unique filename with course context
   */
  generateCourseFilename(
    originalName: string,
    options: FileUploadOptions
  ): string {
    const timestamp = Date.now()
    const extension = originalName.split('.').pop() || 'bin'
    const cleanName = originalName
      .replace(/\.[^/.]+$/, '') // Remove extension
      .replace(/[^a-zA-Z0-9]/g, '-') // Replace special chars with dash
      .toLowerCase()
      .substring(0, 30) // Limit length

    let prefix: string = options.type
    if (options.category) {
      prefix = `${options.category}-${options.type}`
    }
    if (options.lessonId) {
      prefix = `${prefix}-${options.lessonId}`
    }

    return `${prefix}-${cleanName}-${timestamp}.${extension}`
  }

  /**
   * Upload a file to the appropriate course folder
   */
  async uploadCourseFile(
    file: File | Buffer,
    options: FileUploadOptions
  ): Promise<{
    success: boolean
    url?: string
    filename?: string
    folder?: string
    size?: number
    error?: string
  }> {
    try {
      const storage = await this.getStorage()
      
      const folderMap = {
        image: 'images',
        file: 'files',
        video: 'videos'
      } as const

      const folderType = folderMap[options.type]
      const folder = this.getCourseFolderPath(options.courseId, folderType)
      
      const filename = this.generateCourseFilename(
        file instanceof File ? file.name : 'file',
        options
      )

      const uploadResult = await storage.uploadFile(file, {
        folder,
        filename,
        maxSize: this.getMaxFileSize(options.type)
      })

      if (uploadResult.success) {
        return {
          success: true,
          url: uploadResult.url,
          filename: uploadResult.filename,
          folder,
          size: uploadResult.size
        }
      } else {
        return {
          success: false,
          error: uploadResult.error
        }
      }
    } catch (error) {
      console.error('Course file upload error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Get maximum file size based on file type
   */
  private getMaxFileSize(type: 'image' | 'file' | 'video'): number {
    const sizes = {
      image: 5 * 1024 * 1024,   // 5MB
      file: 50 * 1024 * 1024,   // 50MB
      video: 100 * 1024 * 1024  // 100MB
    }
    return sizes[type]
  }

  /**
   * List all files for a specific course
   */
  async listCourseFiles(courseId: string): Promise<CourseFileStructure> {
    try {
      const storage = await this.getStorage()

      // Get files from database records instead of storage API
      // This is more reliable and includes metadata
      const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
      const coursePrefix = `${pullZoneUrl}/courses/${courseId}/`

      // Query database for course-related files
      const { prisma } = await import('./prisma')

      const courseFiles = await prisma.file.findMany({
        where: {
          url: {
            startsWith: coursePrefix
          }
        },
        select: {
          url: true,
          filename: true,
          size: true,
          mimeType: true
        }
      })

      // Categorize files by type
      const images: string[] = []
      const files: string[] = []
      const videos: string[] = []

      courseFiles.forEach(file => {
        const relativePath = file.url.replace(coursePrefix, '')

        if (relativePath.startsWith('images/')) {
          images.push(relativePath)
        } else if (relativePath.startsWith('files/')) {
          files.push(relativePath)
        } else if (relativePath.startsWith('videos/')) {
          videos.push(relativePath)
        }
      })

      return {
        courseId,
        images,
        files,
        videos
      }
    } catch (error) {
      console.error('Error listing course files:', error)
      return {
        courseId,
        images: [],
        files: [],
        videos: []
      }
    }
  }

  /**
   * Delete a course file
   */
  async deleteCourseFile(fileUrl: string): Promise<boolean> {
    try {
      const storage = await this.getStorage()
      
      // Extract the file path from the URL
      const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL
      if (!pullZoneUrl || !fileUrl.startsWith(pullZoneUrl)) {
        console.error('Invalid file URL for deletion:', fileUrl)
        return false
      }

      const filepath = fileUrl.replace(pullZoneUrl + '/', '')
      return await storage.deleteFile(filepath)
    } catch (error) {
      console.error('Error deleting course file:', error)
      return false
    }
  }

  /**
   * Move files from old structure to new course-specific structure
   */
  async migrateToCourseStructure(
    oldFileUrl: string,
    courseId: string,
    fileType: 'images' | 'files' | 'videos'
  ): Promise<{
    success: boolean
    newUrl?: string
    error?: string
  }> {
    try {
      const storage = await this.getStorage()
      const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''

      // Check if file is already in course structure
      if (oldFileUrl.includes(`/courses/${courseId}/`)) {
        return {
          success: true,
          newUrl: oldFileUrl
        }
      }

      // Extract filename from old URL
      const filename = oldFileUrl.split('/').pop()
      if (!filename) {
        return {
          success: false,
          error: 'Invalid file URL'
        }
      }

      // Generate new path
      const newFolder = this.getCourseFolderPath(courseId, fileType)
      const newFilename = `migrated-${Date.now()}-${filename}`

      try {
        // Download file from old location
        const response = await fetch(oldFileUrl)
        if (!response.ok) {
          throw new Error(`Failed to download file: ${response.statusText}`)
        }

        const fileBuffer = Buffer.from(await response.arrayBuffer())

        // Upload to new location
        const uploadResult = await storage.uploadFile(fileBuffer, {
          folder: newFolder,
          filename: newFilename,
          maxSize: this.getMaxFileSize(fileType === 'images' ? 'image' : fileType === 'files' ? 'file' : 'video')
        })

        if (uploadResult.success) {
          // Update database record if exists
          const { prisma } = await import('./prisma')
          await prisma.file.updateMany({
            where: { url: oldFileUrl },
            data: { url: uploadResult.url }
          })

          return {
            success: true,
            newUrl: uploadResult.url
          }
        } else {
          return {
            success: false,
            error: uploadResult.error
          }
        }
      } catch (downloadError) {
        return {
          success: false,
          error: downloadError instanceof Error ? downloadError.message : 'Migration failed'
        }
      }
    } catch (error) {
      console.error('Error migrating file:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Migration failed'
      }
    }
  }

  /**
   * Get course storage statistics
   */
  async getCourseStorageStats(courseId: string): Promise<{
    totalFiles: number
    totalSize: number
    imageCount: number
    fileCount: number
    videoCount: number
  }> {
    try {
      const { prisma } = await import('./prisma')
      const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
      const coursePrefix = `${pullZoneUrl}/courses/${courseId}/`

      // Get all files for this course from database
      const courseFiles = await prisma.file.findMany({
        where: {
          url: {
            startsWith: coursePrefix
          }
        },
        select: {
          url: true,
          size: true,
          mimeType: true
        }
      })

      let totalSize = 0
      let imageCount = 0
      let fileCount = 0
      let videoCount = 0

      courseFiles.forEach(file => {
        totalSize += file.size || 0

        const relativePath = file.url.replace(coursePrefix, '')
        if (relativePath.startsWith('images/')) {
          imageCount++
        } else if (relativePath.startsWith('files/')) {
          fileCount++
        } else if (relativePath.startsWith('videos/')) {
          videoCount++
        }
      })

      return {
        totalFiles: courseFiles.length,
        totalSize,
        imageCount,
        fileCount,
        videoCount
      }
    } catch (error) {
      console.error('Error getting course storage stats:', error)
      return {
        totalFiles: 0,
        totalSize: 0,
        imageCount: 0,
        fileCount: 0,
        videoCount: 0
      }
    }
  }
}

// Export singleton instance
export const courseFileManager = new CourseFileManager()

// Export utility functions
export function getCourseImageUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/images/${filename}`
}

export function getCourseFileUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/files/${filename}`
}

export function getCourseVideoUrl(courseId: string, filename: string): string {
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL || ''
  return `${pullZoneUrl}/courses/${courseId}/videos/${filename}`
}

export function extractCourseIdFromUrl(fileUrl: string): string | null {
  const match = fileUrl.match(/\/courses\/([^\/]+)\//)
  return match ? match[1] : null
}

export function getFileTypeFromUrl(fileUrl: string): 'images' | 'files' | 'videos' | null {
  if (fileUrl.includes('/images/')) return 'images'
  if (fileUrl.includes('/files/')) return 'files'
  if (fileUrl.includes('/videos/')) return 'videos'
  return null
}
