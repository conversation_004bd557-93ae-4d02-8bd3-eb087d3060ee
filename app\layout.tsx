import "./globals.css";
import { Metadata } from "next";
import { Toaster } from "sonner";
import { ThemeProvider } from "next-themes";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { AuthProvider } from "@/components/providers/session-provider";
import { SettingsProvider } from "@/lib/settings-context";
import { MaintenanceMode } from "@/components/maintenance-mode";
import { ConditionalHeader } from "@/components/conditional-header";

import { RoleUpdateHandler } from "@/components/auth/role-update-handler";
import { SkipNav, SkipNavTarget } from "@/components/accessibility/skip-nav";
import { GlobalKeyboardShortcuts } from "@/components/accessibility/keyboard-shortcuts";

const geist = Geist({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL("https://preplocus.vercel.app"),
  title: "PrepLocus - India's #1 Online Exam Coaching Platform",
  description: "Crack every Indian exam with PrepLocus. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more. Start your free trial today!",
  keywords: ["JEE", "NEET", "UPSC", "SSC", "Banking", "online coaching", "exam preparation", "India", "competitive exams", "AI learning"],
  authors: [{ name: "PrepLocus Team" }],
  openGraph: {
    title: "PrepLocus - India's #1 Online Exam Coaching Platform",
    description: "Crack every Indian exam with PrepLocus. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more.",
    type: "website",
    locale: "en_IN",
  },
  twitter: {
    card: "summary_large_image",
    title: "PrepLocus - India's #1 Online Exam Coaching Platform",
    description: "Crack every Indian exam with PrepLocus. Expert teachers, AI-powered learning, and proven results for JEE, NEET, UPSC, SSC, Banking, and more.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${geist.className}`}>
      <body suppressHydrationWarning>
        <AuthProvider>
          <SettingsProvider>
            <ThemeProvider attribute="class" enableSystem defaultTheme="light">
              <SkipNav />
              <RoleUpdateHandler />
              <GlobalKeyboardShortcuts />
              <MaintenanceMode>
                <div className="relative flex min-h-screen flex-col">
                  <ConditionalHeader />
                  <SkipNavTarget>
                    {children}
                  </SkipNavTarget>
                </div>
              </MaintenanceMode>
              <Toaster position="top-center" richColors />
            </ThemeProvider>
          </SettingsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
