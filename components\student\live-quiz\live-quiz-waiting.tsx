"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Clock, 
  Play, 
  Users, 
  Eye,
  Target,
  Timer,
  BookOpen,
  Zap,
  Square,
  Calendar
} from "lucide-react"
import { motion } from "framer-motion"
import { format } from "date-fns"

interface Session {
  id: string
  title: string
  status: string
  currentQuestion: number
  questionTimeLimit?: number
  showLeaderboard: boolean
}

interface Quiz {
  id: string
  title: string
  questionCount: number
}

interface LiveQuizWaitingProps {
  session: Session
  quiz: Quiz
  isParticipating: boolean
  canJoin: boolean
  onJoin: () => void
  onLeave: () => void
}

export function LiveQuizWaiting({ 
  session, 
  quiz, 
  isParticipating, 
  canJoin, 
  onJoin, 
  onLeave 
}: LiveQuizWaitingProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="glass">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-yellow-100 dark:bg-yellow-900 rounded-full w-fit">
            <Clock className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
          </div>
          <CardTitle className="text-xl">Waiting for Quiz to Start</CardTitle>
          <CardDescription>
            The instructor will start the quiz shortly. Please wait...
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Quiz Information */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h3 className="font-semibold text-lg">{quiz.title}</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-muted-foreground" />
                <span>{quiz.questionCount} questions</span>
              </div>
              
              {session.questionTimeLimit && (
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-muted-foreground" />
                  <span>{session.questionTimeLimit}s per question</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-muted-foreground" />
                <span>Live scoring</span>
              </div>
              
              {session.showLeaderboard && (
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span>Live leaderboard</span>
                </div>
              )}
            </div>
          </div>

          {/* Status and Actions */}
          <div className="text-center space-y-4">
            {isParticipating ? (
              <div className="space-y-4">
                <div className="flex items-center justify-center gap-2">
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <Users className="h-3 w-3 mr-1" />
                    Joined
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    You&apos;re ready to start!
                  </span>
                </div>
                
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-green-700 dark:text-green-300 mb-2">
                    <Play className="h-4 w-4" />
                    <span className="font-medium">Ready to Start</span>
                  </div>
                  <p className="text-sm text-green-600 dark:text-green-400">
                    You&apos;ve successfully joined the quiz. The instructor will start it soon.
                  </p>
                </div>

                <Button
                  variant="outline"
                  onClick={onLeave}
                  className="w-full sm:w-auto"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Leave Session
                </Button>
              </div>
            ) : canJoin ? (
              <div className="space-y-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-blue-700 dark:text-blue-300 mb-2">
                    <Users className="h-4 w-4" />
                    <span className="font-medium">Join the Quiz</span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-400 mb-4">
                    Click the button below to join this live quiz session.
                  </p>
                  <Button
                    onClick={onJoin}
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Join Quiz
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-2 text-red-700 dark:text-red-300 mb-2">
                    <Square className="h-4 w-4" />
                    <span className="font-medium">Cannot Join</span>
                  </div>
                  <p className="text-sm text-red-600 dark:text-red-400">
                    This quiz session is full or no longer accepting new participants.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-muted/30 rounded-lg p-4">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              How it works
            </h4>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">1.</span>
                <span>Wait for the instructor to start the quiz</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">2.</span>
                <span>Answer questions as they appear on your screen</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">3.</span>
                <span>See your real-time ranking on the leaderboard</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary font-bold">4.</span>
                <span>Compete with other participants for the top score!</span>
              </li>
            </ul>
          </div>

          {/* Tips */}
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-4">
            <h4 className="font-medium mb-3 flex items-center gap-2">
              <Zap className="h-4 w-4 text-primary" />
              Pro Tips
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Answer quickly but accurately for maximum points</li>
              <li>• Keep an eye on the timer for each question</li>
              <li>• Stay focused and avoid distractions</li>
              {session.showLeaderboard && (
                <li>• Check the leaderboard to see your ranking</li>
              )}
            </ul>
          </div>

          {/* Animated waiting indicator */}
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <div className="flex gap-1">
              <motion.div
                className="w-2 h-2 bg-primary rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, delay: 0 }}
              />
              <motion.div
                className="w-2 h-2 bg-primary rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
              />
              <motion.div
                className="w-2 h-2 bg-primary rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
              />
            </div>
            <span className="text-sm">Waiting for instructor...</span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
