'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import { 
  ArrowLeftIcon, 
  PlayIcon, 
  DocumentTextIcon, 
  DocumentIcon,
  ClipboardDocumentListIcon,
  AcademicCapIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

interface LessonPreview {
  id: string
  title: string
  description?: string
  type: 'VIDEO' | 'TEXT' | 'QUIZ' | 'ASSIGNMENT' | 'DOCUMENT'
  duration?: number
  isFree: boolean
  content?: string
  video?: {
    id: string
    url: string
    duration?: number
    thumbnailUrl?: string
  }
  attachments: any[]
  course: {
    id: string
    title: string
    slug: string
  }
  isEnrolled: boolean
  hasAccess: boolean
}

export default function LessonPreviewPage() {
  const params = useParams()
  const router = useRouter()
  const lessonId = params?.lessonId as string

  const [lesson, setLesson] = useState<LessonPreview | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (lessonId) {
      fetchLessonPreview()
    }
  }, [lessonId])

  const fetchLessonPreview = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/courses/lessons/${lessonId}/preview`)
      
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to fetch lesson preview')
      }

      const data = await response.json()
      setLesson(data.data.lesson)
    } catch (error: any) {
      console.error('Error fetching lesson preview:', error)
      toast.error(error.message || 'Failed to load lesson preview')
      router.back()
    } finally {
      setLoading(false)
    }
  }

  const getLessonIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return PlayIcon
      case 'TEXT':
        return DocumentTextIcon
      case 'DOCUMENT':
        return DocumentIcon
      case 'ASSIGNMENT':
        return ClipboardDocumentListIcon
      case 'QUIZ':
        return AcademicCapIcon
      default:
        return DocumentTextIcon
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading lesson preview...</p>
        </div>
      </div>
    )
  }

  if (!lesson) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-300">Lesson not found</p>
        </div>
      </div>
    )
  }

  const LessonIcon = getLessonIcon(lesson.type)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Back to Course</span>
          </Button>
          
          <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
            Free Preview
          </Badge>
        </div>

        {/* Course Info */}
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Course: {lesson.course.title}
          </p>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
            {lesson.title}
          </h1>
          {lesson.description && (
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {lesson.description}
            </p>
          )}
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <LessonIcon className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {lesson.type}
              </span>
            </div>
            {lesson.duration && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Duration: {formatDuration(lesson.duration)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Lesson Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl">
              <CardContent className="p-6">
                {lesson.type === 'VIDEO' && lesson.video && (
                  <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded-xl overflow-hidden mb-6">
                    <video
                      src={lesson.video.url}
                      controls
                      className="w-full h-full object-cover"
                      poster={lesson.video.thumbnailUrl}
                    >
                      Your browser does not support the video tag.
                    </video>
                  </div>
                )}

                {lesson.type === 'TEXT' && lesson.content && (
                  <div className="prose dark:prose-invert max-w-none">
                    <div dangerouslySetInnerHTML={{ __html: lesson.content }} />
                  </div>
                )}

                {lesson.type === 'DOCUMENT' && (
                  <div className="text-center py-12">
                    <DocumentIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                      Document Lesson
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      This lesson contains downloadable documents and materials.
                    </p>
                  </div>
                )}

                {lesson.type === 'ASSIGNMENT' && (
                  <div className="text-center py-12">
                    <ClipboardDocumentListIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                      Assignment
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      This lesson contains assignment instructions and materials.
                    </p>
                    {lesson.content && (
                      <div className="mt-6 text-left">
                        <div className="prose dark:prose-invert max-w-none">
                          <div dangerouslySetInnerHTML={{ __html: lesson.content }} />
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {lesson.type === 'QUIZ' && (
                  <div className="text-center py-12">
                    <AcademicCapIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                      Quiz Lesson
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      This lesson contains an interactive quiz.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Attachments */}
            {lesson.attachments && lesson.attachments.length > 0 && (
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-xl">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    Attachments
                  </h3>
                  <div className="space-y-3">
                    {lesson.attachments.map((attachment: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <DocumentIcon className="w-5 h-5 text-blue-600" />
                          <div>
                            <p className="text-sm font-medium text-gray-800 dark:text-white">
                              {attachment.originalName || attachment.name}
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {attachment.size ? `${Math.round(attachment.size / 1024)} KB` : ''}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => window.open(attachment.url, '_blank')}
                          >
                            <EyeIcon className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              const a = document.createElement('a')
                              a.href = attachment.url
                              a.download = attachment.originalName || attachment.name
                              a.click()
                            }}
                          >
                            <ArrowDownTrayIcon className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Enrollment CTA */}
            {!lesson.isEnrolled && (
              <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 shadow-xl">
                <CardContent className="p-6 text-center">
                  <h3 className="text-lg font-semibold mb-2">
                    Want to access all lessons?
                  </h3>
                  <p className="text-blue-100 mb-4 text-sm">
                    Enroll in this course to unlock all lessons and features.
                  </p>
                  <Button
                    onClick={() => router.push(`/courses/${lesson.course.slug}`)}
                    className="w-full bg-white text-blue-600 hover:bg-gray-100"
                  >
                    View Course Details
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
