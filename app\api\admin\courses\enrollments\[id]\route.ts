import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateEnrollmentSchema = z.object({
  status: z.enum(['active', 'completed', 'cancelled']).optional(),
  progress: z.number().min(0).max(100).optional(),
  notes: z.string().optional()
})

// GET /api/admin/courses/enrollments/[id] - Get single enrollment with details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const enrollmentId = resolvedParams?.id as string

      if (!enrollmentId) {
        return APIResponse.error('Enrollment ID is required', 400)
      }

      const enrollment = await prisma.courseEnrollment.findUnique({
        where: { id: enrollmentId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              createdAt: true
            }
          },
          course: {
            select: {
              id: true,
              title: true,
              description: true,
              thumbnailImage: true,
              price: true,
              duration: true,
              level: true,
              category: true,
              instructor: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            }
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('Enrollment not found', 404)
      }

      // Get lesson progress for this enrollment
      const lessonProgress = await prisma.courseProgress.findMany({
        where: {
          userId: enrollment.userId,
          lesson: {
            chapter: {
              section: {
                courseId: enrollment.courseId
              }
            }
          }
        },
        include: {
          lesson: {
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              chapter: {
                select: {
                  title: true,
                  section: {
                    select: {
                      title: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          lastAccessAt: 'desc'
        }
      })

      // Calculate detailed progress statistics
      const totalLessons = await prisma.courseLesson.count({
        where: {
          chapter: {
            section: {
              courseId: enrollment.courseId
            }
          },
          isPublished: true
        }
      })

      const completedLessons = lessonProgress.filter(p => p.isCompleted).length
      const totalWatchTime = lessonProgress.reduce((sum, p) => sum + p.watchTime, 0)

      return APIResponse.success({
        enrollment: {
          ...enrollment,
          progressDetails: {
            totalLessons,
            completedLessons,
            completionPercentage: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0,
            totalWatchTime: Math.round(totalWatchTime / 60), // Convert to minutes
            lastActivity: lessonProgress[0]?.lastAccessAt || enrollment.enrolledAt
          },
          lessonProgress: lessonProgress.slice(0, 10) // Latest 10 lessons
        }
      })
    } catch (error) {
      console.error('Error fetching enrollment:', error)
      return APIResponse.error('Failed to fetch enrollment', 500)
    }
  }
)

// PUT /api/admin/courses/enrollments/[id] - Update enrollment
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateEnrollmentSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const enrollmentId = resolvedParams?.id as string

      if (!enrollmentId) {
        return APIResponse.error('Enrollment ID is required', 400)
      }

      const updateData: any = {}
      
      if (validatedBody.status !== undefined) {
        updateData.status = validatedBody.status
        
        // Set completion date if status is completed
        if (validatedBody.status === 'completed') {
          updateData.completedAt = new Date()
          updateData.progress = 100
        }
      }
      
      if (validatedBody.progress !== undefined) {
        updateData.progress = validatedBody.progress
      }

      const enrollment = await prisma.courseEnrollment.update({
        where: { id: enrollmentId },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              price: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Enrollment updated successfully',
        enrollment
      })
    } catch (error) {
      console.error('Error updating enrollment:', error)
      return APIResponse.error('Failed to update enrollment', 500)
    }
  }
)

// DELETE /api/admin/courses/enrollments/[id] - Cancel enrollment
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const enrollmentId = resolvedParams?.id as string

      if (!enrollmentId) {
        return APIResponse.error('Enrollment ID is required', 400)
      }

      // Update enrollment status to cancelled instead of deleting
      const enrollment = await prisma.courseEnrollment.update({
        where: { id: enrollmentId },
        data: {
          status: 'cancelled'
        }
      })

      // Update course student count
      await prisma.course.update({
        where: { id: enrollment.courseId },
        data: {
          studentsCount: {
            decrement: 1
          }
        }
      })

      return APIResponse.success({
        message: 'Enrollment cancelled successfully'
      })
    } catch (error) {
      console.error('Error cancelling enrollment:', error)
      return APIResponse.error('Failed to cancel enrollment', 500)
    }
  }
)
