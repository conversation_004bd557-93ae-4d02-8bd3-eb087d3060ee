import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const videoSessionSchema = z.object({
  sessionId: z.string(),
  lessonId: z.string(),
  userId: z.string(),
  startTime: z.number(),
  endTime: z.number().optional(),
  totalWatchTime: z.number(),
  completionPercentage: z.number(),
  qualityChanges: z.number(),
  bufferEvents: z.number(),
  averageQuality: z.string(),
  deviceInfo: z.object({
    userAgent: z.string(),
    screenResolution: z.string(),
    connection: z.string().optional()
  }),
  eventCount: z.number()
})

// POST /api/analytics/video-sessions - Store video session analytics
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: videoSessionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      // Verify the lesson exists and user has access
      const lesson = await prisma.courseLesson.findFirst({
        where: {
          id: validatedBody.lessonId,
          chapter: {
            section: {
              course: {
                enrollments: {
                  some: {
                    userId: user.id,
                    status: 'ACTIVE'
                  }
                }
              }
            }
          }
        },
        include: {
          chapter: {
            include: {
              section: {
                include: {
                  course: true
                }
              }
            }
          }
        }
      })

      if (!lesson) {
        return APIResponse.error('Lesson not found or access denied', 404)
      }

      // Store video session analytics
      const videoSession = await prisma.videoAnalytics.create({
        data: {
          sessionId: validatedBody.sessionId,
          lessonId: validatedBody.lessonId,
          userId: validatedBody.userId,
          courseId: lesson.chapter.section.course.id,
          startTime: new Date(validatedBody.startTime),
          endTime: validatedBody.endTime ? new Date(validatedBody.endTime) : null,
          totalWatchTime: validatedBody.totalWatchTime,
          completionPercentage: validatedBody.completionPercentage,
          qualityChanges: validatedBody.qualityChanges,
          bufferEvents: validatedBody.bufferEvents,
          averageQuality: validatedBody.averageQuality,
          deviceInfo: validatedBody.deviceInfo,
          eventCount: validatedBody.eventCount
        }
      })

      return APIResponse.success({
        message: 'Video session analytics stored successfully',
        sessionId: videoSession.sessionId
      })
    } catch (error) {
      console.error('Error storing video session analytics:', error)
      return APIResponse.error('Failed to store video session analytics', 500)
    }
  }
)

// GET /api/analytics/video-sessions - Get video session analytics (admin only)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const { searchParams } = new URL(request.url)
      const courseId = searchParams.get('courseId')
      const lessonId = searchParams.get('lessonId')
      const userId = searchParams.get('userId')
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '50')

      // Build where clause
      const where: any = {}
      
      if (courseId) where.courseId = courseId
      if (lessonId) where.lessonId = lessonId
      if (userId) where.userId = userId
      
      if (startDate || endDate) {
        where.startTime = {}
        if (startDate) where.startTime.gte = new Date(startDate)
        if (endDate) where.startTime.lte = new Date(endDate)
      }

      // Get total count
      const total = await prisma.videoAnalytics.count({ where })

      // Get sessions with pagination
      const sessions = await prisma.videoAnalytics.findMany({
        where,
        include: {
          lesson: {
            select: {
              id: true,
              title: true,
              type: true,
              duration: true
            }
          },
          course: {
            select: {
              id: true,
              title: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        },
        orderBy: { startTime: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      })

      // Calculate aggregate statistics
      const aggregateStats = await prisma.videoAnalytics.aggregate({
        where,
        _avg: {
          totalWatchTime: true,
          completionPercentage: true,
          qualityChanges: true,
          bufferEvents: true
        },
        _sum: {
          totalWatchTime: true,
          eventCount: true
        },
        _count: {
          sessionId: true
        }
      })

      return APIResponse.success({
        sessions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        statistics: {
          totalSessions: aggregateStats._count.sessionId,
          averageWatchTime: aggregateStats._avg.totalWatchTime || 0,
          averageCompletion: aggregateStats._avg.completionPercentage || 0,
          totalWatchTime: aggregateStats._sum.totalWatchTime || 0,
          totalEvents: aggregateStats._sum.eventCount || 0,
          averageQualityChanges: aggregateStats._avg.qualityChanges || 0,
          averageBufferEvents: aggregateStats._avg.bufferEvents || 0
        }
      })
    } catch (error) {
      console.error('Error fetching video session analytics:', error)
      return APIResponse.error('Failed to fetch video session analytics', 500)
    }
  }
)
