'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TrophyIcon,
  StarIcon,
  FireIcon,
  BellIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { toast } from 'react-hot-toast'

interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  rarity: string
  points: number
  progress: number
  unlockedAt?: string
}

interface Notification {
  id: string
  type: string
  title: string
  message: string
  data: Record<string, any>
  priority: string
  isRead: boolean
  createdAt: string
}

interface MissionAchievementsProps {
  userId: string
  onAchievementUnlocked?: (achievement: Achievement) => void
}

export default function MissionAchievements({ 
  userId, 
  onAchievementUnlocked 
}: MissionAchievementsProps) {
  const [achievements, setAchievements] = useState<{
    earned: Achievement[]
    available: Achievement[]
  }>({ earned: [], available: [] })
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [stats, setStats] = useState<any>({})
  const [loading, setLoading] = useState(true)
  const [showNotifications, setShowNotifications] = useState(false)

  useEffect(() => {
    fetchAchievements()
    fetchNotifications()
  }, [userId])

  const fetchAchievements = async () => {
    try {
      const response = await fetch('/api/student/mission-achievements')
      if (response.ok) {
        const data = await response.json()
        setAchievements(data.data.achievements)
        setStats(data.data.stats)
      }
    } catch (error) {
      console.error('Error fetching achievements:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/student/notifications')
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.data.notifications)
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
    }
  }

  const checkNewAchievements = async () => {
    try {
      const response = await fetch('/api/student/mission-achievements/check', {
        method: 'POST'
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.data.newAchievements.length > 0) {
          // Show achievement notifications
          data.data.newAchievements.forEach((achievement: Achievement) => {
            toast.success(`🏆 Achievement Unlocked: ${achievement.title}!`, {
              duration: 5000
            })
            onAchievementUnlocked?.(achievement)
          })
          
          // Refresh achievements
          fetchAchievements()
        }
      }
    } catch (error) {
      console.error('Error checking achievements:', error)
    }
  }

  const markNotificationsAsRead = async (notificationIds: string[]) => {
    try {
      const response = await fetch('/api/student/notifications/mark-read', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationIds })
      })
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => 
            notificationIds.includes(n.id) ? { ...n, isRead: true } : n
          )
        )
      }
    } catch (error) {
      console.error('Error marking notifications as read:', error)
    }
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'from-yellow-400 to-orange-500'
      case 'epic': return 'from-purple-400 to-pink-500'
      case 'rare': return 'from-blue-400 to-indigo-500'
      case 'uncommon': return 'from-green-400 to-emerald-500'
      case 'common': return 'from-gray-400 to-gray-500'
      default: return 'from-gray-400 to-gray-500'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500 bg-red-50'
      case 'normal': return 'border-blue-500 bg-blue-50'
      case 'low': return 'border-gray-500 bg-gray-50'
      default: return 'border-gray-500 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Notifications */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Mission Achievements</h2>
        
        <div className="flex items-center space-x-4">
          <Button onClick={checkNewAchievements} variant="outline" size="sm">
            Check New Achievements
          </Button>
          
          <div className="relative">
            <Button
              onClick={() => setShowNotifications(!showNotifications)}
              variant="outline"
              size="sm"
              className="relative"
            >
              <BellIcon className="w-4 h-4" />
              {notifications.filter(n => !n.isRead).length > 0 && (
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
                  {notifications.filter(n => !n.isRead).length}
                </span>
              )}
            </Button>
            
            {/* Notifications Dropdown */}
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-96 overflow-y-auto"
                >
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900">Notifications</h3>
                      <Button
                        onClick={() => setShowNotifications(false)}
                        variant="ghost"
                        size="sm"
                      >
                        <XMarkIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="max-h-64 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        No notifications yet
                      </div>
                    ) : (
                      notifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-3 border-b border-gray-100 last:border-b-0 ${
                            !notification.isRead ? 'bg-blue-50' : ''
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm text-gray-900">
                                {notification.title}
                              </h4>
                              <p className="text-xs text-gray-600 mt-1">
                                {notification.message}
                              </p>
                              <p className="text-xs text-gray-400 mt-1">
                                {new Date(notification.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                            
                            {!notification.isRead && (
                              <Button
                                onClick={() => markNotificationsAsRead([notification.id])}
                                variant="ghost"
                                size="sm"
                              >
                                <CheckCircleIcon className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                  
                  {notifications.filter(n => !n.isRead).length > 0 && (
                    <div className="p-3 border-t border-gray-200">
                      <Button
                        onClick={() => markNotificationsAsRead(
                          notifications.filter(n => !n.isRead).map(n => n.id)
                        )}
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        Mark All as Read
                      </Button>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrophyIcon className="w-5 h-5 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-600">Achievements</p>
                <p className="text-xl font-bold">{achievements.earned.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <StarIcon className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Total Points</p>
                <p className="text-xl font-bold">{stats.totalPointsEarned || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FireIcon className="w-5 h-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Missions</p>
                <p className="text-xl font-bold">{stats.completedMissions || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircleIcon className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Streak</p>
                <p className="text-xl font-bold">{stats.consecutiveDays || 0} days</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Earned Achievements */}
      <Card>
        <CardHeader>
          <CardTitle>Earned Achievements ({achievements.earned.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {achievements.earned.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No achievements earned yet. Complete missions to unlock achievements!
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.earned.map((achievement) => (
                <motion.div
                  key={achievement.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className={`p-4 rounded-lg bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{achievement.icon}</span>
                    <div className="flex-1">
                      <h3 className="font-semibold">{achievement.title}</h3>
                      <p className="text-sm opacity-90">{achievement.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {achievement.rarity}
                        </Badge>
                        <span className="text-sm font-medium">+{achievement.points} pts</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Achievements */}
      <Card>
        <CardHeader>
          <CardTitle>Available Achievements ({achievements.available.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {achievements.available.map((achievement) => (
              <div
                key={achievement.id}
                className="p-4 rounded-lg border border-gray-200 bg-gray-50"
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl opacity-50">{achievement.icon}</span>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{achievement.title}</h3>
                    <p className="text-sm text-gray-600">{achievement.description}</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">
                        {achievement.rarity}
                      </Badge>
                      <span className="text-sm text-gray-500">+{achievement.points} pts</span>
                    </div>
                    {achievement.progress > 0 && achievement.progress < 100 && (
                      <div className="mt-2">
                        <Progress value={achievement.progress} className="h-2" />
                        <p className="text-xs text-gray-500 mt-1">
                          {Math.round(achievement.progress)}% complete
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
