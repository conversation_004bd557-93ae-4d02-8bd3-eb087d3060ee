interface VideoAnalyticsEvent {
  type: 'play' | 'pause' | 'seek' | 'quality_change' | 'speed_change' | 'complete' | 'buffer' | 'error'
  timestamp: number
  currentTime: number
  duration: number
  quality?: string
  speed?: number
  bufferDuration?: number
  errorMessage?: string
}

interface VideoSession {
  sessionId: string
  lessonId: string
  userId: string
  startTime: number
  endTime?: number
  totalWatchTime: number
  completionPercentage: number
  events: VideoAnalyticsEvent[]
  qualityChanges: number
  bufferEvents: number
  averageQuality: string
  deviceInfo: {
    userAgent: string
    screenResolution: string
    connection?: string
  }
}

class VideoAnalytics {
  private sessions: Map<string, VideoSession> = new Map()
  private currentSession: VideoSession | null = null
  private watchTimeInterval: NodeJS.Timeout | null = null
  private lastUpdateTime: number = 0

  /**
   * Start a new video analytics session
   */
  startSession(lessonId: string, userId: string): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const session: VideoSession = {
      sessionId,
      lessonId,
      userId,
      startTime: Date.now(),
      totalWatchTime: 0,
      completionPercentage: 0,
      events: [],
      qualityChanges: 0,
      bufferEvents: 0,
      averageQuality: 'auto',
      deviceInfo: {
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
        screenResolution: typeof window !== 'undefined' 
          ? `${window.screen.width}x${window.screen.height}` 
          : '',
        connection: typeof navigator !== 'undefined' && 'connection' in navigator 
          ? (navigator as any).connection?.effectiveType 
          : undefined
      }
    }

    this.sessions.set(sessionId, session)
    this.currentSession = session
    this.lastUpdateTime = Date.now()

    // Start tracking watch time
    this.startWatchTimeTracking()

    return sessionId
  }

  /**
   * End the current session
   */
  endSession(): void {
    if (this.currentSession) {
      this.currentSession.endTime = Date.now()
      this.stopWatchTimeTracking()
      
      // Send session data to server
      this.sendSessionData(this.currentSession)
      
      this.currentSession = null
    }
  }

  /**
   * Track a video event
   */
  trackEvent(
    type: VideoAnalyticsEvent['type'],
    currentTime: number,
    duration: number,
    additionalData?: Partial<VideoAnalyticsEvent>
  ): void {
    if (!this.currentSession) return

    const event: VideoAnalyticsEvent = {
      type,
      timestamp: Date.now(),
      currentTime,
      duration,
      ...additionalData
    }

    this.currentSession.events.push(event)

    // Update session statistics
    this.updateSessionStats(event)

    // Send real-time events for important actions
    if (['complete', 'error'].includes(type)) {
      this.sendEventData(this.currentSession.sessionId, event)
    }
  }

  /**
   * Update watch time and completion percentage
   */
  updateProgress(currentTime: number, duration: number): void {
    if (!this.currentSession) return

    const now = Date.now()
    const timeDiff = now - this.lastUpdateTime

    // Only count as watch time if less than 2 seconds have passed (to avoid counting pauses)
    if (timeDiff < 2000) {
      this.currentSession.totalWatchTime += timeDiff
    }

    this.currentSession.completionPercentage = duration > 0 ? (currentTime / duration) * 100 : 0
    this.lastUpdateTime = now

    // Send progress update every 30 seconds
    if (this.currentSession.totalWatchTime % 30000 < 1000) {
      this.sendProgressUpdate()
    }
  }

  /**
   * Track quality change
   */
  trackQualityChange(newQuality: string, currentTime: number, duration: number): void {
    if (!this.currentSession) return

    this.currentSession.qualityChanges++
    this.trackEvent('quality_change', currentTime, duration, { quality: newQuality })
  }

  /**
   * Track playback speed change
   */
  trackSpeedChange(newSpeed: number, currentTime: number, duration: number): void {
    if (!this.currentSession) return

    this.trackEvent('speed_change', currentTime, duration, { speed: newSpeed })
  }

  /**
   * Track buffer event
   */
  trackBuffer(currentTime: number, duration: number, bufferDuration?: number): void {
    if (!this.currentSession) return

    this.currentSession.bufferEvents++
    this.trackEvent('buffer', currentTime, duration, { bufferDuration })
  }

  /**
   * Get current session statistics
   */
  getSessionStats(): Partial<VideoSession> | null {
    if (!this.currentSession) return null

    return {
      sessionId: this.currentSession.sessionId,
      totalWatchTime: this.currentSession.totalWatchTime,
      completionPercentage: this.currentSession.completionPercentage,
      qualityChanges: this.currentSession.qualityChanges,
      bufferEvents: this.currentSession.bufferEvents,
      averageQuality: this.currentSession.averageQuality
    }
  }

  /**
   * Start tracking watch time
   */
  private startWatchTimeTracking(): void {
    this.watchTimeInterval = setInterval(() => {
      if (this.currentSession) {
        const now = Date.now()
        const timeDiff = now - this.lastUpdateTime
        
        // Only count if video is likely playing (small time diff)
        if (timeDiff < 1500) {
          this.currentSession.totalWatchTime += 1000 // Add 1 second
        }
        
        this.lastUpdateTime = now
      }
    }, 1000)
  }

  /**
   * Stop tracking watch time
   */
  private stopWatchTimeTracking(): void {
    if (this.watchTimeInterval) {
      clearInterval(this.watchTimeInterval)
      this.watchTimeInterval = null
    }
  }

  /**
   * Update session statistics based on events
   */
  private updateSessionStats(event: VideoAnalyticsEvent): void {
    if (!this.currentSession) return

    // Update average quality (simplified)
    if (event.quality) {
      this.currentSession.averageQuality = event.quality
    }
  }

  /**
   * Send session data to server
   */
  private async sendSessionData(session: VideoSession): Promise<void> {
    try {
      await fetch('/api/analytics/video-sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: session.sessionId,
          lessonId: session.lessonId,
          userId: session.userId,
          startTime: session.startTime,
          endTime: session.endTime,
          totalWatchTime: session.totalWatchTime,
          completionPercentage: session.completionPercentage,
          qualityChanges: session.qualityChanges,
          bufferEvents: session.bufferEvents,
          averageQuality: session.averageQuality,
          deviceInfo: session.deviceInfo,
          eventCount: session.events.length
        })
      })
    } catch (error) {
      console.error('Failed to send video analytics session:', error)
    }
  }

  /**
   * Send individual event data
   */
  private async sendEventData(sessionId: string, event: VideoAnalyticsEvent): Promise<void> {
    try {
      await fetch('/api/analytics/video-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId,
          event
        })
      })
    } catch (error) {
      console.error('Failed to send video analytics event:', error)
    }
  }

  /**
   * Send progress update
   */
  private async sendProgressUpdate(): Promise<void> {
    if (!this.currentSession) return

    try {
      await fetch('/api/analytics/video-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionId: this.currentSession.sessionId,
          lessonId: this.currentSession.lessonId,
          totalWatchTime: this.currentSession.totalWatchTime,
          completionPercentage: this.currentSession.completionPercentage,
          timestamp: Date.now()
        })
      })
    } catch (error) {
      console.error('Failed to send video progress update:', error)
    }
  }
}

// Global instance
let videoAnalytics: VideoAnalytics | null = null

export function getVideoAnalytics(): VideoAnalytics {
  if (!videoAnalytics) {
    videoAnalytics = new VideoAnalytics()
  }
  return videoAnalytics
}

export type { VideoAnalyticsEvent, VideoSession }
