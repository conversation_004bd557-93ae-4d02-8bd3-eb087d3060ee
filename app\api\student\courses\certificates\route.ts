import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { getBunnyStorage } from '@/lib/bunny-storage'
// Removed Puppeteer-based PDF generation in favor of React-PDF
import { z } from 'zod'

const generateCertificateSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

// GET /api/student/courses/certificates - Get user's certificates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const url = new URL(request.url)
      const courseId = url.searchParams.get('courseId')

      if (courseId) {
        // Get certificate for specific course
        const certificate = await prisma.courseCertificate.findUnique({
          where: {
            courseId_userId: {
              courseId,
              userId: user.id
            }
          },
          include: {
            course: {
              select: {
                id: true,
                title: true,
                instructor: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        })

        if (!certificate) {
          return APIResponse.error('Certificate not found', 404)
        }

        return APIResponse.success({ certificate })
      }

      // Get all certificates for user
      const certificates = await prisma.courseCertificate.findMany({
        where: { userId: user.id },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: { issuedAt: 'desc' }
      })

      return APIResponse.success({ certificates })
    } catch (error) {
      console.error('Error fetching certificates:', error)
      return APIResponse.error('Failed to fetch certificates', 500)
    }
  }
)

// POST /api/student/courses/certificates - Generate certificate for completed course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: generateCertificateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId } = validatedBody

      // Check if user is enrolled and has completed the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        },
        include: {
          course: {
            include: {
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('You are not enrolled in this course', 404)
      }

      if (enrollment.status !== 'completed' || enrollment.progress < 100) {
        return APIResponse.error('Course must be completed to generate certificate', 400)
      }

      // Check if certificate already exists
      const existingCertificate = await prisma.courseCertificate.findUnique({
        where: {
          courseId_userId: {
            courseId,
            userId: user.id
          }
        }
      })

      // If certificate exists, return it (don't regenerate)
      if (existingCertificate) {
                 return APIResponse.success({
          message: 'Certificate already exists',
          certificate: existingCertificate
        })
      }

      // Get user details
      const userDetails = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          name: true,
          email: true
        }
      })

      if (!userDetails) {
        return APIResponse.error('User not found', 404)
      }

      // Calculate final score (average of all quiz attempts)
      const quizAttempts = await prisma.courseQuizAttempt.findMany({
        where: {
          userId: user.id,
          quiz: {
            courseId
          }
        },
        select: {
          percentage: true
        }
      })

      const finalScore = quizAttempts.length > 0
        ? Math.round(quizAttempts.reduce((acc, attempt) => acc + attempt.percentage, 0) / quizAttempts.length)
        : null

      // Generate unique certificate ID
      const certificateId = `CERT-${courseId.slice(-8).toUpperCase()}-${user.id.slice(-8).toUpperCase()}-${Date.now().toString(36).toUpperCase()}`

      // Generate certificate PDF (this would typically use a PDF generation library)
      const certificateData = {
        certificateId,
        studentName: userDetails.name,
        courseName: enrollment.course.title,
        instructorName: enrollment.course.instructor.name,
        completionDate: enrollment.completedAt!,
        issuedDate: new Date(),
        finalScore
      }

      // Generate modern certificate PDF and upload to Bunny CDN
      let pdfUrl: string
      try {
        pdfUrl = await generateModernCertificatePDF({
          ...certificateData,
          studentName: certificateData.studentName || 'Student',
          instructorName: certificateData.instructorName || 'Instructor'
        })
               } catch (pdfError) {
        console.error('PDF generation failed, using fallback:', pdfError)
        // Fallback: Create a simple certificate URL that redirects to a certificate view
        pdfUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/student/courses/certificates/${certificateId}/view`
      }

      // Create certificate record
      const certificate = await prisma.courseCertificate.create({
        data: {
          courseId,
          userId: user.id,
          certificateId,
          completionDate: enrollment.completedAt!,
          finalScore,
          pdfUrl
        },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructor: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Certificate generated successfully',
        certificate
      })
    } catch (error) {
      console.error('Error generating certificate:', error)
      return APIResponse.error('Failed to generate certificate', 500)
    }
  }
)



// Helper function to generate certificate PDF using modern template and Puppeteer
async function generateModernCertificatePDF(data: {
  certificateId: string
  studentName: string
  courseName: string
  instructorName: string
  completionDate: Date
  issuedDate: Date
  finalScore: number | null
}): Promise<string> {
  try {
   

    // Generate PDF using React-PDF instead of Puppeteer for better reliability
    const { generateCertificatePDF: generateReactPDF } = await import('@/lib/enhanced-pdf-generator')

    const certificateData = {
      studentName: data.studentName,
      courseName: data.courseName,
      completionDate: data.completionDate.toISOString(),
      score: data.finalScore || 0,
      percentage: data.finalScore || 0,
      certificateId: data.certificateId,
      institutionName: 'PrepLocus',
      instructorName: data.instructorName,
      achievements: data.finalScore && data.finalScore >= 80 ? ['Course Completion'] : []
    }

    const pdfBlob = await generateReactPDF(certificateData)
    const arrayBuffer = await pdfBlob.arrayBuffer()
    const pdfBuffer = Buffer.from(arrayBuffer)

         // Upload to Bunny CDN
    const bunnyStorage = getBunnyStorage()

    // Create filename with proper extension
    const filename = `certificate-${data.certificateId}.pdf`

    // Extract course ID from the certificate ID for folder structure
    const courseIdFromCert = data.certificateId.split('-')[1] // Extract from CERT-COURSEID-USERID-TIMESTAMP format
    const folder = `courses/${courseIdFromCert}/certificates`

         const uploadResult = await bunnyStorage.uploadFile(pdfBuffer, {
      folder,
      filename,
      contentType: 'application/pdf'
    })

    if (!uploadResult.success) {
      console.error('Failed to upload PDF to Bunny CDN:', uploadResult.error)
      throw new Error('Failed to upload certificate PDF')
    }

         return uploadResult.url || ''
  } catch (error) {
    console.error('Error generating certificate PDF:', error)
    throw new Error(`Failed to generate certificate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}
