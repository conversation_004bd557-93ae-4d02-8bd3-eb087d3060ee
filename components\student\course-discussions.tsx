'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  UserCircleIcon,
  EllipsisVerticalIcon,
  HeartIcon,

} from '@heroicons/react/24/outline'
import { HeartIcon as HeartIconSolid } from '@heroicons/react/24/solid'
import { toast } from 'react-hot-toast'
import { ReplyIcon } from 'lucide-react'

interface Discussion {
  id: string
  content: string
  isEdited: boolean
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    image?: string
  }
  replies: Discussion[]
  _count?: {
    likes: number
  }
  isLiked?: boolean
}

interface CourseDiscussionsProps {
  courseId: string
  lessonId?: string
  className?: string
}

export default function CourseDiscussions({ courseId, lessonId, className = '' }: CourseDiscussionsProps) {
  const [discussions, setDiscussions] = useState<Discussion[]>([])
  const [loading, setLoading] = useState(true)
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    fetchDiscussions()
  }, [courseId, lessonId])

  const fetchDiscussions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        courseId,
        ...(lessonId && { lessonId })
      })

      const response = await fetch(`/api/student/courses/discussions?${params}`)
      if (!response.ok) throw new Error('Failed to fetch discussions')

      const data = await response.json()
      setDiscussions(data.discussions)
    } catch (error) {
      console.error('Error fetching discussions:', error)
      toast.error('Failed to load discussions')
    } finally {
      setLoading(false)
    }
  }

  const submitComment = async () => {
    if (!newComment.trim()) return

    try {
      setSubmitting(true)
      const response = await fetch('/api/student/courses/discussions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId,
          content: newComment.trim()
        })
      })

      if (!response.ok) throw new Error('Failed to post comment')

      const data = await response.json()
      setDiscussions(prev => [data.discussion, ...prev])
      setNewComment('')
      toast.success('Comment posted successfully!')
    } catch (error) {
      console.error('Error posting comment:', error)
      toast.error('Failed to post comment')
    } finally {
      setSubmitting(false)
    }
  }

  const submitReply = async (parentId: string) => {
    if (!replyContent.trim()) return

    try {
      setSubmitting(true)
      const response = await fetch('/api/student/courses/discussions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          courseId,
          lessonId,
          parentId,
          content: replyContent.trim()
        })
      })

      if (!response.ok) throw new Error('Failed to post reply')

      const data = await response.json()
      
      // Update discussions with new reply
      setDiscussions(prev => prev.map(discussion => 
        discussion.id === parentId
          ? { ...discussion, replies: [...discussion.replies, data.discussion] }
          : discussion
      ))
      
      setReplyContent('')
      setReplyingTo(null)
      toast.success('Reply posted successfully!')
    } catch (error) {
      console.error('Error posting reply:', error)
      toast.error('Failed to post reply')
    } finally {
      setSubmitting(false)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    
    return date.toLocaleDateString()
  }

  return (
    <div className={`bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <ChatBubbleLeftRightIcon className="w-6 h-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-800">
              {lessonId ? 'Lesson Discussion' : 'Course Discussion'}
            </h3>
            <p className="text-sm text-gray-600">
              {discussions.length} {discussions.length === 1 ? 'comment' : 'comments'}
            </p>
          </div>
        </div>
      </div>

      {/* New Comment Form */}
      <div className="p-6 border-b border-gray-200">
        <div className="space-y-4">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder={lessonId ? "Ask a question about this lesson..." : "Start a discussion about this course..."}
            rows={3}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
          />
          <div className="flex justify-end">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={submitComment}
              disabled={!newComment.trim() || submitting}
              className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
            >
              {submitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <PaperAirplaneIcon className="w-4 h-4 mr-2" />
              )}
              Post Comment
            </motion.button>
          </div>
        </div>
      </div>

      {/* Discussions List */}
      <div className="max-h-96 overflow-y-auto">
        {loading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading discussions...</p>
          </div>
        ) : discussions.length === 0 ? (
          <div className="p-6 text-center">
            <ChatBubbleLeftRightIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-gray-600 mb-2">No discussions yet</h4>
            <p className="text-gray-500">Be the first to start a conversation!</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {discussions.map((discussion) => (
              <DiscussionItem
                key={discussion.id}
                discussion={discussion}
                onReply={(id) => setReplyingTo(id)}
                replyingTo={replyingTo}
                replyContent={replyContent}
                onReplyContentChange={setReplyContent}
                onSubmitReply={submitReply}
                submitting={submitting}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface DiscussionItemProps {
  discussion: Discussion
  onReply: (id: string) => void
  replyingTo: string | null
  replyContent: string
  onReplyContentChange: (content: string) => void
  onSubmitReply: (parentId: string) => void
  submitting: boolean
}

function DiscussionItem({ 
  discussion, 
  onReply, 
  replyingTo, 
  replyContent, 
  onReplyContentChange, 
  onSubmitReply,
  submitting
}: DiscussionItemProps) {
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    
    return date.toLocaleDateString()
  }

  return (
    <div className="p-6">
      {/* Main Comment */}
      <div className="flex space-x-3">
        <div className="flex-shrink-0">
          {discussion.user.image ? (
            <img
              src={discussion.user.image}
              alt={discussion.user.name}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <UserCircleIcon className="w-8 h-8 text-gray-400" />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-medium text-gray-800">{discussion.user.name}</span>
            <span className="text-sm text-gray-500">{formatTimeAgo(discussion.createdAt)}</span>
            {discussion.isEdited && (
              <span className="text-xs text-gray-400">(edited)</span>
            )}
          </div>
          <p className="text-gray-700 mb-3">{discussion.content}</p>
          
          {/* Actions */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => onReply(discussion.id)}
              className="flex items-center text-sm text-gray-500 hover:text-blue-600 transition-colors duration-200"
            >
              <ReplyIcon className="w-4 h-4 mr-1" />
              Reply
            </button>
            {discussion._count && discussion._count.likes > 0 && (
              <span className="flex items-center text-sm text-gray-500">
                <HeartIconSolid className="w-4 h-4 mr-1 text-red-500" />
                {discussion._count.likes}
              </span>
            )}
          </div>

          {/* Reply Form */}
          <AnimatePresence>
            {replyingTo === discussion.id && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 space-y-3"
              >
                <textarea
                  value={replyContent}
                  onChange={(e) => onReplyContentChange(e.target.value)}
                  placeholder="Write a reply..."
                  rows={2}
                  className="w-full px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none text-sm"
                />
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => onSubmitReply(discussion.id)}
                    disabled={!replyContent.trim() || submitting}
                    className="px-3 py-1 bg-blue-600 text-white rounded text-sm font-medium hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50"
                  >
                    {submitting ? 'Posting...' : 'Reply'}
                  </motion.button>
                  <button
                    onClick={() => onReply('')}
                    className="px-3 py-1 text-gray-600 hover:text-gray-800 text-sm transition-colors duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Replies */}
          {discussion.replies.length > 0 && (
            <div className="mt-4 space-y-4">
              {discussion.replies.map((reply) => (
                <div key={reply.id} className="flex space-x-3 pl-4 border-l-2 border-gray-200">
                  <div className="flex-shrink-0">
                    {reply.user.image ? (
                      <img
                        src={reply.user.image}
                        alt={reply.user.name}
                        className="w-6 h-6 rounded-full object-cover"
                      />
                    ) : (
                      <UserCircleIcon className="w-6 h-6 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-gray-800 text-sm">{reply.user.name}</span>
                      <span className="text-xs text-gray-500">{formatTimeAgo(reply.createdAt)}</span>
                    </div>
                    <p className="text-gray-700 text-sm">{reply.content}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
