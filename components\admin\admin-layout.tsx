"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { UserMenu } from "@/components/user-menu"
import { initializeSocket } from "@/lib/socket-client"
import { ScrollArea } from "@/components/ui/scroll-area"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import {
  LayoutDashboard,
  FileText,
  Users,
  BarChart3,
  Settings,
  Plus,
  Menu,
  Search,
  Shield,
  BookOpen,
  Calendar,
  Download,
  Activity,
  Brain,
  Zap,
  Folder,
  Package
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useSystemSettings } from "@/hooks/use-system-settings"

interface AdminLayoutProps {
  children: React.ReactNode
}

const sidebarNavItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
    description: "Overview and analytics"
  },
  {
    title: "Course Management",
    href: "/admin/courses",
    icon: BookOpen,
    description: "Create and manage courses",
    children: [
      { title: "All Courses", href: "/admin/courses" },
      { title: "Create Course", href: "/admin/courses/create" },
      { title: "Categories", href: "/admin/courses/categories" },
      { title: "Enrollments", href: "/admin/courses/enrollments" }
    ]
  },
  {
    title: "Quiz Management",
    href: "/admin/quizzes",
    icon: FileText,
    description: "Create and manage quizzes",
    children: [
      { title: "All Quizzes", href: "/admin/quizzes" },
      { title: "Create Quiz", href: "/admin/quizzes/create" },
      { title: "Test Series", href: "/admin/quizzes/test-series" },
      { title: "Daily Practice", href: "/admin/quizzes/daily-practice" }
    ]
  },
  {
    title: "Quiz Bundles",
    href: "/admin/quiz-bundles",
    icon: Package,
    description: "Create and manage quiz bundles"
  },
  {
    title: "Live Quiz",
    href: "/admin/live-quiz",
    icon: Zap,
    description: "Manage live quiz sessions",
    children: [
      { title: "All Sessions", href: "/admin/live-quiz" },
      { title: "Analytics", href: "/admin/live-quiz/analytics" }
    ]
  },
  {
    title: "User Management",
    href: "/admin/users",
    icon: Users,
    description: "Manage users and permissions"
  },
  {
    title: "Analytics",
    href: "/admin/analytics-dashboard",
    icon: BarChart3,
    description: "Performance insights and reports"
  },
  {
    title: "Real-time Monitor",
    href: "/admin/realtime",
    icon: Activity,
    description: "Live system monitoring and real-time data"
  },
  {
    title: "PDF Exports",
    href: "/admin/pdf-exports",
    icon: Download,
    description: "Manage PDF export jobs and downloads"
  },
  {
    title: "Content Library",
    href: "/admin/content-library",
    icon: Folder,
    description: "Manage uploaded files and content"
  },
  {
    title: "Scheduling",
    href: "/admin/scheduling",
    icon: Calendar,
    description: "Schedule quizzes and manage timings"
  },
  {
    title: "AI Management",
    href: "/admin/ai-settings",
    icon: Brain,
    description: "AI models, agents, and configuration"
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings,
    description: "System configuration"
  }
]

function SidebarNav() {
  const pathname = usePathname()

  return (
    <div className="space-y-2">
      {sidebarNavItems.map((item) => (
        <div key={item.href}>
          <Link
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground transition-colors",
              pathname === item.href ? "bg-accent text-accent-foreground" : "text-muted-foreground"
            )}
          >
            <item.icon className="h-4 w-4" />
            <div className="flex-1">
              <div>{item.title}</div>
              <div className="text-xs text-muted-foreground">{item.description}</div>
            </div>
          </Link>
          {item.children && pathname.startsWith(item.href) && (
            <div className="ml-6 mt-2 space-y-1">
              {item.children.map((child) => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    "block rounded-md px-3 py-1 text-sm hover:bg-accent hover:text-accent-foreground transition-colors",
                    pathname === child.href ? "bg-accent text-accent-foreground" : "text-muted-foreground"
                  )}
                >
                  {child.title}
                </Link>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

function AdminHeader() {
  const { settings } = useSystemSettings()

  return (
    <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center gap-4 px-4 md:px-6">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden">
              <Menu className="h-4 w-4" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80">
            <div className="flex items-center gap-2 px-2 py-4">
              <Shield className="h-6 w-6 text-primary" />
              <span className="text-lg font-semibold">Admin Panel</span>
            </div>
            <ScrollArea className="h-[calc(100vh-8rem)]">
              <SidebarNav />
            </ScrollArea>
          </SheetContent>
        </Sheet>

        {/* Logo for mobile */}
        <div className="flex items-center gap-2 md:hidden">
          {settings.logoUrl ? (
            <img src={settings.logoUrl} alt={settings.companyName} className="w-8 h-8 rounded-lg" />
          ) : (
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Shield className="h-5 w-5 text-white" />
            </div>
          )}
          <span className="font-bold text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Admin
          </span>
        </div>

        <div className="flex-1 hidden md:block">
        </div>

        <div className="flex items-center gap-2">
          <NotificationCenter />

        </div>
      </div>
    </header>
  )
}

function AdminSidebar() {
  const { settings } = useSystemSettings()

  return (
    <div className="hidden md:flex md:w-80 md:flex-col md:fixed md:inset-y-0 z-50">
      <div className="flex flex-col flex-1 min-h-0 border-r bg-muted/10">
        <div className="flex h-16 items-center gap-2 border-b px-6">
          <div className="flex items-center gap-2">
            {settings.logoUrl ? (
              <img src={settings.logoUrl} alt={settings.companyName} className="w-8 h-8 rounded-lg" />
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-white" />
              </div>
            )}
            <div>
              <div className="text-sm font-semibold">Admin Panel</div>
              <div className="text-xs text-muted-foreground">{settings.companyName}</div>
            </div>
          </div>
          <Badge variant="secondary" className="ml-auto">
            ADMIN
          </Badge>
        </div>
        <ScrollArea className="flex-1 px-4 py-4">
          <SidebarNav />
        </ScrollArea>
        <div className="border-t px-4 py-4">
          <UserMenu />
        </div>
      </div>
    </div>
  )
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { data: session } = useSession()

  useEffect(() => {
    if (session?.user) {
      initializeSocket({
        id: session.user.id,
        name: session.user.name || 'Admin',
        email: session.user.email || '',
        role: session.user.role || 'ADMIN'
      })
    }
  }, [session])

  return (
    <div className="min-h-screen bg-background">
      <AdminSidebar />
      <div className="md:pl-80">
        <AdminHeader />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
