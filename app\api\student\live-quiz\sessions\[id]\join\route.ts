import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
// Use server-side socket manager, not client

// POST /api/student/live-quiz/sessions/[id]/join - Join live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
             // Get session details
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              isPublished: true,
              questions: {
                select: { id: true }
              }
            }
          },
          _count: {
            select: {
              participants: {
                where: { isActive: true }
              }
            }
          }
        }
      })

             if (!session) {
                 return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Validate session can be joined
      if (!session.quiz.isPublished) {
        return APIResponse.error('Quiz is not available', 403, 'QUIZ_NOT_PUBLISHED')
      }

      if (session.status === 'COMPLETED' || session.status === 'CANCELLED') {
        return APIResponse.error('Cannot join completed or cancelled session', 400, 'SESSION_ENDED')
      }

      if (session.status === 'ACTIVE' && !session.allowLateJoin) {
        return APIResponse.error('Late joining is not allowed for this session', 400, 'LATE_JOIN_NOT_ALLOWED')
      }

      if (session.status === 'PAUSED') {
        return APIResponse.error('Cannot join paused session', 400, 'SESSION_PAUSED')
      }

      // Check participant limit
      const currentParticipantCount = session._count.participants
      if (session.maxParticipants && currentParticipantCount >= session.maxParticipants) {
        return APIResponse.error('Session is full', 400, 'SESSION_FULL')
      }

      // Check if user is already participating
      const existingParticipation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId,
            userId: user.id
          }
        }
      })

      if (existingParticipation?.isActive) {
        return APIResponse.error('Already participating in this session', 400, 'ALREADY_PARTICIPATING')
      }

      // Create or reactivate participation
      let participation
      if (existingParticipation) {
        // Reactivate existing participation
        participation = await prisma.liveQuizParticipant.update({
          where: { id: existingParticipation.id },
          data: {
            isActive: true,
            joinedAt: new Date(),
            leftAt: null,
            currentQuestion: session.currentQuestion, // Start from current question if late join
            score: 0,
            correctAnswers: 0,
            totalAnswered: 0,
            rank: null,
            answers: {},
            timeSpent: 0
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })
      } else {
        // Create new participation
        participation = await prisma.liveQuizParticipant.create({
          data: {
            sessionId,
            userId: user.id,
            currentQuestion: session.currentQuestion, // Start from current question if late join
            score: 0,
            correctAnswers: 0,
            totalAnswered: 0,
            answers: {},
            timeSpent: 0
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        })
      }

      // Get current question if session is active
      let currentQuestionData = null
      if (session.status === 'ACTIVE') {
        currentQuestionData = await prisma.question.findFirst({
          where: {
            quizId: session.quizId,
            order: session.currentQuestion
          },
          select: {
            id: true,
            type: true,
            text: true,
            options: true,
            points: true,
            order: true,
            image: true
          }
        })
      }

      // Broadcast participant joined event
      try {
        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()

        // Notify session room about new participant
        socketManager?.broadcastToRoom(`live-quiz:${sessionId}`, 'live-quiz:participant-joined', {
          sessionId,
          participant: {
            id: participation.id,
            userId: participation.userId,
            userName: participation.user.name,
            joinedAt: participation.joinedAt,
            score: participation.score,
            rank: participation.rank
          },
          session: {
            id: session.id,
            title: session.title,
            participantCount: currentParticipantCount + 1
          }
        })

        // Send welcome notification to the new participant (direct)
        socketManager?.broadcastToRoom(`user:${user.id}`, 'notification:received', {
          id: Math.random().toString(36).substring(7),
          type: 'success',
          title: 'Joined Successfully!',
          message: `Welcome to ${session.title}. ${session.status === 'ACTIVE' ? 'The quiz is in progress.' : 'Waiting for the quiz to start.'}`,
          data: {
            sessionId,
            action: 'joined-session'
          },
          createdAt: new Date()
        })

        // If session is active, send current question to the user
        if (session.status === 'ACTIVE' && currentQuestionData) {
          socketManager?.broadcastToRoom(`user:${user.id}`, 'live-quiz:current-question', {
            userId: user.id,
            sessionId,
            questionData: currentQuestionData,
            questionIndex: session.currentQuestion,
            timeLimit: session.questionTimeLimit
          })
        }

      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        participation: {
          id: participation.id,
          sessionId: participation.sessionId,
          userId: participation.userId,
          joinedAt: participation.joinedAt,
          currentQuestion: participation.currentQuestion,
          score: participation.score,
          correctAnswers: participation.correctAnswers,
          totalAnswered: participation.totalAnswered,
          isActive: participation.isActive,
          rank: participation.rank,
          timeSpent: participation.timeSpent
        },
        session: {
          id: session.id,
          title: session.title,
          status: session.status,
          currentQuestion: session.currentQuestion,
          questionTimeLimit: session.questionTimeLimit,
          showLeaderboard: session.showLeaderboard
        },
        currentQuestionData,
        quiz: {
          id: session.quiz.id,
          title: session.quiz.title,
          questionCount: session.quiz.questions.length
        }
      }, 'Successfully joined live quiz session')

    } catch (error) {
      console.error('Error joining live quiz session:', error)
      return APIResponse.error('Failed to join live quiz session', 500)
    }
  }
)
