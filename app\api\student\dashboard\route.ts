import { NextRequest } from 'next/server'
 import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/dashboard - Get student dashboard data
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get user stats
      const userStats = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          totalQuizzes: true,
          totalPoints: true,
          averageScore: true,
          streak: true
        }
      })

      // Get recent quiz attempts
      const recentAttempts = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          completedAt: { not: null }
        },
        include: {
          quiz: {
            select: {
              title: true,
              type: true,
              difficulty: true,
              points: true,
              _count: {
                select: { questions: true }
              }
            }
          }
        },
        orderBy: { completedAt: 'desc' },
        take: 5
      })

      // Get enrolled quizzes
      const enrolledQuizzes = await prisma.quizEnrollment.findMany({
        where: { userId: user.id },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              type: true,
              difficulty: true,
              thumbnail: true,
              timeLimit: true,
              startTime: true,
              endTime: true,
              _count: {
                select: { questions: true }
              }
            }
          }
        },
        orderBy: { enrolledAt: 'desc' },
        take: 6
      })

      // Get achievements
      const achievements = await prisma.userAchievement.findMany({
        where: { userId: user.id },
        include: {
          achievement: true
        },
        orderBy: { unlockedAt: 'desc' },
        take: 10
      })

      // Get performance analytics
      const performanceData = await prisma.quizAttempt.findMany({
        where: {
          userId: user.id,
          completedAt: { not: null }
        },
        select: {
          percentage: true,
          completedAt: true,
          quiz: {
            select: {
              type: true,
              difficulty: true
            }
          }
        },
        orderBy: { completedAt: 'asc' }
      })

      // Calculate performance trends
      const performanceTrend = calculatePerformanceTrend(performanceData)
      const difficultyBreakdown = calculateDifficultyBreakdown(performanceData)
      const typeBreakdown = calculateTypeBreakdown(performanceData)

      // Get upcoming quizzes (enrolled and within time bounds)
      const upcomingQuizzes = await prisma.quiz.findMany({
        where: {
          isPublished: true,
          enrollments: {
            some: { userId: user.id }
          },
          OR: [
            { startTime: { gt: new Date() } },
            {
              AND: [
                { startTime: { lte: new Date() } },
                { endTime: { gt: new Date() } }
              ]
            }
          ]
        },
        select: {
          id: true,
          title: true,
          type: true,
          difficulty: true,
          startTime: true,
          endTime: true,
          timeLimit: true,
          _count: {
            select: { questions: true }
          }
        },
        orderBy: { startTime: 'asc' },
        take: 5
      })

      // Get leaderboard position
      const leaderboardPosition = await getLeaderboardPosition(user.id)

      // Get total students count
      const totalStudents = await prisma.user.count({
        where: { role: 'STUDENT' }
      })

      // Calculate completed quizzes (all completed attempts, not just recent)
      const allCompletedAttempts = await prisma.quizAttempt.count({
        where: {
          userId: user.id,
          completedAt: { not: null }
        }
      })

      // Calculate real hours spent from actual time spent data
      const totalTimeSpentResult = await prisma.quizAttempt.aggregate({
        where: {
          userId: user.id,
          completedAt: { not: null },
          timeSpent: { not: null }
        },
        _sum: {
          timeSpent: true
        }
      })

      const totalTimeSpentSeconds = totalTimeSpentResult._sum.timeSpent || 0
      const hoursSpent = Math.round(totalTimeSpentSeconds / 3600) // Convert seconds to hours

      // Calculate monthly performance trend
      const monthlyTrend = calculateMonthlyPerformanceTrend(performanceData)

      return APIResponse.success(
        {
          stats: {
            totalQuizzes: userStats?.totalQuizzes || 0,
            completedQuizzes: allCompletedAttempts,
            averageScore: userStats?.averageScore || 0,
            totalPoints: userStats?.totalPoints || 0,
            currentStreak: userStats?.streak || 0,
            rank: leaderboardPosition?.position || 0,
            totalStudents,
            hoursSpent,
            monthlyTrend
          },
          recentAttempts: recentAttempts.map(attempt => {
            // Calculate real max score from quiz questions
            const questionCount = attempt.quiz._count.questions
            const pointsPerQuestion = attempt.quiz.points || 10
            const maxScore = attempt.totalPoints || (questionCount * pointsPerQuestion) || 100
            return {
              id: attempt.id,
              title: attempt.quiz.title,
              type: attempt.quiz.type,
              score: attempt.score,
              maxScore,
              completedAt: attempt.completedAt?.toISOString() || '',
              difficulty: attempt.quiz.difficulty,
              timeSpent: attempt.timeSpent ? Math.round(attempt.timeSpent / 60) : 0 // Convert seconds to minutes
            }
          }),
          enrolledQuizzes: upcomingQuizzes.map(quiz => ({
            id: quiz.id,
            title: quiz.title,
            type: quiz.type,
            difficulty: quiz.difficulty,
            startTime: quiz.startTime?.toISOString() || new Date().toISOString(),
            duration: quiz.timeLimit || 30,
            questionsCount: quiz._count.questions,
            isEnrolled: true
          })),
          achievements: achievements.map(userAchievement => ({
            id: userAchievement.id,
            type: (userAchievement.achievement as any)?.type || 'general',
            title: (userAchievement.achievement as any)?.title || 'Achievement',
            description: (userAchievement.achievement as any)?.description || 'Achievement unlocked',
            unlockedAt: userAchievement.unlockedAt
          })),
          analytics: {
            performanceTrend,
            difficultyBreakdown,
            typeBreakdown
          },
          upcomingQuizzes,
          leaderboardPosition
        },
        'Dashboard data retrieved successfully'
      )

    } catch (error) {
      console.error('Error fetching student dashboard:', error)
      return APIResponse.error('Failed to fetch dashboard data', 500)
    }
  }
)

// Helper functions
function calculatePerformanceTrend(attempts: any[]) {
  if (attempts.length === 0) return []

  // Group by week and calculate average
  const weeklyData = new Map()
  
  attempts.forEach(attempt => {
    const week = getWeekKey(attempt.completedAt)
    if (!weeklyData.has(week)) {
      weeklyData.set(week, { total: 0, count: 0 })
    }
    const data = weeklyData.get(week)
    data.total += attempt.percentage
    data.count += 1
  })

  return Array.from(weeklyData.entries())
    .map(([week, data]) => ({
      week,
      average: Math.round(data.total / data.count)
    }))
    .sort((a, b) => a.week.localeCompare(b.week))
}

function calculateDifficultyBreakdown(attempts: any[]) {
  const breakdown = { EASY: 0, MEDIUM: 0, HARD: 0 }
  
  attempts.forEach(attempt => {
    if (attempt.quiz.difficulty && breakdown.hasOwnProperty(attempt.quiz.difficulty)) {
      breakdown[attempt.quiz.difficulty as keyof typeof breakdown]++
    }
  })

  return Object.entries(breakdown).map(([difficulty, count]) => ({
    difficulty,
    count,
    percentage: attempts.length > 0 ? Math.round((count / attempts.length) * 100) : 0
  }))
}

function calculateTypeBreakdown(attempts: any[]) {
  const breakdown = { QUIZ: 0, TEST_SERIES: 0, DAILY_PRACTICE: 0 }
  
  attempts.forEach(attempt => {
    if (attempt.quiz.type && breakdown.hasOwnProperty(attempt.quiz.type)) {
      breakdown[attempt.quiz.type as keyof typeof breakdown]++
    }
  })

  return Object.entries(breakdown).map(([type, count]) => ({
    type,
    count,
    percentage: attempts.length > 0 ? Math.round((count / attempts.length) * 100) : 0
  }))
}

function getWeekKey(date: Date) {
  const d = new Date(date)
  const year = d.getFullYear()
  const week = Math.ceil((d.getDate() - d.getDay() + 1) / 7)
  return `${year}-W${week.toString().padStart(2, '0')}`
}

function calculateMonthlyPerformanceTrend(attempts: any[]) {
  if (attempts.length === 0) {
    return { change: 0, direction: 'neutral', hasData: false }
  }

  const now = new Date()
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1)

  // Filter attempts by month
  const currentMonthAttempts = attempts.filter(attempt =>
    new Date(attempt.completedAt) >= currentMonth
  )
  const lastMonthAttempts = attempts.filter(attempt => {
    const date = new Date(attempt.completedAt)
    return date >= lastMonth && date < currentMonth
  })

  if (currentMonthAttempts.length === 0 && lastMonthAttempts.length === 0) {
    return { change: 0, direction: 'neutral', hasData: false }
  }

  // Calculate average scores
  const currentAvg = currentMonthAttempts.length > 0
    ? currentMonthAttempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / currentMonthAttempts.length
    : 0

  const lastAvg = lastMonthAttempts.length > 0
    ? lastMonthAttempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / lastMonthAttempts.length
    : 0

  if (lastAvg === 0) {
    return { change: 0, direction: 'neutral', hasData: currentMonthAttempts.length > 0 }
  }

  const change = ((currentAvg - lastAvg) / lastAvg) * 100
  const direction = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'

  return {
    change: Math.abs(Math.round(change * 10) / 10), // Round to 1 decimal place
    direction,
    hasData: true,
    currentAvg: Math.round(currentAvg * 10) / 10,
    lastAvg: Math.round(lastAvg * 10) / 10
  }
}

async function getLeaderboardPosition(userId: string) {
  // Get user's total points
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { totalPoints: true }
  })

  if (!user) return null

  // Count users with more points
  const usersAbove = await prisma.user.count({
    where: {
      totalPoints: { gt: user.totalPoints },
      role: 'STUDENT'
    }
  })

  return {
    position: usersAbove + 1,
    totalPoints: user.totalPoints
  }
}
