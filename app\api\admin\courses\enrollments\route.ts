import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/courses/enrollments - Get all course enrollments
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url)
      const search = searchParams.get('search')
      const status = searchParams.get('status')
      const courseId = searchParams.get('courseId')
      const limit = parseInt(searchParams.get('limit') || '50')
      const offset = parseInt(searchParams.get('offset') || '0')

      // Build where clause
      const where: any = {}

      if (status && status !== 'all') {
        where.status = status
      }

      if (courseId && courseId !== 'all') {
        where.courseId = courseId
      }

      if (search) {
        where.OR = [
          {
            user: {
              name: { contains: search, mode: 'insensitive' }
            }
          },
          {
            user: {
              email: { contains: search, mode: 'insensitive' }
            }
          },
          {
            course: {
              title: { contains: search, mode: 'insensitive' }
            }
          }
        ]
      }

      // Get enrollments with user and course details
      const enrollments = await prisma.courseEnrollment.findMany({
        where,
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              price: true
            }
          }
        },
        orderBy: { enrolledAt: 'desc' }
      })
      
   
      // Get total count for pagination
      const totalCount = await prisma.courseEnrollment.count({ where })

      // Get enrollment statistics
      const stats = await prisma.courseEnrollment.aggregate({
        where,
        _count: {
          id: true
        },
        _avg: {
          progress: true
        }
      })

      // Get status breakdown
      const statusBreakdown = await prisma.courseEnrollment.groupBy({
        by: ['status'],
        where,
        _count: {
          status: true
        }
      })

      // Get recent enrollments (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentEnrollments = await prisma.courseEnrollment.count({
        where: {
          ...where,
          enrolledAt: {
            gte: thirtyDaysAgo
          }
        }
      })

      return APIResponse.success({
        enrollments,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        },
        statistics: {
          total: stats._count.id,
          averageProgress: stats._avg.progress || 0,
          recentEnrollments,
          statusBreakdown: statusBreakdown.reduce((acc, item) => {
            acc[item.status] = item._count.status
            return acc
          }, {} as Record<string, number>)
        }
      })
    } catch (error) {
      console.error('Error fetching enrollments:', error)
      return APIResponse.error('Failed to fetch enrollments', 500)
    }
  }
)
