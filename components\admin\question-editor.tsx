"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  X, 
  Image, 
  Save,
  Trash2,
  Edit,
  CheckCircle
} from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { motion, AnimatePresence } from "framer-motion"

type QuestionType = 'MCQ' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'MATCHING'

interface Question {
  id: string
  type: QuestionType
  text: string
  options: string[]
  correctAnswer: string
  explanation: string
  points: number
  order: number
  image?: string
}

interface QuestionEditorProps {
  question?: Question
  onSave: (question: Question) => void
  onCancel: () => void
}

export function QuestionEditor({ question, onSave, onCancel }: QuestionEditorProps) {
  const defaultQuestion: Question = {
    id: Date.now().toString(),
    type: 'MCQ',
    text: '',
    options: ['', '', '', ''],
    correctAnswer: '',
    explanation: '',
    points: 1,
    order: 1,
    image: undefined
  }

  const [questionData, setQuestionData] = useState<Question>(
    question || defaultQuestion
  )

  const handleTypeChange = (type: QuestionType) => {
    let newOptions: string[] = []
    let newCorrectAnswer = ''

    switch (type) {
      case 'MCQ':
        newOptions = ['', '', '', '']
        break
      case 'TRUE_FALSE':
        newOptions = ['True', 'False']
        newCorrectAnswer = 'True'
        break
      case 'SHORT_ANSWER':
        newOptions = []
        break
      case 'MATCHING':
        newOptions = ['', '', '', '']
        break
    }

    setQuestionData(prev => ({
      ...prev,
      type,
      options: newOptions,
      correctAnswer: newCorrectAnswer
    }))
  }

  const updateOption = (index: number, value: string) => {
    const newOptions = [...questionData.options]
    newOptions[index] = value
    setQuestionData(prev => ({ ...prev, options: newOptions }))
  }

  const addOption = () => {
    if (questionData.options.length < 6) {
      setQuestionData(prev => ({
        ...prev,
        options: [...prev.options, '']
      }))
    }
  }

  const removeOption = (index: number) => {
    if (questionData.options.length > 2) {
      const newOptions = questionData.options.filter((_, i) => i !== index)
      setQuestionData(prev => ({
        ...prev,
        options: newOptions,
        correctAnswer: prev.correctAnswer === prev.options[index] ? '' : prev.correctAnswer
      }))
    }
  }

  const handleSave = () => {
    if (!questionData.text.trim()) {
      return
    }

    if (questionData.type === 'MCQ' || questionData.type === 'TRUE_FALSE') {
      if (!questionData.correctAnswer) {
        return
      }
    }

    onSave(questionData)
  }

  const isValid = () => {
    if (!questionData.text.trim()) return false
    
    if (questionData.type === 'MCQ') {
      return questionData.options.some(opt => opt.trim()) && questionData.correctAnswer
    }
    
    if (questionData.type === 'TRUE_FALSE') {
      return questionData.correctAnswer
    }
    
    return true
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto"
      >
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Edit className="h-6 w-6 text-primary" />
              {question ? 'Edit Question' : 'Add New Question'}
            </h2>
            <p className="text-muted-foreground">Create or modify quiz questions</p>
          </div>
          <Button variant="outline" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="p-6 space-y-6">
          {/* Question Type */}
          <div>
            <Label className="text-base font-semibold">Question Type</Label>
            <RadioGroup
              value={questionData.type}
              onValueChange={handleTypeChange}
              className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="MCQ" id="mcq-type" />
                <Label htmlFor="mcq-type">Multiple Choice</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="TRUE_FALSE" id="tf-type" />
                <Label htmlFor="tf-type">True/False</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="SHORT_ANSWER" id="sa-type" />
                <Label htmlFor="sa-type">Short Answer</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="MATCHING" id="match-type" />
                <Label htmlFor="match-type">Matching</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Question Text */}
          <div>
            <Label htmlFor="question-text">Question Text *</Label>
            <Textarea
              id="question-text"
              value={questionData.text}
              onChange={(e) => setQuestionData(prev => ({ ...prev, text: e.target.value }))}
              placeholder="Enter your question here..."
              className="mt-1 min-h-[100px]"
            />
          </div>

          {/* Question Image */}
          <div>
            <Label>Question Image (Optional)</Label>
            <div className="mt-1 border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center hover:border-muted-foreground/50 transition-colors">
              <input
                type="file"
                accept="image/*"
                className="hidden"
                id="question-image"
                onChange={(e) => {
                  const file = e.target.files?.[0]
                  if (file) {
                    // Handle image upload
                    const reader = new FileReader()
                    reader.onload = (e) => {
                      setQuestionData(prev => ({ ...prev, image: e.target?.result as string }))
                    }
                    reader.readAsDataURL(file)
                  }
                }}
              />
              <label htmlFor="question-image" className="cursor-pointer">
                <Image className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Click to upload an image
                </p>
              </label>
            </div>
            {questionData.image && (
              <div className="mt-2 relative">
                <img
                  src={questionData.image}
                  alt="Question"
                  className="max-w-full h-32 object-contain rounded border"
                />
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-1 right-1"
                  onClick={() => setQuestionData(prev => ({ ...prev, image: undefined }))}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>

          {/* Options */}
          {(questionData.type === 'MCQ' || questionData.type === 'TRUE_FALSE' || questionData.type === 'MATCHING') && (
            <div>
              <Label className="text-base font-semibold">Answer Options</Label>
              <div className="mt-2 space-y-3">
                {questionData.options.map((option, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <RadioGroup
                        value={questionData.correctAnswer}
                        onValueChange={(value) => setQuestionData(prev => ({ ...prev, correctAnswer: value }))}
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem 
                            value={option} 
                            id={`option-${index}`}
                            disabled={!option.trim()}
                          />
                        </div>
                      </RadioGroup>
                      <Label htmlFor={`option-${index}`} className="text-sm font-medium">
                        {String.fromCharCode(65 + index)}.
                      </Label>
                    </div>
                    <Input
                      value={option}
                      onChange={(e) => updateOption(index, e.target.value)}
                      placeholder={`Option ${String.fromCharCode(65 + index)}`}
                      className="flex-1"
                      disabled={questionData.type === 'TRUE_FALSE'}
                    />
                    {questionData.type === 'MCQ' && questionData.options.length > 2 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeOption(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                    {questionData.correctAnswer === option && option.trim() && (
                      <Badge className="bg-green-500">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Correct
                      </Badge>
                    )}
                  </div>
                ))}
                {questionData.type === 'MCQ' && questionData.options.length < 6 && (
                  <Button variant="outline" size="sm" onClick={addOption}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Option
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Short Answer */}
          {questionData.type === 'SHORT_ANSWER' && (
            <div>
              <Label htmlFor="correct-answer">Correct Answer(s)</Label>
              <Textarea
                id="correct-answer"
                value={questionData.correctAnswer}
                onChange={(e) => setQuestionData(prev => ({ ...prev, correctAnswer: e.target.value }))}
                placeholder="Enter the correct answer(s). For multiple acceptable answers, separate with semicolons."
                className="mt-1"
                rows={3}
              />
            </div>
          )}

          {/* Explanation */}
          <div>
            <Label htmlFor="explanation">Explanation (Optional)</Label>
            <Textarea
              id="explanation"
              value={questionData.explanation}
              onChange={(e) => setQuestionData(prev => ({ ...prev, explanation: e.target.value }))}
              placeholder="Provide an explanation for the correct answer..."
              className="mt-1"
              rows={3}
            />
          </div>

          {/* Points */}
          <div>
            <Label htmlFor="points">Points</Label>
            <Input
              id="points"
              type="number"
              min="1"
              max="10"
              value={questionData.points}
              onChange={(e) => setQuestionData(prev => ({ ...prev, points: parseInt(e.target.value) || 1 }))}
              className="mt-1 max-w-24"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center p-6 border-t bg-muted/50">
          <div className="text-sm text-muted-foreground">
            <Badge variant="outline">{questionData.type}</Badge>
            <span className="ml-2">{questionData.points} point{questionData.points !== 1 ? 's' : ''}</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!isValid()}>
              <Save className="h-4 w-4 mr-2" />
              Save Question
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
