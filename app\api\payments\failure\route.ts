import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { razorpayService } from '@/lib/razorpay-service'
import { z } from 'zod'

const paymentFailureSchema = z.object({
  razorpay_order_id: z.string().min(1, 'Order ID is required'),
  error: z.object({
    code: z.string(),
    description: z.string(),
    source: z.string().optional(),
    step: z.string().optional(),
    reason: z.string().optional(),
    metadata: z.record(z.any()).optional()
  })
})

// POST /api/payments/failure - Handle payment failure
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: paymentFailureSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { razorpay_order_id, error } = validatedBody

      // Get payment details
      const payment = await razorpayService.getPaymentByOrderId(razorpay_order_id)
      
      if (!payment) {
        return APIResponse.error('Payment record not found', 404)
      }

      // Verify the payment belongs to the authenticated user
      if (payment.userId !== user.id) {
        return APIResponse.error('Unauthorized access to payment', 403)
      }

      // Format failure reason
      const failureReason = `${error.code}: ${error.description}${
        error.reason ? ` (${error.reason})` : ''
      }`

      // Handle payment failure
      await razorpayService.handlePaymentFailure(razorpay_order_id, failureReason)

      return APIResponse.success({
        message: 'Payment failure recorded',
        error: {
          code: error.code,
          description: error.description,
          reason: error.reason
        },
        course: payment.course ? {
          id: payment.course.id,
          title: payment.course.title,
          slug: payment.course.slug
        } : null
      })

    } catch (error) {
      console.error('Error handling payment failure:', error)
      return APIResponse.error('Failed to process payment failure', 500)
    }
  }
)
