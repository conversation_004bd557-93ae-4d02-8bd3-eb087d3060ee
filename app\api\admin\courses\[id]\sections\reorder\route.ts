import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const reorderSchema = z.object({
  sections: z.array(z.object({
    id: z.string()
  }))
})

export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: reorderSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Update section timestamps to reflect new order
      // We'll update the updatedAt field in sequence to maintain order
      const updatePromises = validatedBody.sections.map((section: any, index: number) => {
        const timestamp = new Date(Date.now() + index * 1000) // Add seconds to maintain order
        return prisma.courseSection.update({
          where: { id: section.id },
          data: { updatedAt: timestamp }
        })
      })

      await Promise.all(updatePromises)

      return APIResponse.success({
        message: 'Sections reordered successfully'
      })
    } catch (error) {
      console.error('Error reordering sections:', error)
      return APIResponse.error('Failed to reorder sections', 500)
    }
  }
)
