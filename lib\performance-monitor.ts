/**
 * Performance Monitoring Utility
 * 
 * Tracks performance metrics for the roadmap feature and provides
 * insights for optimization.
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  metadata?: Record<string, any>
}

interface RoadmapMetrics {
  loadTime: number
  renderTime: number
  missionCount: number
  interactionLatency: number
  memoryUsage?: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    // Observe navigation timing
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming
              this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.startTime)
              this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.startTime)
            }
          }
        })
        navObserver.observe({ entryTypes: ['navigation'] })
        this.observers.push(navObserver)
      } catch (error) {
        // Navigation timing observer not supported
      }

      // Observe resource timing
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name.includes('roadmap') || entry.name.includes('mission')) {
              this.recordMetric('resource_load_time', entry.duration, {
                resource: entry.name,
                type: (entry as any).initiatorType
              })
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })
        this.observers.push(resourceObserver)
      } catch (error) {
        console.warn('Resource timing observer not supported:', error)
      }
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata
    }
    
    this.metrics.push(metric)
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100)
    }
    
    // Log performance issues
    this.checkPerformanceThresholds(metric)
  }

  /**
   * Time a function execution
   */
  time<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    const start = performance.now()
    const result = fn()
    const duration = performance.now() - start
    
    this.recordMetric(name, duration, metadata)
    return result
  }

  /**
   * Time an async function execution
   */
  async timeAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    const start = performance.now()
    const result = await fn()
    const duration = performance.now() - start
    
    this.recordMetric(name, duration, metadata)
    return result
  }

  /**
   * Start a performance mark
   */
  startMark(name: string) {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * End a performance mark and record the duration
   */
  endMark(name: string, metadata?: Record<string, any>) {
    if (typeof window !== 'undefined' && 'performance' in window) {
      try {
        performance.mark(`${name}-end`)
        performance.measure(name, `${name}-start`, `${name}-end`)
        
        const measure = performance.getEntriesByName(name, 'measure')[0]
        if (measure) {
          this.recordMetric(name, measure.duration, metadata)
        }
      } catch (error) {
        // Performance measurement failed
      }
    }
  }

  /**
   * Record roadmap-specific metrics
   */
  recordRoadmapMetrics(metrics: RoadmapMetrics) {
    this.recordMetric('roadmap_load_time', metrics.loadTime)
    this.recordMetric('roadmap_render_time', metrics.renderTime)
    this.recordMetric('roadmap_mission_count', metrics.missionCount)
    this.recordMetric('roadmap_interaction_latency', metrics.interactionLatency)
    
    if (metrics.memoryUsage) {
      this.recordMetric('roadmap_memory_usage', metrics.memoryUsage)
    }
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const summary: Record<string, { avg: number; min: number; max: number; count: number }> = {}
    
    for (const metric of this.metrics) {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          avg: 0,
          min: Infinity,
          max: -Infinity,
          count: 0
        }
      }
      
      const s = summary[metric.name]
      s.count++
      s.min = Math.min(s.min, metric.value)
      s.max = Math.max(s.max, metric.value)
      s.avg = (s.avg * (s.count - 1) + metric.value) / s.count
    }
    
    return summary
  }

  /**
   * Get recent metrics
   */
  getRecentMetrics(minutes: number = 5) {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    return this.metrics.filter(m => m.timestamp > cutoff)
  }

  /**
   * Check performance thresholds and warn about issues
   */
  private checkPerformanceThresholds(metric: PerformanceMetric) {
    const thresholds: Record<string, number> = {
      'roadmap_load_time': 2000, // 2 seconds
      'roadmap_render_time': 500, // 500ms
      'roadmap_interaction_latency': 100, // 100ms
      'mission_progress_update': 200, // 200ms
      'achievement_check': 300, // 300ms
    }
    
    const threshold = thresholds[metric.name]
    if (threshold && metric.value > threshold) {
      // Performance threshold exceeded - could be logged to monitoring service
    }
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics() {
    return {
      metrics: this.metrics,
      summary: this.getSummary(),
      timestamp: Date.now(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    }
  }

  /**
   * Clear all metrics
   */
  clear() {
    this.metrics = []
  }

  /**
   * Cleanup observers
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.clear()
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// React hook for performance monitoring
export function usePerformanceMonitor() {
  const recordMetric = (name: string, value: number, metadata?: Record<string, any>) => {
    performanceMonitor.recordMetric(name, value, metadata)
  }

  const timeFunction = <T>(name: string, fn: () => T, metadata?: Record<string, any>): T => {
    return performanceMonitor.time(name, fn, metadata)
  }

  const timeAsyncFunction = async <T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> => {
    return performanceMonitor.timeAsync(name, fn, metadata)
  }

  const startMark = (name: string) => {
    performanceMonitor.startMark(name)
  }

  const endMark = (name: string, metadata?: Record<string, any>) => {
    performanceMonitor.endMark(name, metadata)
  }

  return {
    recordMetric,
    timeFunction,
    timeAsyncFunction,
    startMark,
    endMark,
    getSummary: () => performanceMonitor.getSummary(),
    exportMetrics: () => performanceMonitor.exportMetrics()
  }
}

// Utility functions for common performance measurements
export const measureRoadmapLoad = (missionCount: number) => {
  performanceMonitor.startMark('roadmap_load')
  
  return {
    end: () => {
      performanceMonitor.endMark('roadmap_load', { missionCount })
    }
  }
}

export const measureMissionInteraction = (missionId: string, action: string) => {
  const start = performance.now()
  
  return {
    end: () => {
      const duration = performance.now() - start
      performanceMonitor.recordMetric('mission_interaction', duration, {
        missionId,
        action
      })
    }
  }
}
