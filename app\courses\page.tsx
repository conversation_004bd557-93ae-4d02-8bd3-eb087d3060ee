'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  Calculator,
  Stethoscope,
  Building,
  GraduationCap,
  Users,
  Clock,
  Star,
  ArrowRight,
  Filter,
  BookOpen,
  ExternalLink,
  Briefcase,
  Laptop,
  Globe,
  DollarSign,
  Search
} from 'lucide-react';
import { AnimatedCard } from '@/components/ui/animated-card';
import { AnimatedButton } from '@/components/ui/animated-button';
import { cn } from '@/lib/utils';

interface Course {
  id: string;
  productId: string;
  title: string;
  description?: string;
  category?: string;
  price: number;
  originalPrice?: number;
  thumbnailImage?: string;
  rating?: number;
  studentsCount?: number;
  duration?: string;
  level?: string;
  slug: string;
  isEnrolled?: boolean;
}

// Icon mapping for categories
const categoryIcons: Record<string, any> = {
  'engineering': Calculator,
  'medical': Stethoscope,
  'government': Building,
  'teaching': Users,
  'business': Briefcase,
  'technology': Laptop,
  'language': Globe,
  'finance': DollarSign,
  'default': BookOpen
};

// Color mapping for categories
const categoryColors: Record<string, string> = {
  'engineering': 'blue',
  'medical': 'pink',
  'government': 'green',
  'teaching': 'orange',
  'business': 'purple',
  'technology': 'indigo',
  'language': 'teal',
  'finance': 'yellow',
  'default': 'violet'
};

export default function PublicCoursesPage() {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [courses, setCourses] = useState<Course[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [userEnrollments, setUserEnrollments] = useState<string[]>([]);
  const { data: session } = useSession();
  const router = useRouter();

  // Fetch categories from public API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true);
        const response = await fetch('/api/public/categories');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.categories && data.categories.length > 0) {
            // Add "All Courses" category at the beginning
            const allCategory = {
              id: 'all',
              name: 'All Courses',
              icon: 'GraduationCap',
              color: 'violet',
              courseCount: data.categories.reduce((sum: number, cat: any) => sum + (cat.courseCount || 0), 0)
            };
            setCategories([allCategory, ...data.categories]);
          }
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        // Set fallback categories
        const fallbackCategories = [
          { id: 'all', name: 'All Courses', icon: 'GraduationCap', color: 'violet', courseCount: 0 },
          { id: 'engineering', name: 'Engineering', icon: 'Calculator', color: 'blue', courseCount: 0 },
          { id: 'medical', name: 'Medical', icon: 'Stethoscope', color: 'pink', courseCount: 0 },
          { id: 'government', name: 'Government', icon: 'Building', color: 'green', courseCount: 0 },
          { id: 'teaching', name: 'Teaching', icon: 'Users', color: 'orange', courseCount: 0 },
        ];
        setCategories(fallbackCategories);
      } finally {
        setCategoriesLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Fetch courses from public API
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/public/courses');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.courses && data.courses.length > 0) {
            setCourses(data.courses);
          }
        }
      } catch (error) {
        console.error('Error fetching courses:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  // Fetch user enrollments if signed in
  useEffect(() => {
    const fetchUserEnrollments = async () => {
      if (!session) {
        setUserEnrollments([]);
        return;
      }

      try {
        const response = await fetch('/api/courses/my-courses?status=all&sync=true');
        if (response.ok) {
          const result = await response.json();
          const data = result.data || result;
          if (data.enrollments) {
            // Extract course IDs from enrollments
            const enrolledCourseIds = data.enrollments.map((enrollment: any) => enrollment.courseId);
            setUserEnrollments(enrolledCourseIds);
          }
        }
      } catch (error) {
        console.error('Error fetching user enrollments:', error);
      }
    };

    fetchUserEnrollments();
  }, [session]);

  const filteredCourses = courses
    .filter(course => {
      const matchesCategory = activeCategory === 'all' || course.category?.toLowerCase() === activeCategory.toLowerCase();
      const matchesSearch = !searchQuery ||
        course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        course.description?.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesCategory && matchesSearch;
    })
    .map(course => ({
      ...course,
      isEnrolled: userEnrollments.includes(course.id)
    }));

  const handleCourseClick = (course: Course) => {
    router.push(`/courses/${course.slug}`);
  };

  const handleEnrollClick = (course: Course) => {
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (course.isEnrolled) {
      // Redirect to course learning page for enrolled users
      router.push(`/student/courses/${course.slug}`);
    } else {
      // Redirect to student courses page to enroll/buy
      router.push('/student/courses');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-violet-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6">
              Explore All Courses
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
              Discover comprehensive courses designed by experts to help you crack any Indian competitive exam
            </p>
            
            {/* Search Bar */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search courses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/30"
              />
            </div>
          </motion.div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categoriesLoading ? (
            Array.from({ length: 5 }).map((_, index) => (
              <div
                key={`category-skeleton-${index}`}
                className="h-12 w-32 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-2xl"
              />
            ))
          ) : (
            categories.map((category, index) => {
              const IconComponent = category.icon === 'GraduationCap' 
                ? GraduationCap 
                : categoryIcons[category.name?.toLowerCase()] || categoryIcons.default;
              
              return (
                <motion.button
                  key={category.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  onClick={() => setActiveCategory(category.id)}
                  className={cn(
                    'flex items-center gap-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300',
                    activeCategory === category.id
                      ? 'bg-gradient-primary text-white shadow-glow'
                      : 'glass hover:shadow-lg text-gray-700 dark:text-gray-300'
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <IconComponent className="w-5 h-5" />
                  {category.name}
                </motion.button>
              );
            })
          )}
        </motion.div>

        {/* Courses Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {isLoading ? (
            Array.from({ length: 9 }).map((_, index) => (
              <div key={`skeleton-${index}`} className="animate-pulse">
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded-t-xl" />
                <div className="p-6 bg-white dark:bg-gray-800 rounded-b-xl">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2" />
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-4" />
                  <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded" />
                </div>
              </div>
            ))
          ) : filteredCourses.length > 0 ? (
            filteredCourses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <AnimatedCard className="h-full overflow-hidden cursor-pointer group" onClick={() => handleCourseClick(course)}>
                  <div className="h-48 bg-gradient-to-br from-violet-100 to-blue-100 dark:from-violet-900/20 dark:to-blue-900/20 relative overflow-hidden">
                    {course.thumbnailImage ? (
                      <img
                        src={course.thumbnailImage}
                        alt={course.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <BookOpen className="w-16 h-16 text-violet-400" />
                      </div>
                    )}
                    <div className="absolute top-4 right-4">
                      <span className="bg-white/90 dark:bg-gray-800/90 text-violet-600 dark:text-violet-400 px-2 py-1 rounded-full text-xs font-medium">
                        {course.category || 'General'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                      {course.title}
                    </h3>
                    
                    {course.description && (
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
                        {course.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        {course.rating && (
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                              {course.rating.toFixed(1)}
                            </span>
                          </div>
                        )}
                        {course.duration && (
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">
                              {course.duration}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-violet-600 dark:text-violet-400">
                          ₹{course.price}
                        </span>
                        {course.originalPrice && course.originalPrice > course.price && (
                          <span className="text-sm text-gray-500 line-through">
                            ₹{course.originalPrice}
                          </span>
                        )}
                      </div>
                      
                      <AnimatedButton
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEnrollClick(course);
                        }}
                        className="group"
                      >
                        {!session
                          ? 'Sign In'
                          : course.isEnrolled
                            ? 'Continue Learning'
                            : 'Buy Now'
                        }
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </AnimatedButton>
                    </div>
                  </div>
                </AnimatedCard>
              </motion.div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                No courses found
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                Try adjusting your search or category filter
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
