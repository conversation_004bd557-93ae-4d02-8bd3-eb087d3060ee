import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { courseFileManager } from '@/lib/course-file-manager'

// POST /api/admin/upload/files - Upload file attachments
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const lessonId = formData.get('lessonId') as string
      const courseId = formData.get('courseId') as string
      const type = formData.get('type') as string || 'attachment'

      if (!file) {
        return APIResponse.error('No file provided', 400)
      }

      if (!courseId) {
        return APIResponse.error('Course ID is required for organized file storage', 400)
      }

      // Validate file size (max 50MB)
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (file.size > maxSize) {
        return APIResponse.error('File too large. Maximum size is 50MB', 400)
      }

      // Allowed file types
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/webm',
        'audio/mpeg',
        'audio/wav',
        'application/zip',
        'application/x-rar-compressed'
      ]

      if (!allowedTypes.includes(file.type)) {
        return APIResponse.error(`File type ${file.type} is not allowed`, 400)
      }

      // Upload file using course file manager
      const uploadResult = await courseFileManager.uploadCourseFile(file, {
        courseId,
        lessonId,
        type: 'file',
        category: type
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload file: ${uploadResult.error}`,
          500
        )
      }

      // Create file object with course context
      const timestamp = Date.now()
      const fileData = {
        id: `file-${timestamp}`,
        url: uploadResult.url!,
        filename: uploadResult.filename!,
        originalName: file.name,
        type: file.type,
        size: uploadResult.size!,
        uploadedAt: new Date().toISOString(),
        lessonId: lessonId || null,
        courseId: courseId,
        attachmentType: type,
        folder: uploadResult.folder!
      }

      return APIResponse.success({
        message: 'File uploaded successfully to course-specific folder',
        ...fileData
      })

    } catch (error) {
      console.error('Error uploading file:', error)
      return APIResponse.error('Failed to upload file', 500)
    }
  }
)
