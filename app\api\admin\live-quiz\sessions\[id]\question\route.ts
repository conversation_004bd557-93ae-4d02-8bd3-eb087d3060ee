import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { liveQuizDelivery } from '@/lib/live-quiz-delivery'

const controlSchema = z.object({
  action: z.enum(['next', 'previous'])
})

// POST /api/admin/live-quiz/sessions/[id]/question - Move to next/previous question
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: controlSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string
    const { action } = validatedBody

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              questions: { select: { id: true, order: true }, orderBy: { order: 'asc' } }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      if (session.status !== 'ACTIVE') {
        return APIResponse.error('Session is not active', 400, 'SESSION_NOT_ACTIVE')
      }

      const totalQuestions = session.quiz.questions.length
      if (totalQuestions === 0) {
        return APIResponse.error('No questions in this quiz', 400, 'NO_QUESTIONS')
      }

      let targetIndex = session.currentQuestion
      if (action === 'next') targetIndex = Math.min(session.currentQuestion + 1, totalQuestions - 1)
      if (action === 'previous') targetIndex = Math.max(session.currentQuestion - 1, 0)

      // If no change, return OK
      if (targetIndex === session.currentQuestion) {
        return APIResponse.success({ session }, 'No question change')
      }

      await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: { currentQuestion: targetIndex }
      })

      // Use delivery service to broadcast the new question coherently
      await liveQuizDelivery.startQuestionDelivery({
        sessionId,
        questionIndex: targetIndex,
        timeLimit: session.questionTimeLimit || undefined,
        autoAdvance: session.autoAdvance
      })

      return APIResponse.success({
        session: { id: sessionId, currentQuestion: targetIndex }
      }, 'Question changed')
    } catch (error) {
      console.error('Error controlling question:', error)
      return APIResponse.error('Failed to control question', 500)
    }
  }
)





// (Removed duplicate extended POST to avoid redeclaration and client socket usage)
