'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  InformationCircleIcon,
  MapIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import RoadmapConfig from './roadmap/roadmap-config'
import { toast } from 'react-hot-toast'

interface CourseFormData {
  title: string
  description: string
  shortDescription: string
  price: number
  originalPrice: number
  category: string
  level: string
  language: string
  duration: string
  thumbnailImage: string
  features: string[]
  tags: string[]
  requirements: string[]
  whatYouLearn: string[]
  isPublished: boolean
}

interface RoadmapConfig {
  hasRoadmap: boolean
  roadmapTitle?: string
  roadmapDescription?: string
  missions: any[]
}

interface CourseEditTabsProps {
  courseId: string
  initialFormData: CourseFormData
  categories: Array<{ id: string; name: string; description?: string }>
  onSave: (data: CourseFormData) => Promise<void>
  children: React.ReactNode // The existing course form content
}

export default function CourseEditTabs({
  courseId,
  initialFormData,
  categories,
  onSave,
  children
}: CourseEditTabsProps) {
  const [activeTab, setActiveTab] = useState('basic')
  const [roadmapConfig, setRoadmapConfig] = useState<RoadmapConfig>({
    hasRoadmap: false,
    roadmapTitle: '',
    roadmapDescription: '',
    missions: []
  })
  const [loadingRoadmap, setLoadingRoadmap] = useState(true)

  useEffect(() => {
    fetchRoadmapConfig()
  }, [courseId])

  const fetchRoadmapConfig = async () => {
    try {
      setLoadingRoadmap(true)
      const response = await fetch(`/api/admin/courses/${courseId}/roadmap`)
      
      if (response.ok) {
        const data = await response.json()
        setRoadmapConfig(data.data || {
          hasRoadmap: false,
          roadmapTitle: '',
          roadmapDescription: '',
          missions: []
        })
      }
    } catch (error) {
      console.error('Failed to fetch roadmap config:', error)
    } finally {
      setLoadingRoadmap(false)
    }
  }

  const handleRoadmapUpdate = async (config: RoadmapConfig) => {
    try {
      const response = await fetch(`/api/admin/courses/${courseId}/roadmap`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update roadmap')
      }

      setRoadmapConfig(config)
      toast.success('Roadmap configuration saved!')
    } catch (error: any) {
      console.error('Error updating roadmap:', error)
      toast.error(error.message || 'Failed to save roadmap configuration')
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic" className="flex items-center space-x-2">
            <InformationCircleIcon className="h-4 w-4" />
            <span>Basic Info</span>
          </TabsTrigger>
          <TabsTrigger value="roadmap" className="flex items-center space-x-2">
            <MapIcon className="h-4 w-4" />
            <span>Learning Roadmap</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Cog6ToothIcon className="h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {children}
          </motion.div>
        </TabsContent>

        <TabsContent value="roadmap" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {loadingRoadmap ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <RoadmapConfig
                courseId={courseId}
                hasRoadmap={roadmapConfig.hasRoadmap}
                roadmapTitle={roadmapConfig.roadmapTitle}
                roadmapDescription={roadmapConfig.roadmapDescription}
                missions={roadmapConfig.missions}
                onUpdate={handleRoadmapUpdate}
              />
            )}
          </motion.div>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            <div className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Settings</h3>
              <p className="text-gray-600">
                Additional course settings and configurations will be available here.
              </p>
            </div>
          </motion.div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
