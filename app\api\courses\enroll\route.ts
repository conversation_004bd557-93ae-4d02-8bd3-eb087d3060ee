import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { NotificationEvents } from '@/lib/notification-events'
import { z } from 'zod'

const enrollmentSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

// POST /api/courses/enroll - Enroll user in a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: enrollmentSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId } = validatedBody

      // Check if course exists and is published
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          instructor: {
            select: { id: true, name: true }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (!course.isActive || !course.isPublished) {
        return APIResponse.error('Course is not available', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment && existingEnrollment.status === 'active') {
        return APIResponse.error('Already enrolled in this course', 400)
      }

      // Handle free courses
      if (course.price === 0) {
        // Create or update enrollment for free course
        const enrollmentData = {
          userId: user.id,
          courseId: courseId,
          status: 'active' as const,
          enrolledAt: new Date(),
          progress: 0
        }

        let enrollment
        if (existingEnrollment) {
          enrollment = await prisma.courseEnrollment.update({
            where: { id: existingEnrollment.id },
            data: enrollmentData
          })
        } else {
          enrollment = await prisma.courseEnrollment.create({
            data: enrollmentData
          })
        }

        // Update course student count based on actual enrollments
        const enrollmentCount = await prisma.courseEnrollment.count({
          where: {
            courseId: courseId,
            status: 'active'
          }
        })

        await prisma.course.update({
          where: { id: courseId },
          data: {
            studentsCount: enrollmentCount
          }
        })

        // Send enrollment notification
        try {
          await NotificationEvents.onCourseEnrolled(user.id, course.title, course.id)
        } catch (notificationError) {
          console.error('Failed to send enrollment notification:', notificationError)
          // Don't fail the enrollment if notification fails
        }

        return APIResponse.success({
          message: 'Successfully enrolled in course',
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            slug: course.slug,
            instructor: course.instructor
          },
          enrollment: {
            id: enrollment.id,
            status: enrollment.status,
            enrolledAt: enrollment.enrolledAt.toISOString(),
            progress: enrollment.progress
          }
        })
      } else {
        // For paid courses, return payment required response
        return APIResponse.success({
          message: 'Payment required to enroll in this course',
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            slug: course.slug,
            instructor: course.instructor
          },
          requiresPayment: true,
          paymentUrl: `/courses/${course.slug}/payment`
        })
      }

    } catch (error) {
      console.error('Error during course enrollment:', error)
      return APIResponse.error('Failed to enroll in course', 500)
    }
  }
)
