import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { MissionAchievementService } from '@/lib/mission-achievements'

// GET /api/student/mission-achievements - Get user's mission achievements
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get user's mission achievements
      const achievements = await MissionAchievementService.getUserAchievements(user.id)
      
      // Get user's mission statistics
      const stats = await MissionAchievementService.getUserMissionStats(user.id)
      
      return APIResponse.success({
        achievements,
        stats,
        summary: {
          totalEarned: achievements.earned.length,
          totalAvailable: achievements.available.length,
          completionRate: achievements.earned.length + achievements.available.length > 0 
            ? (achievements.earned.length / (achievements.earned.length + achievements.available.length)) * 100 
            : 0,
          totalPoints: achievements.earned.reduce((sum: number, a: any) => sum + (a.points || 0), 0)
        }
      }, 'Mission achievements retrieved successfully')

    } catch (error) {
      console.error('Error fetching mission achievements:', error)
      return APIResponse.internalServerError('Failed to fetch mission achievements')
    }
  }
)

// POST /api/student/mission-achievements/check - Check and award new achievements
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Check and award new achievements
      const newAchievements = await MissionAchievementService.checkAndAwardAchievements(user.id)
      
      return APIResponse.success({
        newAchievements,
        count: newAchievements.length,
        totalPoints: newAchievements.reduce((sum, a) => sum + a.points, 0)
      }, newAchievements.length > 0 
        ? `${newAchievements.length} new achievement(s) unlocked!` 
        : 'No new achievements at this time'
      )

    } catch (error) {
      console.error('Error checking mission achievements:', error)
      return APIResponse.internalServerError('Failed to check mission achievements')
    }
  }
)
