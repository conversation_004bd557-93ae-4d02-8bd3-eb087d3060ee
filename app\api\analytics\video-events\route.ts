import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const videoEventSchema = z.object({
  sessionId: z.string(),
  event: z.object({
    type: z.enum(['play', 'pause', 'seek', 'quality_change', 'speed_change', 'complete', 'buffer', 'error']),
    timestamp: z.number(),
    currentTime: z.number(),
    duration: z.number(),
    quality: z.string().optional(),
    speed: z.number().optional(),
    bufferDuration: z.number().optional(),
    errorMessage: z.string().optional()
  })
})

// POST /api/analytics/video-events - Store individual video events
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: videoEventSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      // Verify the session belongs to the user
      const session = await prisma.videoAnalytics.findFirst({
        where: {
          sessionId: validatedBody.sessionId,
          userId: user.id
        }
      })

      if (!session) {
        return APIResponse.error('Session not found or access denied', 404)
      }

      // Store the video event
      const videoEvent = await prisma.videoEvent.create({
        data: {
          sessionId: validatedBody.sessionId,
          type: validatedBody.event.type,
          timestamp: new Date(validatedBody.event.timestamp),
          currentTime: validatedBody.event.currentTime,
          duration: validatedBody.event.duration,
          quality: validatedBody.event.quality,
          speed: validatedBody.event.speed,
          bufferDuration: validatedBody.event.bufferDuration,
          errorMessage: validatedBody.event.errorMessage
        }
      })

      return APIResponse.success({
        message: 'Video event stored successfully',
        eventId: videoEvent.id
      })
    } catch (error) {
      console.error('Error storing video event:', error)
      return APIResponse.error('Failed to store video event', 500)
    }
  }
)

// GET /api/analytics/video-events - Get video events for a session (admin only)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const { searchParams } = new URL(request.url)
      const sessionId = searchParams.get('sessionId')
      const eventType = searchParams.get('type')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '100')

      if (!sessionId) {
        return APIResponse.error('Session ID is required', 400)
      }

      // Build where clause
      const where: any = { sessionId }
      if (eventType) where.type = eventType

      // Get total count
      const total = await prisma.videoEvent.count({ where })

      // Get events with pagination
      const events = await prisma.videoEvent.findMany({
        where,
        orderBy: { timestamp: 'asc' },
        skip: (page - 1) * limit,
        take: limit
      })

      // Get event type statistics
      const eventStats = await prisma.videoEvent.groupBy({
        by: ['type'],
        where: { sessionId },
        _count: {
          type: true
        }
      })

      return APIResponse.success({
        events,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        eventStatistics: eventStats.reduce((acc: Record<string, number>, stat: any) => {
          acc[stat.type] = stat._count.type
          return acc
        }, {} as Record<string, number>)
      })
    } catch (error) {
      console.error('Error fetching video events:', error)
      return APIResponse.error('Failed to fetch video events', 500)
    }
  }
)
