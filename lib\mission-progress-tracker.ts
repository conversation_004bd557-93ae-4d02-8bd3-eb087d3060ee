/**
 * Mission Progress Tracker
 * 
 * Handles real-time mission progress tracking, integrates with existing CourseProgress system,
 * and provides Socket.io updates for live progress updates.
 */

import { prisma } from '@/lib/prisma'
import { MissionNotificationService } from '@/lib/mission-notifications'

export interface MissionProgressData {
  missionId: string
  userId: string
  courseId: string
  contentId: string
  contentType: 'LESSON' | 'QUIZ' | 'ASSIGNMENT' | 'DISCUSSION'
  isCompleted: boolean
  pointsEarned?: number
}

export interface MissionProgressResult {
  missionProgress: {
    id: string
    isStarted: boolean
    isCompleted: boolean
    completionRate: number
    pointsEarned: number
    startedAt?: Date
    completedAt?: Date
  }
  missionCompleted: boolean
  achievementsUnlocked: string[]
  nextMissions: string[]
}

export class MissionProgressTracker {
  /**
   * Update mission progress based on content completion
   */
  static async updateMissionProgress(data: MissionProgressData): Promise<MissionProgressResult> {
    const { missionId, userId, courseId, contentId, contentType, isCompleted, pointsEarned = 0 } = data

    try {
      // Get mission details with contents and course
      const mission = await prisma.courseMission.findUnique({
        where: { id: missionId },
        include: {
          contents: {
            orderBy: { order: 'asc' }
          },
          course: {
            select: { title: true }
          }
        }
      })

      if (!mission) {
        throw new Error('Mission not found')
      }

      // Get or create mission progress
      let missionProgress = await prisma.missionProgress.findUnique({
        where: {
          userId_missionId: {
            userId,
            missionId
          }
        }
      })

      if (!missionProgress) {
        missionProgress = await prisma.missionProgress.create({
          data: {
            userId,
            missionId,
            isStarted: true,
            startedAt: new Date(),
            lastAccessAt: new Date()
          }
        })
      }

      // Calculate completion rate based on content completion
      const completedContents = await this.getCompletedMissionContents(userId, mission.contents)
      const totalRequiredContents = mission.contents.filter(c => c.isRequired).length
      const completedRequiredContents = completedContents.filter(c => 
        mission.contents.find(mc => mc.contentId === c.contentId && mc.isRequired)
      ).length

      const completionRate = totalRequiredContents > 0 
        ? (completedRequiredContents / totalRequiredContents) * 100 
        : 0

      const isMissionCompleted = completionRate >= 100

      // Update mission progress
      const updatedProgress = await prisma.missionProgress.update({
        where: { id: missionProgress.id },
        data: {
          completionRate,
          isCompleted: isMissionCompleted,
          pointsEarned: isMissionCompleted ? mission.pointsReward : Math.floor((completionRate / 100) * mission.pointsReward),
          completedAt: isMissionCompleted && !missionProgress.isCompleted ? new Date() : missionProgress.completedAt,
          lastAccessAt: new Date()
        }
      })

      // Check for achievements and unlocked missions
      const achievementsUnlocked: string[] = []
      const nextMissions: string[] = []

      if (isMissionCompleted && !missionProgress.isCompleted) {
        // Mission just completed
        
        // Check for badge rewards
        if (mission.badgeReward) {
          achievementsUnlocked.push(mission.badgeReward)
          // Badge achievement records will be created by the achievement service
        }

        // Find missions that have this mission as a prerequisite
        const dependentMissions = await prisma.courseMission.findMany({
          where: {
            courseId,
            prerequisites: {
              some: {
                prerequisiteMissionId: missionId
              }
            }
          },
          select: { id: true, title: true }
        })

        nextMissions.push(...dependentMissions.map(m => m.id))

        // Update course enrollment progress
        await this.updateCourseEnrollmentProgress(userId, courseId)

        // Send mission completion notification
        try {
          await MissionNotificationService.notifyMissionCompleted(userId, {
            missionId,
            missionTitle: mission.title,
            courseId,
            courseTitle: mission.course?.title || 'Course',
            pointsEarned: updatedProgress.pointsEarned,
            completionRate: 100
          })
        } catch (notificationError) {
          console.error('Failed to send mission completion notification:', notificationError)
          // Don't fail the mission completion if notification fails
        }
      }

      return {
        missionProgress: {
          id: updatedProgress.id,
          isStarted: updatedProgress.isStarted,
          isCompleted: updatedProgress.isCompleted,
          completionRate: updatedProgress.completionRate,
          pointsEarned: updatedProgress.pointsEarned,
          startedAt: updatedProgress.startedAt || undefined,
          completedAt: updatedProgress.completedAt || undefined
        },
        missionCompleted: isMissionCompleted && !missionProgress.isCompleted,
        achievementsUnlocked,
        nextMissions
      }

    } catch (error) {
      console.error('Error updating mission progress:', error)
      throw error
    }
  }

  /**
   * Get completed content items for a mission
   */
  private static async getCompletedMissionContents(userId: string, missionContents: any[]) {
    const completedContents = []

    for (const content of missionContents) {
      let isCompleted = false

      switch (content.contentType) {
        case 'LESSON':
          const lessonProgress = await prisma.courseProgress.findUnique({
            where: {
              userId_lessonId: {
                userId,
                lessonId: content.contentId
              }
            }
          })
          isCompleted = lessonProgress?.isCompleted || false
          break

        case 'QUIZ':
          const quizAttempt = await prisma.courseQuizAttempt.findFirst({
            where: {
              userId,
              quizId: content.contentId,
              isCompleted: true
            },
            orderBy: { completedAt: 'desc' }
          })
          isCompleted = !!quizAttempt
          break

        case 'ASSIGNMENT':
          // Check if assignment is completed
          const assignmentProgress = await prisma.courseProgress.findUnique({
            where: {
              userId_lessonId: {
                userId,
                lessonId: content.contentId
              }
            }
          })
          isCompleted = assignmentProgress?.isCompleted || false
          break

        case 'DISCUSSION':
          // Check if user has participated in discussion
          const discussionParticipation = await prisma.courseDiscussion.findFirst({
            where: {
              userId,
              lessonId: content.contentId
            }
          })
          isCompleted = !!discussionParticipation
          break
      }

      if (isCompleted) {
        completedContents.push({
          contentId: content.contentId,
          contentType: content.contentType
        })
      }
    }

    return completedContents
  }

  /**
   * Update overall course enrollment progress
   */
  private static async updateCourseEnrollmentProgress(userId: string, courseId: string) {
    try {
      // Get all missions for the course
      const missions = await prisma.courseMission.findMany({
        where: { courseId },
        include: {
          progress: {
            where: { userId },
            take: 1
          }
        }
      })

      const totalMissions = missions.length
      const completedMissions = missions.filter(m => m.progress[0]?.isCompleted).length
      const missionProgress = totalMissions > 0 ? (completedMissions / totalMissions) * 100 : 0

      // Get regular course progress
      const courseProgress = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId,
            courseId
          }
        }
      })

      if (courseProgress) {
        // Combine mission progress with regular lesson progress
        // For roadmap-enabled courses, give missions more weight
        const combinedProgress = Math.max(courseProgress.progress, missionProgress)

        await prisma.courseEnrollment.update({
          where: { id: courseProgress.id },
          data: {
            progress: combinedProgress,
            lastAccessedAt: new Date(),
            status: combinedProgress >= 100 ? 'completed' : 'active',
            completedAt: combinedProgress >= 100 && !courseProgress.completedAt ? new Date() : courseProgress.completedAt
          }
        })
      }
    } catch (error) {
      console.error('Error updating course enrollment progress:', error)
    }
  }

  /**
   * Get mission progress for a user
   */
  static async getMissionProgress(userId: string, missionId: string) {
    return await prisma.missionProgress.findUnique({
      where: {
        userId_missionId: {
          userId,
          missionId
        }
      }
    })
  }

  /**
   * Get all mission progress for a course
   */
  static async getCourseMissionProgress(userId: string, courseId: string) {
    return await prisma.missionProgress.findMany({
      where: {
        userId,
        mission: {
          courseId
        }
      },
      include: {
        mission: {
          select: {
            id: true,
            title: true,
            order: true,
            pointsReward: true
          }
        }
      },
      orderBy: {
        mission: {
          order: 'asc'
        }
      }
    })
  }

  /**
   * Calculate user's total roadmap statistics
   */
  static async getUserRoadmapStats(userId: string, courseId: string) {
    const missionProgress = await this.getCourseMissionProgress(userId, courseId)
    
    const totalMissions = missionProgress.length
    const completedMissions = missionProgress.filter(p => p.isCompleted).length
    const totalPoints = missionProgress.reduce((sum, p) => sum + p.pointsEarned, 0)
    
    // Calculate streak (simplified)
    const recentCompletions = missionProgress
      .filter(p => p.completedAt && p.completedAt >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
      .length

    return {
      totalMissions,
      completedMissions,
      totalPoints,
      currentStreak: recentCompletions,
      completionRate: totalMissions > 0 ? (completedMissions / totalMissions) * 100 : 0
    }
  }
}
