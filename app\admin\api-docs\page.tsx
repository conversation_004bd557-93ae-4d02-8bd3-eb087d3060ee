"use client";

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { 
  Code, 
  Download, 
  ExternalLink, 
  Search,
  Key,
  Shield,
  Zap,
  BookOpen,
  Copy,
  CheckCircle
} from "lucide-react"
import { toast } from "@/lib/toast-utils"

interface APIEndpoint {
  path: string
  method: string
  summary: string
  description: string
  tags: string[]
  requiresAuth: boolean
  requiredRole?: string
  rateLimit?: {
    requests: number
    window: string
  }
}

export default function APIDocsPage() {
  const [endpoints, setEndpoints] = useState<APIEndpoint[]>([])
  const [filteredEndpoints, setFilteredEndpoints] = useState<APIEndpoint[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTag, setSelectedTag] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  useEffect(() => {
    loadAPIDocumentation()
  }, [])

  useEffect(() => {
    filterEndpoints()
  }, [endpoints, searchTerm, selectedTag])

  const loadAPIDocumentation = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/docs')
      if (response.ok) {
        const data = await response.json()
        setEndpoints(data.data.endpoints)
      }
    } catch (error) {
      console.error('Error loading API documentation:', error)
      toast.error('Failed to load API documentation')
    } finally {
      setIsLoading(false)
    }
  }

  const filterEndpoints = () => {
    let filtered = endpoints

    if (searchTerm) {
      filtered = filtered.filter(endpoint =>
        endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
        endpoint.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        endpoint.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedTag !== 'all') {
      filtered = filtered.filter(endpoint =>
        endpoint.tags.includes(selectedTag)
      )
    }

    setFilteredEndpoints(filtered)
  }

  const getAllTags = () => {
    const tagSet = new Set<string>()
    endpoints.forEach(endpoint => {
      endpoint.tags.forEach(tag => tagSet.add(tag))
    })
    return Array.from(tagSet).sort()
  }

  const getMethodColor = (method: string) => {
    switch (method.toUpperCase()) {
      case 'GET': return 'bg-green-500'
      case 'POST': return 'bg-blue-500'
      case 'PUT': return 'bg-orange-500'
      case 'PATCH': return 'bg-yellow-500'
      case 'DELETE': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCode(id)
      toast.success('Copied to clipboard!')
      setTimeout(() => setCopiedCode(null), 2000)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  const downloadOpenAPISpec = async () => {
    try {
      const response = await fetch('/api/docs?format=openapi')
      if (response.ok) {
        const spec = await response.json()
        const blob = new Blob([JSON.stringify(spec, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = 'openapi-spec.json'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
        toast.success('OpenAPI specification downloaded!')
      }
    } catch (error) {
      toast.error('Failed to download OpenAPI specification')
    }
  }

  const downloadMarkdownDocs = async () => {
    try {
      const response = await fetch('/api/docs?format=markdown')
      if (response.ok) {
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = 'api-docs.md'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
        toast.success('Markdown documentation downloaded!')
      }
    } catch (error) {
      toast.error('Failed to download markdown documentation')
    }
  }

  const exampleCurlCommand = (endpoint: APIEndpoint) => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'
    let curl = `curl -X ${endpoint.method} "${baseUrl}${endpoint.path}"`
    
    if (endpoint.requiresAuth) {
      curl += ` \\\n  -H "Authorization: Bearer YOUR_API_KEY"`
    }
    
    if (['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
      curl += ` \\\n  -H "Content-Type: application/json" \\\n  -d '{"key": "value"}'`
    }
    
    return curl
  }

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BookOpen className="h-8 w-8 text-blue-600" />
            API Documentation
          </h1>
          <p className="text-muted-foreground mt-1">
            Comprehensive API reference for third-party integrations
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={downloadMarkdownDocs}>
            <Download className="h-4 w-4 mr-2" />
            Download MD
          </Button>
          <Button variant="outline" size="sm" onClick={downloadOpenAPISpec}>
            <Download className="h-4 w-4 mr-2" />
            OpenAPI Spec
          </Button>
          <Button size="sm" asChild>
            <a href="/admin/api-keys">
              <Key className="h-4 w-4 mr-2" />
              Manage API Keys
            </a>
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">{endpoints.length}</div>
                <p className="text-sm text-muted-foreground">Total Endpoints</p>
              </div>
              <Code className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">{getAllTags().length}</div>
                <p className="text-sm text-muted-foreground">Categories</p>
              </div>
              <Shield className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">REST</div>
                <p className="text-sm text-muted-foreground">API Type</p>
              </div>
              <Zap className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold">v1.0</div>
                <p className="text-sm text-muted-foreground">API Version</p>
              </div>
              <ExternalLink className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="endpoints" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="examples">Code Examples</TabsTrigger>
        </TabsList>

        {/* Endpoints Tab */}
        <TabsContent value="endpoints" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search endpoints..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">All Categories</option>
              {getAllTags().map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>

          {/* Endpoints List */}
          <div className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">Loading API documentation...</p>
              </div>
            ) : filteredEndpoints.length === 0 ? (
              <div className="text-center py-8">
                <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium">No endpoints found</p>
                <p className="text-muted-foreground">Try adjusting your search or filter criteria</p>
              </div>
            ) : (
              filteredEndpoints.map((endpoint, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge className={`${getMethodColor(endpoint.method)} text-white`}>
                          {endpoint.method}
                        </Badge>
                        <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                          {endpoint.path}
                        </code>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {endpoint.tags.map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <CardTitle className="text-lg">{endpoint.summary}</CardTitle>
                    <CardDescription>{endpoint.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      {/* Authentication & Rate Limiting */}
                      <div className="flex flex-wrap gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          <span>Auth: {endpoint.requiresAuth ? 'Required' : 'Optional'}</span>
                          {endpoint.requiredRole && (
                            <Badge variant="outline" className="text-xs">
                              {endpoint.requiredRole}
                            </Badge>
                          )}
                        </div>
                        
                        {endpoint.rateLimit && (
                          <div className="flex items-center gap-2">
                            <Zap className="h-4 w-4" />
                            <span>Rate Limit: {endpoint.rateLimit.requests}/{endpoint.rateLimit.window}</span>
                          </div>
                        )}
                      </div>

                      {/* Example cURL Command */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="text-sm font-medium">Example Request</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(exampleCurlCommand(endpoint), `curl-${index}`)}
                          >
                            {copiedCode === `curl-${index}` ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <pre className="bg-muted p-3 rounded-md text-xs overflow-x-auto">
                          <code>{exampleCurlCommand(endpoint)}</code>
                        </pre>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        {/* Authentication Tab */}
        <TabsContent value="authentication" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>API Key Authentication</CardTitle>
                <CardDescription>
                  Use API keys for programmatic access to the API
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Getting Started</h4>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Generate an API key from the admin panel</li>
                    <li>Include the key in the Authorization header</li>
                    <li>Make requests to authenticated endpoints</li>
                  </ol>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Example</h4>
                  <pre className="bg-muted p-3 rounded-md text-xs">
                    <code>{`curl -H "Authorization: Bearer qp_your_api_key_here" \\
  https://api.example.com/api/quizzes`}</code>
                  </pre>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Rate Limiting</CardTitle>
                <CardDescription>
                  API requests are rate-limited to ensure fair usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Rate Limit Tiers</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Read Operations:</span>
                      <Badge variant="outline">1000/15min</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Write Operations:</span>
                      <Badge variant="outline">100/15min</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Sensitive Operations:</span>
                      <Badge variant="outline">5/15min</Badge>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Response Headers</h4>
                  <pre className="bg-muted p-3 rounded-md text-xs">
                    <code>{`X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200`}</code>
                  </pre>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Examples Tab */}
        <TabsContent value="examples" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>JavaScript/Node.js</CardTitle>
                <CardDescription>
                  Example using fetch API
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                  <code>{`const response = await fetch('/api/quizzes', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);`}</code>
                </pre>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Python</CardTitle>
                <CardDescription>
                  Example using requests library
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
                  <code>{`import requests

headers = {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
}

response = requests.get(
    'https://api.example.com/api/quizzes',
    headers=headers
)

data = response.json()
print(data)`}</code>
                </pre>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
