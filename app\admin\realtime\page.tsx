"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { 
  Zap, 
  Bell, 
  MessageCircle, 
  Users, 
  Send,
  Settings,
  Activity,
  Wifi,
  TestTube
} from "lucide-react"
import { motion } from "framer-motion"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { LiveQuizSession } from "@/components/realtime/live-quiz-session"
import { ChatRoom } from "@/components/realtime/chat-room"
import { ConnectionStatus } from "@/components/realtime/connection-status"
import { getSocketClient, initializeSocket } from "@/lib/socket-client"
import { toast } from "@/lib/toast-utils"

interface Quiz {
  id: string
  title: string
  description?: string
  type: string
  difficulty: string
  isPublished: boolean
}

export default function RealtimePage() {
  const { data: session } = useSession()
  const [isInitialized, setIsInitialized] = useState(false)
  const [testNotification, setTestNotification] = useState({
    type: 'info' as 'info' | 'success' | 'warning' | 'error',
    title: '',
    message: '',
    targetUserId: ''
  })
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loadingQuizzes, setLoadingQuizzes] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingExample, setEditingExample] = useState<{
    type: 'system' | 'maintenance' | 'quiz' | 'service'
    title: string
    message: string
    selectedQuizId?: string
  } | null>(null)

  useEffect(() => {
    if (session?.user && !isInitialized) {
             // Initialize socket connection with real session data
      initializeSocket({
        id: session.user.id,
        name: session.user.name || 'Admin',
        email: session.user.email || '',
        role: session.user.role || 'ADMIN'
      })

      setIsInitialized(true)
      fetchQuizzes()
    }
  }, [session?.user?.id, isInitialized])

  const fetchQuizzes = async () => {
    try {
      setLoadingQuizzes(true)
      const response = await fetch('/api/admin/quizzes?limit=50&status=published')
      if (response.ok) {
        const data = await response.json()
        setQuizzes(data.quizzes || [])
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to load quizzes')
    } finally {
      setLoadingQuizzes(false)
    }
  }

  const handleEditExample = (type: 'system' | 'maintenance' | 'quiz' | 'service') => {
    const examples = {
      system: { title: 'System Update', message: 'The platform has been successfully updated with new features!' },
      maintenance: { title: 'Maintenance Notice', message: 'Scheduled maintenance will begin in 30 minutes.' },
      quiz: { title: 'New Quiz Available', message: 'A new quiz has been published!' },
      service: { title: 'Service Alert', message: 'Some features may be temporarily unavailable.' }
    }

    setEditingExample({
      type,
      title: examples[type].title,
      message: examples[type].message,
      selectedQuizId: type === 'quiz' ? '' : undefined
    })
    setShowEditDialog(true)
  }

  const handleSendEditedExample = () => {
    if (!editingExample) return

    const socketClient = getSocketClient()
    let finalMessage = editingExample.message

    // If it's a quiz notification and a quiz is selected, customize the message
    if (editingExample.type === 'quiz' && editingExample.selectedQuizId) {
      const selectedQuiz = quizzes.find(q => q.id === editingExample.selectedQuizId)
      if (selectedQuiz) {
        finalMessage = `${selectedQuiz.title} is now available! ${editingExample.message}`
      }
    }

    const notificationTypes = {
      system: 'success' as const,
      maintenance: 'warning' as const,
      quiz: 'info' as const,
      service: 'error' as const
    }

    socketClient.broadcastNotification({
      type: notificationTypes[editingExample.type],
      title: editingExample.title,
      message: finalMessage
    })

    toast.success('Notification sent successfully!')
    setShowEditDialog(false)
    setEditingExample(null)
  }

  const sendTestNotification = () => {
    if (!testNotification.title || !testNotification.message) {
      toast.error('Please fill in title and message')
      return
    }

    const socketClient = getSocketClient()

    if (testNotification.targetUserId) {
      // Send to specific user
      socketClient.sendNotification({
        targetUserId: testNotification.targetUserId,
        type: testNotification.type,
        title: testNotification.title,
        message: testNotification.message,
        data: { source: 'admin-panel' }
      })
      toast.success('Notification sent to specific user')
    } else {
      // Broadcast to all users
      socketClient.broadcastNotification({
        type: testNotification.type,
        title: testNotification.title,
        message: testNotification.message,
        data: { source: 'admin-panel' }
      })
      toast.success('Notification broadcasted to all users')
    }

    // Reset form
    setTestNotification({
      type: 'info',
      title: '',
      message: '',
      targetUserId: ''
    })
  }

  const sendTestQuizUpdate = () => {
    const socketClient = getSocketClient()
    socketClient.sendQuizProgress({
      quizId: 'demo-quiz-1',
      questionIndex: Math.floor(Math.random() * 10),
      timeRemaining: Math.floor(Math.random() * 300) + 60,
      answered: Math.random() > 0.5
    })
    toast.success('Test quiz progress sent')
  }

  if (!session?.user || !isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6 backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
              </div>
              <p className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-2">
                {!session?.user ? 'Loading session...' : 'Initializing real-time connection...'}
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="p-6 space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl shadow-lg">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
                    Real-time Monitor
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    WebSocket-powered real-time communication and collaboration
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <ConnectionStatus />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Connection Status Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <ConnectionStatus showDetails={true} />
        </motion.div>

        {/* Real-time Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Tabs defaultValue="notifications" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 glass bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border-white/20 dark:border-gray-800/20 shadow-lg p-2 h-auto">
              <TabsTrigger
                value="notifications"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger
                value="quiz"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <Activity className="h-4 w-4" />
                Live Quiz
              </TabsTrigger>
              <TabsTrigger
                value="chat"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <MessageCircle className="h-4 w-4" />
                Chat
              </TabsTrigger>
              <TabsTrigger
                value="testing"
                className="gap-2 h-12 glass data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-600 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-300"
              >
                <TestTube className="h-4 w-4" />
                Testing
              </TabsTrigger>
            </TabsList>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Send Notification */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Send className="h-5 w-5" />
                  Send Notification
                </CardTitle>
                <CardDescription>
                  Send real-time notifications to users
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Type</label>
                  <Select 
                    value={testNotification.type} 
                    onValueChange={(value: any) => setTestNotification(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="success">Success</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Title</label>
                  <Input
                    value={testNotification.title}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Notification title"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Message</label>
                  <Textarea
                    value={testNotification.message}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Notification message"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Target User ID (optional)</label>
                  <Input
                    value={testNotification.targetUserId}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, targetUserId: e.target.value }))}
                    placeholder="Leave empty to broadcast to all users"
                  />
                </div>

                <Button onClick={sendTestNotification} className="w-full">
                  <Send className="h-4 w-4 mr-2" />
                  Send Notification
                </Button>
              </CardContent>
            </Card>

            {/* Notification Examples */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Examples</CardTitle>
                <CardDescription>
                  Send common notification types
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleEditExample('system')}
                >
                  <Badge className="bg-green-500 mr-2">Success</Badge>
                  System Update
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleEditExample('maintenance')}
                >
                  <Badge className="bg-yellow-500 mr-2">Warning</Badge>
                  Maintenance Notice
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleEditExample('quiz')}
                >
                  <Badge className="bg-blue-500 mr-2">Info</Badge>
                  New Quiz Available
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleEditExample('service')}
                >
                  <Badge className="bg-red-500 mr-2">Error</Badge>
                  Service Alert
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Edit Example Dialog */}
          <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Edit Notification Example</DialogTitle>
                <DialogDescription>
                  Customize the notification content before sending
                </DialogDescription>
              </DialogHeader>
              {editingExample && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={editingExample.title}
                      onChange={(e) => setEditingExample(prev => prev ? { ...prev, title: e.target.value } : null)}
                      placeholder="Notification title"
                    />
                  </div>

                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      value={editingExample.message}
                      onChange={(e) => setEditingExample(prev => prev ? { ...prev, message: e.target.value } : null)}
                      placeholder="Notification message"
                      rows={3}
                    />
                  </div>

                  {editingExample.type === 'quiz' && (
                    <div>
                      <Label htmlFor="quiz">Select Quiz (Optional)</Label>
                      <Select
                        value={editingExample.selectedQuizId || ''}
                        onValueChange={(value) => setEditingExample(prev => prev ? { ...prev, selectedQuizId: value } : null)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a quiz to feature" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No specific quiz</SelectItem>
                          {quizzes.map((quiz) => (
                            <SelectItem key={quiz.id} value={quiz.id}>
                              {quiz.title} ({quiz.difficulty})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleSendEditedExample}>
                      Send Notification
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </TabsContent>

        {/* Live Quiz Tab */}
        <TabsContent value="quiz" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <LiveQuizSession
                quizId="demo-quiz-1"
                quizTitle="JavaScript Fundamentals Demo"
                totalQuestions={25}
                duration={45}
                onJoin={() => toast.success('Joined demo quiz session')}
                onLeave={() => toast.info('Left demo quiz session')}
              />
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quiz Controls</CardTitle>
                  <CardDescription>
                    Test quiz session features
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={sendTestQuizUpdate}
                  >
                    Send Progress Update
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      const socketClient = getSocketClient()
                      socketClient.completeQuiz({
                        quizId: 'demo-quiz-1',
                        score: Math.floor(Math.random() * 40) + 60,
                        timeSpent: Math.floor(Math.random() * 30) + 15,
                        rank: Math.floor(Math.random() * 10) + 1
                      })
                      toast.success('Test quiz completion sent')
                    }}
                  >
                    Complete Quiz (Test)
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Session Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span>Active Sessions:</span>
                      <Badge>1</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Participants:</span>
                      <Badge>0</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Completed:</span>
                      <Badge>0</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Chat Tab */}
        <TabsContent value="chat" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ChatRoom
              key="admin-student-general"
              roomId="student-general"
              roomName="General Discussion"
              roomType="public"
            />

            <ChatRoom
              key="admin-student-study-help"
              roomId="student-study-help"
              roomName="Study Help & Questions"
              roomType="public"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <ChatRoom
              key="admin-student-quiz-discussion"
              roomId="student-quiz-discussion"
              roomName="Quiz Discussion"
              roomType="quiz"
            />

            <div className="flex items-center justify-center p-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
              <div className="text-center text-muted-foreground">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">Additional chat rooms can be added here</p>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Testing Tab */}
        <TabsContent value="testing" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Connection Test</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    const socketClient = getSocketClient()
                    toast.info(`Connected: ${socketClient.isConnected()}`)
                  }}
                >
                  Check Connection
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    if (typeof window !== 'undefined') {
                      window.location.reload()
                    }
                  }}
                >
                  Reconnect
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Simulation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Simulate user joining
                    toast.success('Simulated user joined')
                  }}
                >
                  Simulate User Join
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Simulate user leaving
                    toast.info('Simulated user left')
                  }}
                >
                  Simulate User Leave
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span>Latency:</span>
                    <Badge variant="outline">~50ms</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Messages/sec:</span>
                    <Badge variant="outline">~100</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <Badge variant="outline">~2MB</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}
