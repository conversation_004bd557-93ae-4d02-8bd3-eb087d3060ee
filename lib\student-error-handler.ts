import { toast } from "@/lib/toast-utils"

export interface APIError {
  message: string
  code?: string
  status: number
  details?: any
}

export class StudentAPIError extends Error {
  public status: number
  public code?: string
  public details?: any

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message)
    this.name = 'StudentAPIError'
    this.status = status
    this.code = code
    this.details = details
  }
}

/**
 * Utility function to safely extract a string message from any error type
 */
function safeExtractMessage(error: any, fallback: string = 'An error occurred'): string {
  // Handle StudentAPIError instances first
  if (error instanceof StudentAPIError) {
    return error.message
  }

  // Handle standard Error instances
  if (error instanceof Error) {
    return error.message || fallback
  }

  if (typeof error === 'string') {
    return error
  }

  if (error && typeof error === 'object') {
    // Try common error message properties
    if (typeof error.message === 'string') {
      return error.message
    }
    if (typeof error.error === 'string') {
      return error.error
    }
    if (typeof error.msg === 'string') {
      return error.msg
    }

    // If it's an object with nested message
    if (error.message && typeof error.message === 'object') {
      return safeExtractMessage(error.message, fallback)
    }

    // For API error responses, try to extract the actual error message
    if (error.error && typeof error.error === 'object' && error.error.message) {
      return String(error.error.message)
    }

    // Don't stringify complex objects - they're not user-friendly
    // Just return the fallback message
    return fallback
  }

  return fallback
}

/**
 * Centralized error handler for student-side API calls
 */
export class StudentErrorHandler {
  /**
   * Parse API response and throw appropriate error
   */
  static async handleAPIResponse(response: Response): Promise<any> {
    if (response.ok) {
      try {
        return await response.json()
      } catch {
        // If JSON parsing fails on successful response, return empty object
        return {}
      }
    }

    let errorData: any = {}
    try {
      const text = await response.text()
      if (text) {
        try {
          errorData = JSON.parse(text)
        } catch {
          // If not valid JSON, treat as plain text message
          errorData = { message: text }
        }
      } else {
        errorData = { message: response.statusText || 'Unknown error' }
      }
    } catch {
      // If we can't read the response at all, use status text
      errorData = { message: response.statusText || 'Unknown error' }
    }

    const userFriendlyMessage = this.getUserFriendlyMessage(response.status, errorData)

    throw new StudentAPIError(
      userFriendlyMessage,
      response.status,
      errorData.code
    )
  }

  /**
   * Convert technical errors to user-friendly messages
   */
  static getUserFriendlyMessage(status: number, errorData: any): string {
    // Safely extract message from errorData
    const message = safeExtractMessage(errorData, 'An error occurred')
    const code = errorData.code

    // Handle specific error codes
    switch (code) {
      case 'MAX_ATTEMPTS_EXCEEDED':
        return 'You have reached the maximum number of attempts for this quiz.'
      case 'QUIZ_NOT_FOUND':
        return 'This quiz could not be found. It may have been removed or you may not have access to it.'
      case 'QUIZ_NOT_PUBLISHED':
        return 'This quiz is not currently available. Please check back later.'
      case 'QUIZ_EXPIRED':
        return 'This quiz has expired and is no longer available.'
      case 'ALREADY_ENROLLED':
        return 'You are already enrolled in this quiz.'
      case 'ENROLLMENT_REQUIRED':
        return 'You need to enroll in this quiz before taking it.'
      case 'ATTEMPT_NOT_FOUND':
        return 'Quiz attempt not found. Please start a new attempt.'
      case 'ATTEMPT_ALREADY_COMPLETED':
        return 'This quiz attempt has already been completed.'
      case 'UNAUTHORIZED':
        return 'You are not authorized to access this resource. Please log in again.'
      case 'FORBIDDEN':
        return 'You do not have permission to access this resource.'
    }

    // Handle by status code
    switch (status) {
      case 400:
        return message.includes('validation') 
          ? 'Please check your input and try again.'
          : message || 'Invalid request. Please check your input.'
      
      case 401:
        return 'Your session has expired. Please log in again.'
      
      case 403:
        return 'You do not have permission to perform this action.'
      
      case 404:
        return message.includes('quiz') 
          ? 'Quiz not found. It may have been removed or you may not have access to it.'
          : message.includes('result')
          ? 'Quiz result not found. Please check if the quiz was completed.'
          : 'The requested resource was not found.'
      
      case 409:
        return 'This action conflicts with the current state. Please refresh and try again.'
      
      case 429:
        return 'Too many requests. Please wait a moment before trying again.'
      
      case 500:
        return 'Server error. Please try again in a moment.'
      
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again later.'
      
      default:
        return message || 'An unexpected error occurred. Please try again.'
    }
  }

  /**
   * Handle errors with toast notifications and optional retry
   */
  static handleError(
    error: Error | StudentAPIError | any,
    context: string,
    options: {
      showToast?: boolean
      toastType?: 'error' | 'warning'
      retryCallback?: () => void
      fallbackMessage?: string
    } = {}
  ): string {
    const {
      showToast = true,
      toastType = 'error',
      retryCallback,
      fallbackMessage = 'An error occurred'
    } = options

    console.error(`${context}:`, error)

    // Safely extract message from any error type
    const message = safeExtractMessage(error, fallbackMessage)



    if (showToast) {
      if (toastType === 'error') {
        toast.error(message, {
          action: retryCallback ? {
            label: 'Retry',
            onClick: retryCallback
          } : undefined
        })
      } else {
        toast.warning(message)
      }
    }

    return message
  }

  /**
   * Wrapper for fetch with automatic error handling
   */
  static async fetchWithErrorHandling(
    url: string,
    options: RequestInit = {},
    context: string = 'API call'
  ): Promise<any> {
    try {
      const response = await fetch(url, options)
      return await this.handleAPIResponse(response)
    } catch (error) {
      if (error instanceof StudentAPIError) {
        throw error
      }

      // Handle network errors
      if (error instanceof TypeError && error.message && error.message.includes('fetch')) {
        throw new StudentAPIError(
          'Network error. Please check your internet connection.',
          0,
          'NETWORK_ERROR'
        )
      }

      // Log context for debugging
      console.error(`${context} failed:`, error)

      throw new StudentAPIError(
        'An unexpected error occurred. Please try again.',
        500,
        'UNKNOWN_ERROR'
      )
    }
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: StudentAPIError | Error): boolean {
    if (error instanceof StudentAPIError) {
      return error.status >= 500 || error.status === 0 || error.code === 'NETWORK_ERROR'
    }
    // For generic errors, only retry if it looks like a network error
    return !!(error.message && error.message.toLowerCase().includes('network'))
  }

  /**
   * Get retry delay based on attempt count
   */
  static getRetryDelay(attemptCount: number): number {
    return Math.min(1000 * Math.pow(2, attemptCount), 10000) // Exponential backoff, max 10s
  }
}

/**
 * Hook for handling retries with exponential backoff
 */
export function useRetryableRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3
) {
  const executeWithRetry = async (
    onSuccess?: (data: T) => void,
    onError?: (error: StudentAPIError) => void
  ): Promise<T | null> => {
    let lastError: StudentAPIError | null = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const result = await requestFn()
        onSuccess?.(result)
        return result
      } catch (error) {
        // Convert any error to StudentAPIError for consistent handling
        if (error instanceof StudentAPIError) {
          lastError = error
        } else if (error instanceof Error) {
          lastError = new StudentAPIError(
            error.message || 'Unknown error',
            500,
            'UNKNOWN_ERROR'
          )
        } else {
          lastError = new StudentAPIError(
            typeof error === 'string' ? error : 'Unknown error',
            500,
            'UNKNOWN_ERROR'
          )
        }

        if (attempt < maxRetries && StudentErrorHandler.isRetryableError(lastError)) {
          const delay = StudentErrorHandler.getRetryDelay(attempt)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }

        break
      }
    }

    if (lastError) {
      onError?.(lastError)
      throw lastError
    }

    return null
  }

  return { executeWithRetry }
}
