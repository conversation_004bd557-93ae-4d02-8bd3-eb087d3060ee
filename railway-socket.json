{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm install && npx prisma generate"}, "deploy": {"startCommand": "npm run start:socket:railway", "numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3, "healthcheckPath": "/health", "healthcheckTimeout": 30}, "environments": {"production": {"variables": {"NODE_ENV": "production"}}}}