import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { quizBundleService } from '@/lib/quiz-bundle-service'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['all', 'active', 'expired', 'cancelled']).optional().default('all')
})

// GET /api/student/quiz-bundles - Get student's purchased quiz bundles
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, status } = validatedQuery

      // Get user's purchased bundles
      const purchases = await quizBundleService.getUserBundles(user.id, status)

      // Paginate results
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedPurchases = purchases.slice(startIndex, endIndex)

      // Calculate progress for each bundle
      const bundlesWithProgress = await Promise.all(
        paginatedPurchases.map(async (purchase) => {
          const progress = await quizBundleService.calculateBundleProgress(
            user.id,
            purchase.bundleId
          )

          return {
            id: purchase.id,
            bundleId: purchase.bundleId,
            status: purchase.status,
            progress: purchase.progress,
            purchasedAt: purchase.purchasedAt,
            expiresAt: purchase.expiresAt,
            completedAt: purchase.completedAt,
            lastAccessedAt: purchase.lastAccessedAt,
            bundle: {
              id: purchase.bundle.id,
              title: purchase.bundle.title,
              description: purchase.bundle.description,
              shortDescription: purchase.bundle.shortDescription,
              slug: purchase.bundle.slug,
              thumbnailImage: purchase.bundle.thumbnailImage,
              category: purchase.bundle.category,
              level: purchase.bundle.level,
              duration: purchase.bundle.duration,
              tags: purchase.bundle.tags,
              features: purchase.bundle.features,
              price: purchase.bundle.price,
              quizCount: purchase.bundle.items?.length || 0,
              quizzes: purchase.bundle.items?.map(item => ({
                id: item.quiz.id,
                title: item.quiz.title,
                type: item.quiz.type,
                difficulty: item.quiz.difficulty,
                timeLimit: item.quiz.timeLimit,
                order: item.order,
                isRequired: item.isRequired
              })) || []
            },
            calculatedProgress: {
              totalQuizzes: progress.totalQuizzes,
              completedQuizzes: progress.completedQuizzes,
              progressPercentage: progress.progressPercentage,
              lastAccessedAt: progress.lastAccessedAt
            }
          }
        })
      )

      // Calculate summary statistics
      const summary = {
        totalBundles: purchases.length,
        activeBundles: purchases.filter(p => p.status === 'active').length,
        completedBundles: purchases.filter(p => p.completedAt).length,
        totalQuizzes: purchases.reduce((sum, p) => sum + (p.bundle.items?.length || 0), 0),
        completedQuizzes: 0 // Will be calculated from progress
      }

      // Calculate total completed quizzes
      summary.completedQuizzes = bundlesWithProgress.reduce(
        (sum, bundle) => sum + bundle.calculatedProgress.completedQuizzes,
        0
      )

      return APIResponse.success({
        bundles: bundlesWithProgress,
        summary,
        pagination: {
          page,
          limit,
          total: purchases.length,
          totalPages: Math.ceil(purchases.length / limit),
          hasNext: endIndex < purchases.length,
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching student quiz bundles:', error)
      return APIResponse.error('Failed to fetch quiz bundles', 500)
    }
  }
)
