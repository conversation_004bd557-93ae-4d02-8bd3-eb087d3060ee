import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { auth } from '@/auth'

// GET /api/courses/lessons/[lessonId]/preview - Get lesson content for preview (free lessons only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ lessonId: string }> }
) {
  try {
    const resolvedParams = await params
    const lessonId = resolvedParams?.lessonId as string

    if (!lessonId) {
      return APIResponse.error('Lesson ID is required', 400)
    }

    // Get session to check if user is enrolled (optional)
    const session = await auth()
    const userId = session?.user?.id

    // Find the lesson with course information
    const lesson = await prisma.courseLesson.findUnique({
      where: {
        id: lessonId,
        isPublished: true
      },
      include: {
        video: true,
        chapter: {
          include: {
            section: {
              include: {
                course: {
                  select: {
                    id: true,
                    title: true,
                    slug: true,
                    isActive: true,
                    isPublished: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!lesson) {
      return APIResponse.error('Lesson not found', 404)
    }

    // Check if course is active and published
    if (!lesson.chapter.section.course.isActive || !lesson.chapter.section.course.isPublished) {
      return APIResponse.error('Course is not available', 404)
    }

    // Check if user is enrolled (if authenticated)
    let isEnrolled = false
    if (userId) {
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: userId,
            courseId: lesson.chapter.section.course.id
          }
        }
      })
      isEnrolled = enrollment?.status === 'active'
    }

    // Check access permissions
    // Allow access if:
    // 1. User is enrolled, OR
    // 2. Lesson is marked as free/preview
    if (!isEnrolled && !lesson.isFree) {
      return APIResponse.error('Access denied. This lesson requires enrollment.', 403)
    }

    // Prepare lesson data based on access level
    const lessonData = {
      id: lesson.id,
      title: lesson.title,
      description: lesson.description,
      type: lesson.type,
      duration: lesson.duration,
      isFree: lesson.isFree,
      course: {
        id: lesson.chapter.section.course.id,
        title: lesson.chapter.section.course.title,
        slug: lesson.chapter.section.course.slug
      },
      // Include content based on lesson type and access level
      content: lesson.content,
      video: lesson.video ? {
        id: lesson.video.id,
        url: lesson.video.url,
        duration: lesson.video.duration,
        thumbnailUrl: lesson.video.thumbnailUrl
      } : null,
      attachments: lesson.attachments || [],
      isEnrolled,
      hasAccess: isEnrolled || lesson.isFree
    }

    return APIResponse.success({ 
      lesson: lessonData,
      message: lesson.isFree ? 'Free preview lesson' : 'Enrolled lesson access'
    })

  } catch (error) {
    console.error('Error fetching lesson preview:', error)
    return APIResponse.error('Failed to fetch lesson preview', 500)
  }
}
