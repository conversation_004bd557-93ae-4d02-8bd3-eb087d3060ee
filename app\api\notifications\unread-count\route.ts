import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler } from '@/lib/api-middleware'
import { notificationService } from '@/lib/notification-service'

// GET /api/notifications/unread-count - Get unread notification count
export const GET = createAPIHandler({ requireAuth: true }, async (request: NextRequest, { session }) => {
  try {
    const count = await notificationService.getUnreadCount(session!.user.id)
    return NextResponse.json({ count })
  } catch (error) {
    console.error('Error getting unread count:', error)
    return NextResponse.json(
      { error: 'Failed to get unread count' },
      { status: 500 }
    )
  }
})
