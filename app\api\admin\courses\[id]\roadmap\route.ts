import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const missionContentSchema = z.object({
  contentId: z.string(),
  contentType: z.enum(['VIDEO', 'TEXT', 'QUIZ', 'ASSIGNMENT', 'DOCUMENT']),
  order: z.number().default(0),
  isRequired: z.boolean().default(true)
})

type MissionContentInput = z.infer<typeof missionContentSchema>

const missionSchema = z.object({
  title: z.string().min(1, 'Mission title is required'),
  description: z.string().optional(),
  icon: z.string().optional(),
  color: z.string().optional(),
  order: z.number(),
  xPosition: z.number().optional(),
  yPosition: z.number().optional(),
  isRequired: z.boolean().default(true),
  pointsReward: z.number().default(0),
  badgeReward: z.string().optional(),
  estimatedTime: z.string().optional(),
  contents: z.array(missionContentSchema),
  prerequisites: z.array(z.string()).default([])
})

const roadmapConfigSchema = z.object({
  hasRoadmap: z.boolean(),
  roadmapTitle: z.string().optional(),
  roadmapDescription: z.string().optional(),
  estimatedCompletion: z.string().optional(),
  missions: z.array(missionSchema).default([])
})

// GET /api/admin/courses/[id]/roadmap - Get course roadmap configuration
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const courseId = (await params)?.id as string

      if (!courseId) {
        return APIResponse.badRequest('Course ID is required')
      }

      // Get course with roadmap data
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          missions: {
            include: {
              contents: true,
              prerequisites: {
                include: {
                  prerequisiteMission: {
                    select: { id: true, title: true }
                  }
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!course) {
        return APIResponse.notFound('Course not found')
      }

      // Transform missions data
      const missions = course.missions.map(mission => ({
        id: mission.id,
        title: mission.title,
        description: mission.description,
        icon: mission.icon,
        color: mission.color,
        order: mission.order,
        xPosition: mission.xPosition,
        yPosition: mission.yPosition,
        isRequired: mission.isRequired,
        pointsReward: mission.pointsReward,
        badgeReward: mission.badgeReward,
        estimatedTime: mission.estimatedTime,
        contents: mission.contents,
        prerequisites: mission.prerequisites.map(p => p.prerequisiteMissionId)
      }))

      return APIResponse.success({
        hasRoadmap: course.hasRoadmap,
        roadmapTitle: course.roadmapTitle,
        roadmapDescription: course.roadmapDescription,
        estimatedCompletion: course.duration || '',
        missions
      }, 'Roadmap configuration retrieved successfully')

    } catch (error) {
      console.error('Error fetching roadmap configuration:', error)
      return APIResponse.internalServerError('Failed to fetch roadmap configuration')
    }
  }
)

// PUT /api/admin/courses/[id]/roadmap - Update course roadmap configuration
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: roadmapConfigSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const courseId = (await params)?.id as string

      if (!courseId) {
        return APIResponse.badRequest('Course ID is required')
      }

      const { hasRoadmap, roadmapTitle, roadmapDescription, estimatedCompletion, missions } = validatedBody

      // Verify course exists
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { id: true }
      })

      if (!course) {
        return APIResponse.notFound('Course not found')
      }

      // Use transaction to update everything atomically
      await prisma.$transaction(async (tx) => {
        // Update course roadmap settings
        await tx.course.update({
          where: { id: courseId },
          data: {
            hasRoadmap,
            roadmapTitle,
            roadmapDescription,
            duration: estimatedCompletion
          }
        })

        if (hasRoadmap && missions.length > 0) {
          // Delete existing missions and related data
          await tx.missionPrerequisite.deleteMany({
            where: {
              mission: { courseId }
            }
          })

          await tx.missionContent.deleteMany({
            where: {
              mission: { courseId }
            }
          })

          await tx.courseMission.deleteMany({
            where: { courseId }
          })

          // Create new missions and store mapping for prerequisites
          const missionIdMapping = new Map<string, string>()
          const createdMissions = []

          // First pass: Create all missions
          for (const mission of missions) {
            const createdMission = await tx.courseMission.create({
              data: {
                courseId,
                title: mission.title,
                description: mission.description,
                icon: mission.icon,
                color: mission.color,
                order: mission.order,
                xPosition: mission.xPosition,
                yPosition: mission.yPosition,
                isRequired: mission.isRequired,
                pointsReward: mission.pointsReward,
                badgeReward: mission.badgeReward,
                estimatedTime: mission.estimatedTime
              }
            })

            // Store mapping for prerequisites
            if (mission.id) {
              missionIdMapping.set(mission.id, createdMission.id)
            }
            createdMissions.push({ mission, createdMission })

            // Create mission contents
            if (mission.contents && mission.contents.length > 0) {
              await tx.missionContent.createMany({
                data: mission.contents.map((content: MissionContentInput) => ({
                  missionId: createdMission.id,
                  contentId: content.contentId,
                  contentType: content.contentType,
                  order: content.order || 0,
                  isRequired: content.isRequired !== false
                }))
              })
            }
          }

          // Second pass: Create prerequisites using the new mission IDs
          for (const { mission, createdMission } of createdMissions) {
            if (mission.prerequisites && mission.prerequisites.length > 0) {
              const prerequisiteData = mission.prerequisites
                .map((prereqId: string) => {
                  const newPrereqId = missionIdMapping.get(prereqId)
                  if (newPrereqId) {
                    return {
                      missionId: createdMission.id,
                      prerequisiteMissionId: newPrereqId
                    }
                  }
                  return null
                })
                .filter(Boolean)

              if (prerequisiteData.length > 0) {
                await tx.missionPrerequisite.createMany({
                  data: prerequisiteData
                })
              }
            }
          }
        } else if (!hasRoadmap) {
          // If roadmap is disabled, clean up all roadmap data
          await tx.missionPrerequisite.deleteMany({
            where: {
              mission: { courseId }
            }
          })
          
          await tx.missionContent.deleteMany({
            where: {
              mission: { courseId }
            }
          })
          
          await tx.courseMission.deleteMany({
            where: { courseId }
          })
        }
      })

      return APIResponse.success(
        { message: 'Roadmap configuration updated successfully' },
        'Roadmap updated successfully'
      )

    } catch (error: any) {
      console.error('Error updating roadmap configuration:', error)

      // Provide more specific error messages
      if (error.code === 'P2002') {
        return APIResponse.error('Duplicate entry found', 400)
      }

      if (error.code === 'P2025') {
        return APIResponse.error('Record not found', 404)
      }

      // Return the actual error message for debugging
      const errorMessage = error.message || 'Failed to update roadmap configuration'
      return APIResponse.error(errorMessage, 500)
    }
  }
)
