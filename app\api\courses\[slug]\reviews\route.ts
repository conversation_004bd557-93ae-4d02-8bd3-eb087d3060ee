import { NextRequest } from 'next/server'
import { z } from 'zod'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

const createReviewSchema = z.object({
  rating: z.number().min(1).max(5),
  title: z.string().optional(),
  comment: z.string().optional(),
  isPublic: z.boolean().default(true)
})

const querySchema = commonSchemas.pagination.extend({
  sortBy: z.enum(['rating', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

// GET /api/courses/[slug]/reviews - Get reviews for a course
export const GET = createAPIHandler(
  {
    requireAuth: false,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, params }) => {
    try {
      const resolvedParams = await params
      const courseSlug = resolvedParams?.slug as string

      if (!courseSlug) {
        return APIResponse.error('Course slug is required', 400)
      }

      const { page = 1, limit = 10, sortBy, sortOrder } = validatedQuery

      // Find course by slug
      const course = await prisma.course.findUnique({
        where: { 
          slug: courseSlug,
          isActive: true,
          isPublished: true
        },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const offset = (page - 1) * limit

      // Get reviews with user details
      const reviews = await prisma.courseReview.findMany({
        where: {
          courseId: course.id,
          isPublic: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        },
        orderBy: { [sortBy]: sortOrder },
        take: limit,
        skip: offset
      })

      // Get total count for pagination
      const totalCount = await prisma.courseReview.count({
        where: {
          courseId: course.id,
          isPublic: true
        }
      })

      // Calculate rating statistics
      const ratingStats = await prisma.courseReview.groupBy({
        by: ['rating'],
        where: {
          courseId: course.id,
          isPublic: true
        },
        _count: {
          rating: true
        }
      })

      const totalReviews = ratingStats.reduce((sum, stat) => sum + stat._count.rating, 0)
      const averageRating = totalReviews > 0 
        ? ratingStats.reduce((sum, stat) => sum + (stat.rating * stat._count.rating), 0) / totalReviews
        : 0

      const ratingDistribution = {
        5: ratingStats.find(s => s.rating === 5)?._count.rating || 0,
        4: ratingStats.find(s => s.rating === 4)?._count.rating || 0,
        3: ratingStats.find(s => s.rating === 3)?._count.rating || 0,
        2: ratingStats.find(s => s.rating === 2)?._count.rating || 0,
        1: ratingStats.find(s => s.rating === 1)?._count.rating || 0,
      }

      return APIResponse.success({
        reviews,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: offset + limit < totalCount,
          hasPrev: page > 1
        },
        statistics: {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          ratingDistribution
        }
      })

    } catch (error) {
      console.error('Error fetching course reviews:', error)
      return APIResponse.error('Failed to fetch reviews', 500)
    }
  }
)

// POST /api/courses/[slug]/reviews - Create a review for a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: createReviewSchema
  },
  async (request: NextRequest, { validatedBody, user, params }) => {
    try {
      const resolvedParams = await params
      const courseSlug = resolvedParams?.slug as string

      if (!courseSlug) {
        return APIResponse.error('Course slug is required', 400)
      }

      const { rating, title, comment, isPublic } = validatedBody

      // Find course by slug
      const course = await prisma.course.findUnique({
        where: { 
          slug: courseSlug,
          isActive: true,
          isPublished: true
        },
        select: { id: true, title: true }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('You must be enrolled in this course to leave a review', 403)
      }

      // Check if user has already reviewed this course
      const existingReview = await prisma.courseReview.findUnique({
        where: {
          courseId_userId: {
            courseId: course.id,
            userId: user.id
          }
        }
      })

      if (existingReview) {
        return APIResponse.error('You have already reviewed this course', 400)
      }

      // Create the review
      const review = await prisma.courseReview.create({
        data: {
          courseId: course.id,
          userId: user.id,
          rating,
          title,
          comment,
          isPublic
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      return APIResponse.success({
        review,
        message: 'Review created successfully'
      })

    } catch (error) {
      console.error('Error creating course review:', error)
      return APIResponse.error('Failed to create review', 500)
    }
  }
)
