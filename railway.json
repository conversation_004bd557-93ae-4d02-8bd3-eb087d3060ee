{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run build", "watchPatterns": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "prisma/**/*"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3, "healthcheckPath": "/api/health", "healthcheckTimeout": 30}, "environments": {"production": {"variables": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "staging": {"variables": {"NODE_ENV": "staging"}}}}