import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  timeframe: z.enum(['today', 'week', 'month', 'all']).default('week'),
  status: z.enum(['WAITING', 'ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED']).optional(),
  createdBy: z.string().optional()
})

// GET /api/admin/live-quiz/analytics - Get comprehensive live quiz analytics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    const { timeframe, status, createdBy } = validatedQuery

    try {
      // Calculate date range based on timeframe
      const now = new Date()
      let startDate: Date

      switch (timeframe) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'all':
        default:
          startDate = new Date(0) // Beginning of time
          break
      }

      // Build where clause
      const where: any = {
        createdAt: { gte: startDate }
      }

      if (status) {
        where.status = status
      }

      if (createdBy) {
        where.createdBy = createdBy
      }

      // Get session overview statistics
      const sessionStats = await prisma.liveQuizSession.groupBy({
        by: ['status'],
        where,
        _count: {
          id: true
        }
      })

      // Get total sessions and participants
      const totalSessions = await prisma.liveQuizSession.count({ where })
      const totalParticipants = await prisma.liveQuizParticipant.count({
        where: {
          session: where
        }
      })

      // Get average session metrics
      const sessionMetrics = await prisma.liveQuizSession.findMany({
        where: {
          ...where,
          status: 'COMPLETED'
        },
        include: {
          participants: {
            select: {
              score: true,
              correctAnswers: true,
              totalAnswered: true,
              timeSpent: true,
              isActive: true
            }
          },
          quiz: {
            select: {
              difficulty: true,
              questions: {
                select: { id: true }
              }
            }
          }
        }
      })

      // Calculate detailed metrics
      let totalScore = 0
      let totalCorrectAnswers = 0
      let totalAnswers = 0
      let totalTimeSpent = 0
      let totalActiveParticipants = 0
      let totalQuestions = 0

      sessionMetrics.forEach(session => {
        totalQuestions += session.quiz.questions.length
        session.participants.forEach(participant => {
          if (participant.isActive) {
            totalActiveParticipants++
            totalScore += participant.score
            totalCorrectAnswers += participant.correctAnswers
            totalAnswers += participant.totalAnswered
            totalTimeSpent += participant.timeSpent
          }
        })
      })

      // Get top performing sessions
      const topSessions = await prisma.liveQuizSession.findMany({
        where: {
          ...where,
          status: 'COMPLETED'
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              difficulty: true
            }
          },
          creator: {
            select: {
              id: true,
              name: true
            }
          },
          participants: {
            where: { isActive: true },
            select: {
              score: true,
              correctAnswers: true,
              totalAnswered: true
            }
          },
          _count: {
            select: {
              participants: {
                where: { isActive: true }
              }
            }
          }
        },
        orderBy: {
          participants: {
            _count: 'desc'
          }
        },
        take: 10
      })

      // Get daily session counts for the timeframe
      const dailyStats = await prisma.$queryRaw`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as sessions,
          COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_sessions
        FROM live_quiz_sessions 
        WHERE created_at >= ${startDate}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      ` as Array<{
        date: string
        sessions: number
        completed_sessions: number
      }>

      // Get quiz difficulty distribution
      const difficultyStats = await prisma.liveQuizSession.findMany({
        where,
        include: {
          quiz: {
            select: {
              difficulty: true
            }
          }
        }
      })

      const difficultyDistribution = difficultyStats.reduce((acc, session) => {
        const difficulty = session.quiz.difficulty
        acc[difficulty] = (acc[difficulty] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      // Get average scores by difficulty
      const scoresByDifficulty = sessionMetrics.reduce((acc, session) => {
        const difficulty = session.quiz.difficulty
        if (!acc[difficulty]) {
          acc[difficulty] = { totalScore: 0, totalParticipants: 0 }
        }
        
        session.participants.forEach(participant => {
          if (participant.isActive) {
            acc[difficulty].totalScore += participant.score
            acc[difficulty].totalParticipants++
          }
        })
        
        return acc
      }, {} as Record<string, { totalScore: number, totalParticipants: number }>)

      const averageScoresByDifficulty = Object.entries(scoresByDifficulty).reduce((acc, [difficulty, data]) => {
        acc[difficulty] = data.totalParticipants > 0 ? Math.round(data.totalScore / data.totalParticipants) : 0
        return acc
      }, {} as Record<string, number>)

      // Get most active creators
      const creatorStats = await prisma.liveQuizSession.groupBy({
        by: ['createdBy'],
        where,
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 10
      })

      const creatorDetails = await prisma.user.findMany({
        where: {
          id: { in: creatorStats.map(stat => stat.createdBy) }
        },
        select: {
          id: true,
          name: true,
          email: true
        }
      })

      const topCreators = creatorStats.map(stat => {
        const creator = creatorDetails.find(c => c.id === stat.createdBy)
        return {
          creator,
          sessionCount: stat._count.id
        }
      })

      return APIResponse.success({
        overview: {
          totalSessions,
          totalParticipants,
          totalActiveParticipants,
          averageParticipantsPerSession: totalSessions > 0 ? Math.round(totalParticipants / totalSessions) : 0,
          averageScore: totalActiveParticipants > 0 ? Math.round(totalScore / totalActiveParticipants) : 0,
          averageAccuracy: totalAnswers > 0 ? Math.round((totalCorrectAnswers / totalAnswers) * 100) : 0,
          averageTimeSpent: totalActiveParticipants > 0 ? Math.round(totalTimeSpent / totalActiveParticipants) : 0,
          averageQuestionsPerSession: sessionMetrics.length > 0 ? Math.round(totalQuestions / sessionMetrics.length) : 0
        },
        sessionsByStatus: sessionStats.reduce((acc, stat) => {
          acc[stat.status] = stat._count.id
          return acc
        }, {} as Record<string, number>),
        topSessions: topSessions.map(session => ({
          id: session.id,
          title: session.title,
          quiz: session.quiz,
          creator: session.creator,
          participantCount: session._count.participants,
          averageScore: session.participants.length > 0 
            ? Math.round(session.participants.reduce((sum, p) => sum + p.score, 0) / session.participants.length)
            : 0,
          completionRate: session.participants.length > 0
            ? Math.round((session.participants.filter(p => p.totalAnswered > 0).length / session.participants.length) * 100)
            : 0,
          createdAt: session.createdAt
        })),
        dailyStats,
        difficultyDistribution,
        averageScoresByDifficulty,
        topCreators,
        timeframe,
        generatedAt: new Date()
      }, 'Live quiz analytics retrieved successfully')

    } catch (error) {
      console.error('Error fetching live quiz analytics:', error)
      return APIResponse.error('Failed to fetch live quiz analytics', 500)
    }
  }
)
