'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  AcademicCapIcon,
  UserIcon,
  Cog6ToothIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface ResponsiveLayoutProps {
  children: React.ReactNode
  userRole?: 'ADMIN' | 'STUDENT' | 'INSTRUCTOR'
  user?: {
    id: string
    name: string
    email: string
    image?: string
  }
}

interface NavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles?: ('ADMIN' | 'STUDENT' | 'INSTRUCTOR')[]
  children?: NavItem[]
}

const navigationItems: NavItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon
  },
  {
    name: 'Courses',
    href: '/courses',
    icon: AcademicCapIcon,
    children: [
      {
        name: 'Browse Courses',
        href: '/student/courses',
        icon: AcademicCapIcon,
        roles: ['STUDENT']
      },
      {
        name: 'My Courses',
        href: '/student/my-courses',
        icon: AcademicCapIcon,
        roles: ['STUDENT']
      },
      {
        name: 'Manage Courses',
        href: '/admin/courses',
        icon: AcademicCapIcon,
        roles: ['ADMIN', 'INSTRUCTOR']
      }
    ]
  },
  {
    name: 'Profile',
    href: '/profile',
    icon: UserIcon
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Cog6ToothIcon
  }
]

export default function ResponsiveLayout({ children, userRole = 'STUDENT', user }: ResponsiveLayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    // Close mobile menu when route changes
    setIsMobileMenuOpen(false)
  }, [pathname])

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemName)) {
        newSet.delete(itemName)
      } else {
        newSet.add(itemName)
      }
      return newSet
    })
  }

  const filteredNavItems = navigationItems.filter(item => 
    !item.roles || item.roles.includes(userRole)
  )

  const isActiveRoute = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/' || pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Mobile Header */}
      {isMobile && (
        <div className="lg:hidden bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-40">
          <div className="flex items-center justify-between px-4 py-3">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsMobileMenuOpen(true)}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <Bars3Icon className="w-6 h-6" />
              </button>
              <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                PreplocoS
              </h1>
            </div>
            
            {user && (
              <div className="flex items-center space-x-2">
                {user.image ? (
                  <img
                    src={user.image}
                    alt={user.name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">
                      {user.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      <div className="flex">
        {/* Desktop Sidebar */}
        {!isMobile && (
          <div className="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
            <div className="flex flex-col flex-grow bg-white/60 backdrop-blur-xl border-r border-white/20 overflow-y-auto">
              {/* Logo */}
              <div className="flex items-center flex-shrink-0 px-6 py-6">
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  PreplocoS
                </h1>
              </div>

              {/* Navigation */}
              <nav className="flex-1 px-4 pb-4 space-y-2">
                {filteredNavItems.map((item) => (
                  <NavItemComponent
                    key={item.name}
                    item={item}
                    userRole={userRole}
                    isExpanded={expandedItems.has(item.name)}
                    onToggleExpanded={() => toggleExpanded(item.name)}
                    isActive={isActiveRoute(item.href)}
                    isMobile={false}
                  />
                ))}
              </nav>

              {/* User Info */}
              {user && (
                <div className="flex-shrink-0 p-4 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    {user.image ? (
                      <img
                        src={user.image}
                        alt={user.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-800 truncate">
                        {user.name}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        {userRole.toLowerCase()}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Mobile Sidebar */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                onClick={() => setIsMobileMenuOpen(false)}
                className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden"
              />

              {/* Sidebar */}
              <motion.div
                initial={{ x: '-100%' }}
                animate={{ x: 0 }}
                exit={{ x: '-100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                className="fixed inset-y-0 left-0 z-50 w-80 bg-white shadow-xl lg:hidden"
              >
                <div className="flex flex-col h-full">
                  {/* Header */}
                  <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                      PreplocoS
                    </h1>
                    <button
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </button>
                  </div>

                  {/* Navigation */}
                  <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
                    {filteredNavItems.map((item) => (
                      <NavItemComponent
                        key={item.name}
                        item={item}
                        userRole={userRole}
                        isExpanded={expandedItems.has(item.name)}
                        onToggleExpanded={() => toggleExpanded(item.name)}
                        isActive={isActiveRoute(item.href)}
                        isMobile={true}
                      />
                    ))}
                  </nav>

                  {/* User Info */}
                  {user && (
                    <div className="p-4 border-t border-gray-200">
                      <div className="flex items-center space-x-3">
                        {user.image ? (
                          <img
                            src={user.image}
                            alt={user.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-800 truncate">
                            {user.name}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {user.email}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className={`flex-1 ${!isMobile ? 'lg:pl-64' : ''}`}>
          <main className="min-h-screen">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}

interface NavItemComponentProps {
  item: NavItem
  userRole: string
  isExpanded: boolean
  onToggleExpanded: () => void
  isActive: boolean
  isMobile: boolean
}

function NavItemComponent({ 
  item, 
  userRole, 
  isExpanded, 
  onToggleExpanded, 
  isActive,
  isMobile 
}: NavItemComponentProps) {
  const hasChildren = item.children && item.children.length > 0
  const filteredChildren = item.children?.filter(child => 
    !child.roles || child.roles.includes(userRole as any)
  )

  if (hasChildren && filteredChildren && filteredChildren.length > 0) {
    return (
      <div>
        <button
          onClick={onToggleExpanded}
          className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
            isActive
              ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
              : 'text-gray-700 hover:bg-gray-100'
          }`}
        >
          <div className="flex items-center space-x-3">
            <item.icon className="w-5 h-5" />
            <span>{item.name}</span>
          </div>
          <ChevronDownIcon 
            className={`w-4 h-4 transition-transform duration-200 ${
              isExpanded ? 'rotate-180' : ''
            }`} 
          />
        </button>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="ml-6 mt-2 space-y-1">
                {filteredChildren.map((child) => (
                  <Link
                    key={child.name}
                    href={child.href}
                    className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                      child.href === window.location.pathname
                        ? 'bg-blue-50 text-blue-700 border-l-2 border-blue-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <child.icon className="w-4 h-4" />
                    <span>{child.name}</span>
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <Link
      href={item.href}
      className={`flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
        isActive
          ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
          : 'text-gray-700 hover:bg-gray-100'
      }`}
    >
      <item.icon className="w-5 h-5" />
      <span>{item.name}</span>
    </Link>
  )
}
