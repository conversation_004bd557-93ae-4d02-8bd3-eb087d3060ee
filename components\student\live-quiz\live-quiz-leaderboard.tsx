"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Trophy, 
  Crown, 
  Medal,
  Target,
  TrendingUp,
  User
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface LeaderboardEntry {
  userId: string
  userName: string
  score: number
  rank: number
}

interface LiveQuizLeaderboardProps {
  leaderboard: LeaderboardEntry[]
  currentUserId?: string
}

export function LiveQuizLeaderboard({ leaderboard, currentUserId }: LiveQuizLeaderboardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-4 w-4 text-yellow-500" />
      case 2:
        return <Medal className="h-4 w-4 text-gray-400" />
      case 3:
        return <Medal className="h-4 w-4 text-amber-600" />
      default:
        return <span className="text-sm font-bold text-muted-foreground">#{rank}</span>
    }
  }

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "bg-gradient-to-r from-yellow-100 to-yellow-50 dark:from-yellow-900 dark:to-yellow-800 border-yellow-200 dark:border-yellow-700"
      case 2:
        return "bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-700 border-gray-200 dark:border-gray-600"
      case 3:
        return "bg-gradient-to-r from-amber-100 to-amber-50 dark:from-amber-900 dark:to-amber-800 border-amber-200 dark:border-amber-700"
      default:
        return "bg-background border-border"
    }
  }

  const isCurrentUser = (userId: string) => {
    return currentUserId === userId
  }

  const sortedLeaderboard = [...leaderboard].sort((a, b) => a.rank - b.rank)

  return (
    <Card className="glass">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <Trophy className="h-4 w-4 text-primary" />
          Live Leaderboard
        </CardTitle>
        <CardDescription>
          Real-time rankings • {leaderboard.length} participants
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-64">
          <div className="space-y-2">
            <AnimatePresence>
              {sortedLeaderboard.map((entry, index) => (
                <motion.div
                  key={entry.userId}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ delay: index * 0.05 }}
                  className={`
                    p-3 rounded-lg border transition-all duration-200
                    ${getRankColor(entry.rank)}
                    ${isCurrentUser(entry.userId) 
                      ? 'ring-2 ring-primary ring-opacity-50 shadow-md' 
                      : 'hover:shadow-sm'
                    }
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {/* Rank Icon */}
                      <div className="flex items-center justify-center w-8 h-8">
                        {getRankIcon(entry.rank)}
                      </div>

                      {/* User Info */}
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="text-xs">
                            {entry.userName.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className={`text-sm font-medium ${
                            isCurrentUser(entry.userId) ? 'text-primary' : ''
                          }`}>
                            {entry.userName}
                            {isCurrentUser(entry.userId) && (
                              <Badge variant="secondary" className="ml-2 text-xs">
                                You
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Score */}
                    <div className="text-right">
                      <div className="text-lg font-bold text-primary">
                        {entry.score}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        points
                      </div>
                    </div>
                  </div>

                  {/* Rank Change Indicator (placeholder for future enhancement) */}
                  {entry.rank <= 3 && (
                    <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
                      <TrendingUp className="h-3 w-3" />
                      <span>Top performer</span>
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>

            {leaderboard.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Trophy className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No rankings yet</p>
                <p className="text-xs">Start answering questions to see the leaderboard!</p>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Current User Summary */}
        {currentUserId && (
          <div className="mt-4 pt-3 border-t">
            <div className="text-xs text-muted-foreground mb-2">Your Performance</div>
            {(() => {
              const userEntry = leaderboard.find(entry => entry.userId === currentUserId)
              if (userEntry) {
                return (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        {getRankIcon(userEntry.rank)}
                        <span className="font-medium">Rank {userEntry.rank}</span>
                      </div>
                    </div>
                    <div className="text-primary font-bold">
                      {userEntry.score} points
                    </div>
                  </div>
                )
              } else {
                return (
                  <div className="text-sm text-muted-foreground">
                    Not ranked yet - answer questions to appear on the leaderboard!
                  </div>
                )
              }
            })()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
