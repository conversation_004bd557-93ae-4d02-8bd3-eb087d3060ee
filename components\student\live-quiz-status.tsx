"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Play,
  Pause,
  Trophy,
  Target
} from "lucide-react"
import { getSocketClient } from "@/lib/socket-client"

interface QuizProgress {
  quizId: string
  questionIndex: number
  totalQuestions: number
  timeRemaining: number
  answered: boolean
  score?: number
  participants?: number
}

interface LiveQuizStatusProps {
  quizId?: string
  onProgressUpdate?: (progress: QuizProgress) => void
}

export function LiveQuizStatus({ quizId, onProgressUpdate }: LiveQuizStatusProps) {
  const { data: session } = useSession()
  const [quizProgress, setQuizProgress] = useState<QuizProgress | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [participants, setParticipants] = useState<Array<{
    userId: string
    userName: string
    progress: number
    score: number
  }>>([])

  useEffect(() => {
    if (!quizId || !session?.user) return

    const socketClient = getSocketClient()

    // Listen for connection status
    socketClient.on('connection:established', () => {
      setIsConnected(true)
      // Join the quiz room
      socketClient.joinQuiz(quizId)
    })

    socketClient.on('connection:lost', () => {
      setIsConnected(false)
    })

    // Listen for quiz progress updates
    socketClient.on('quiz_progress_update', (progress: QuizProgress & {
      userId: string
      userName: string
    }) => {
             if (progress.userId === session.user.id) {
        // Update own progress
        setQuizProgress(progress)
        onProgressUpdate?.(progress)
      } else {
        // Update other participants
        setParticipants(prev => {
          const existing = prev.find(p => p.userId === progress.userId)
          if (existing) {
            return prev.map(p => 
              p.userId === progress.userId 
                ? { ...p, progress: (progress.questionIndex / progress.totalQuestions) * 100, score: progress.score || 0 }
                : p
            )
          } else {
            return [...prev, {
              userId: progress.userId,
              userName: progress.userName,
              progress: (progress.questionIndex / progress.totalQuestions) * 100,
              score: progress.score || 0
            }]
          }
        })
      }
    })

    // Listen for participants leaving (clean up)
    socketClient.on('user:left', (data: { userId: string }) => {
      setParticipants(prev => prev.filter(p => p.userId !== data.userId))
    })

    return () => {
      // Leave quiz room on cleanup
      if (quizId) {
        socketClient.leaveQuiz(quizId)
      }
    }
  }, [quizId, session, onProgressUpdate])

  // Send progress update
  const sendProgressUpdate = (progress: Partial<QuizProgress>) => {
    if (!quizId || !session?.user) return

    const socketClient = getSocketClient()
    socketClient.sendQuizProgress({
      quizId,
      questionIndex: progress.questionIndex || 0,
      timeRemaining: progress.timeRemaining || 0,
      answered: progress.answered || false
    })
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!quizId) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Play className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No active quiz session</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Live Quiz Session</CardTitle>
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? "Connected" : "Disconnected"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {quizProgress && (
            <div className="space-y-4">
              {/* Progress Bar */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Question {quizProgress.questionIndex + 1} of {quizProgress.totalQuestions}</span>
                  <span>{Math.round((quizProgress.questionIndex / quizProgress.totalQuestions) * 100)}% Complete</span>
                </div>
                <Progress 
                  value={(quizProgress.questionIndex / quizProgress.totalQuestions) * 100} 
                  className="h-2"
                />
              </div>

              {/* Quiz Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="text-sm font-medium">{formatTime(quizProgress.timeRemaining)}</div>
                    <div className="text-xs text-muted-foreground">Time Left</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="text-sm font-medium">{quizProgress.questionIndex}</div>
                    <div className="text-xs text-muted-foreground">Answered</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-purple-500" />
                  <div>
                    <div className="text-sm font-medium">{participants.length + 1}</div>
                    <div className="text-xs text-muted-foreground">Participants</div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {quizProgress.answered ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  )}
                  <div>
                    <div className="text-sm font-medium">
                      {quizProgress.answered ? "Answered" : "Pending"}
                    </div>
                    <div className="text-xs text-muted-foreground">Current Status</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Other Participants */}
      {participants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5" />
              Other Participants ({participants.length})
            </CardTitle>
            <CardDescription>
              See how other students are progressing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {participants.map((participant) => (
                <div key={participant.userId} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">
                        {participant.userName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium">{participant.userName}</div>
                      <div className="text-sm text-muted-foreground">
                        {Math.round(participant.progress)}% complete
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">{participant.score}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Export function to send progress updates
export const useLiveQuizProgress = (quizId: string) => {
  const { data: session } = useSession()

  const sendProgress = (progress: {
    questionIndex: number
    timeRemaining: number
    answered: boolean
    totalQuestions?: number
    score?: number
  }) => {
    if (!session?.user) return

    const socketClient = getSocketClient()
    socketClient.sendQuizProgress({
      quizId,
      questionIndex: progress.questionIndex,
      timeRemaining: progress.timeRemaining,
      answered: progress.answered
    })
  }

  return { sendProgress }
}
