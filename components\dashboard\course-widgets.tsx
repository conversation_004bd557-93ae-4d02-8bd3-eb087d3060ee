'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  GraduationCap, 
  Library, 
  Play, 
  ArrowRight, 
  BookOpen,
  TrendingUp,
  Star,
  Users,
  ChevronRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'

interface EnrolledCourse {
  enrollmentId: string
  progress: number
  status: string
  course: {
    id: string
    title: string
    thumbnailImage?: string
    instructor?: string | {
      id: string
      name: string
      image?: string
    }
    slug: string
    productId: string
  }
}

interface RecommendedCourse {
  id: string
  title: string
  price: number
  rating?: number
  studentsCount?: number
  thumbnailImage?: string
  category?: string
}

interface CourseStats {
  totalEnrollments: number
  completedCourses: number
  activeCourses: number
  averageProgress: number
}

export function EnrolledCoursesWidget() {
  const [courses, setCourses] = useState<EnrolledCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchEnrolledCourses = async () => {
      try {
        const response = await fetch('/api/courses/my-courses?limit=3')
        if (response.ok) {
          const result = await response.json()
          const data = result.data || result
          setCourses(data.enrollments || [])
        }
      } catch (error) {
        console.error('Error fetching enrolled courses:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchEnrolledCourses()
  }, [])

  if (isLoading) {
    return <EnrolledCoursesWidgetSkeleton />
  }

  return (
    <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Library className="h-5 w-5 text-purple-600" />
            My Courses
          </CardTitle>
          <Button variant="outline" size="sm" asChild>
            <Link href="/student/my-courses">
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {courses.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <GraduationCap className="h-8 w-8 text-purple-600" />
            </div>
            <p className="text-muted-foreground mb-4">No enrolled courses yet</p>
            <Button asChild className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
              <Link href="/student/courses">
                Browse Courses
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {courses.map((enrollment) => (
              <motion.div
                key={enrollment.enrollmentId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center gap-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                onClick={() => {
                  window.location.href = `/student/courses/${enrollment.course.slug}`
                }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  {enrollment.course.thumbnailImage ? (
                    <img
                      src={enrollment.course.thumbnailImage}
                      alt={enrollment.course.title}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <BookOpen className="h-6 w-6 text-white" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm line-clamp-1">{enrollment.course.title}</h4>
                  {enrollment.course.instructor && (
                    <p className="text-xs text-muted-foreground">
                      {typeof enrollment.course.instructor === 'string'
                        ? enrollment.course.instructor
                        : enrollment.course.instructor.name}
                    </p>
                  )}
                  <div className="mt-2">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">{enrollment.progress}%</span>
                    </div>
                    <Progress value={enrollment.progress} className="h-1" />
                  </div>
                </div>
                
                <Button size="sm" variant="ghost" className="flex-shrink-0">
                  <Play className="h-4 w-4" />
                </Button>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function CourseStatsWidget() {
  const [stats, setStats] = useState<CourseStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchCourseStats = async () => {
      try {
        const response = await fetch('/api/courses/my-courses?limit=1')
        if (response.ok) {
          const result = await response.json()
          const data = result.data || result
          setStats(data.summary || null)
        }
      } catch (error) {
        console.error('Error fetching course stats:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCourseStats()
  }, [])

  if (isLoading) {
    return <CourseStatsWidgetSkeleton />
  }

  if (!stats) {
    return null
  }

  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <BookOpen className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Enrolled</p>
              <p className="text-lg font-bold">{stats.totalEnrollments}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Avg Progress</p>
              <p className="text-lg font-bold">{stats.averageProgress}%</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function RecommendedCoursesWidget() {
  const [courses, setCourses] = useState<RecommendedCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchRecommendedCourses = async () => {
      try {
        const response = await fetch('/api/courses?limit=3')
        if (response.ok) {
          const result = await response.json()
          const data = result.data || result
          setCourses(data.courses || [])
        }
      } catch (error) {
        console.error('Error fetching recommended courses:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchRecommendedCourses()
  }, [])

  if (isLoading) {
    return <RecommendedCoursesWidgetSkeleton />
  }

  return (
    <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <GraduationCap className="h-5 w-5 text-blue-600" />
            Recommended Courses
          </CardTitle>
          <Button variant="outline" size="sm" asChild>
            <Link href="/student/courses">
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {courses.map((course) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="flex items-center gap-4 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors cursor-pointer"
              onClick={() => window.location.href = `/student/courses`}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                {course.thumbnailImage ? (
                  <img
                    src={course.thumbnailImage}
                    alt={course.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <BookOpen className="h-6 w-6 text-white" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm line-clamp-1">{course.title}</h4>
                <Badge variant="secondary" className="text-xs mt-1">
                  {course.category || 'Professional Course'}
                </Badge>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-sm font-bold text-blue-600">₹{course.price}</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs">{course.rating || '4.5'}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {course.studentsCount ? `${course.studentsCount}+` : 'Popular'}
                    </span>
                  </div>
                </div>
              </div>
              
              <Button size="sm" variant="ghost" className="flex-shrink-0">
                <ArrowRight className="h-4 w-4" />
              </Button>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function EnrolledCoursesWidgetSkeleton() {
  return (
    <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-8 w-20" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4">
              <Skeleton className="w-12 h-12 rounded-lg" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-1 w-full" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function CourseStatsWidgetSkeleton() {
  return (
    <div className="grid grid-cols-2 gap-4">
      {Array.from({ length: 2 }).map((_, i) => (
        <Card key={i} className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Skeleton className="w-8 h-8 rounded-lg" />
              <div className="space-y-1">
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-5 w-8" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function RecommendedCoursesWidgetSkeleton() {
  return (
    <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-8 w-20" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center gap-4">
              <Skeleton className="w-12 h-12 rounded-lg" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <Skeleton className="h-3 w-2/3" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
