"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Clock, 
  ChevronLeft, 
  ChevronRight, 
  Flag,
  Pause,
  Play,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Save,
  RotateCcw,
  BookOpen,
  Target,
  Timer,
  HelpCircle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { StudentError<PERSON><PERSON><PERSON>, StudentAPIError } from "@/lib/student-error-handler"
import { QuizTimer } from "@/components/student/quiz-timer"
import { QuestionRenderer } from "@/components/student/question-renderer"
import { QuizKeyboardShortcuts } from "@/components/accessibility/keyboard-shortcuts"
import { useLiveRegion } from "@/components/accessibility/live-region"
import { QuizNavigation } from "@/components/student/quiz-navigation"
import { LiveQuizStatus, useLiveQuizProgress } from "@/components/student/live-quiz-status"
import { getSocketClient } from "@/lib/socket-client"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"

interface Question {
  id: string
  type: 'MCQ' | 'TRUE_FALSE' | 'FILL_BLANK' | 'ESSAY'
  text: string
  options?: string[]
  correctAnswer?: string
  explanation?: string
  points: number
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  tags?: string[]
  imageUrl?: string
  order?: number
}

interface QuizAttempt {
  id: string
  attemptId: string
  quizId: string
  quiz: {
    id: string
    title: string
    description: string
    type: string
    difficulty: string
    duration: number
    passingScore?: number
    allowPause: boolean
    showTimer: boolean
    shuffleQuestions: boolean
    showResults: boolean
    allowReview: boolean
  }
  questions: Question[]
  answers: Record<string, any>
  currentQuestionIndex: number
  timeRemaining: number | null
  isPaused: boolean
  isCompleted: boolean
  flaggedQuestions: string[]
  startedAt: string
  completedAt?: string
  score?: number
  percentage?: number
}

export default function QuizAttempt() {
  const params = useParams()
  const router = useRouter()
  const [attempt, setAttempt] = useState<QuizAttempt | null>(null)
  const [loading, setLoading] = useState(true)
  const [showInstructions, setShowInstructions] = useState(true)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const { announce: _announce } = useLiveRegion()
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Live quiz progress hook
  const quizId = Array.isArray(params.id) ? params.id[0] : params.id
  const { sendProgress } = useLiveQuizProgress(quizId || '')

  // Enhanced loading states
  const [saving, setSaving] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  // Dialog states
  const [showResumeDialog, setShowResumeDialog] = useState(false)
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)
  const [showExitDialog, setShowExitDialog] = useState(false)
  const [resumeAttemptId, setResumeAttemptId] = useState<string | null>(null)
  const [unansweredCount, setUnansweredCount] = useState(0)
  const [pauseResuming, setPauseResuming] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')

  // Check for existing paused attempts
  const checkForExistingAttempt = async () => {
    try {
      const response = await fetch(`/api/student/quizzes/${params.id}/attempt`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data.isPaused) {
          // Show resume dialog
          setResumeAttemptId(data.data.attemptId)
          setShowResumeDialog(true)
          return
        }
      }
    } catch (error) {
           }

    // Start new attempt if no paused attempt or user chose not to resume
    await initializeQuizAttempt()
  }

  const handleResumeConfirm = async () => {
    if (resumeAttemptId) {
      await initializeQuizAttempt(resumeAttemptId)
    }
    setShowResumeDialog(false)
    setResumeAttemptId(null)
  }

  const handleResumeCancel = async () => {
    setShowResumeDialog(false)
    setResumeAttemptId(null)
    // Start new attempt
    await initializeQuizAttempt()
  }

  useEffect(() => {
    checkForExistingAttempt()
  }, [params.id])

  // Auto-save answers every 30 seconds
  useEffect(() => {
    if (!attempt || !autoSaveEnabled) return

    const interval = setInterval(() => {
      saveProgress()
    }, 30000)

    return () => clearInterval(interval)
  }, [attempt, autoSaveEnabled])

  const initializeQuizAttempt = async (resumeAttemptId?: string) => {
    setLoading(true)
    try {
             // Check for existing attempt or create new one
      const response = await fetch(`/api/student/quizzes/${params.id}/attempt`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          resumeAttemptId
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        if (errorData.code === 'MAX_ATTEMPTS_EXCEEDED') {
          const message = StudentErrorHandler.getUserFriendlyMessage(response.status, errorData)
          toast.error(message)
          router.push('/student/dashboard')
          return
        }

        throw new StudentAPIError(
          StudentErrorHandler.getUserFriendlyMessage(response.status, errorData),
          response.status,
          errorData.code
        )
      }
      
      const data = await response.json()

      if (data.success) {
        // Use real data from API
                 const apiAttempt: QuizAttempt = {
          id: data.data.attemptId,
          attemptId: data.data.attemptId,
          quizId: params.id as string,
          quiz: {
            id: params.id as string,
            title: data.data.quiz?.title || 'Quiz',
            description: data.data.quiz?.description || '',
            type: data.data.quiz?.type || 'QUIZ',
            difficulty: data.data.quiz?.difficulty || 'MEDIUM',
            duration: data.data.timeLimit || data.data.quiz?.timeLimit || 0,
            passingScore: data.data.quiz?.passingScore || 0,
            allowPause: true, // Enable pause functionality
            showTimer: (data.data.timeLimit || data.data.quiz?.timeLimit || 0) > 0,
            shuffleQuestions: false,
            showResults: true,
            allowReview: true
          },
          questions: data.data.questions || [],
          answers: data.data.answers || {},
          currentQuestionIndex: 0,
          timeRemaining: (data.data.timeLimit || data.data.quiz?.timeLimit) ? (data.data.timeLimit || data.data.quiz?.timeLimit) * 60 : null,
          isPaused: data.data.isPaused || false,
          isCompleted: data.data.isCompleted || false,
          flaggedQuestions: [],
          startedAt: data.data.startedAt || new Date().toISOString()
        }

        setAttempt(apiAttempt)
      } else {
        throw new Error(data.message || 'Failed to initialize quiz attempt')
      }
    } catch (error) {
      StudentErrorHandler.handleError(
        error as Error,
        'Initializing quiz attempt',
        {
          showToast: true,
          fallbackMessage: 'Failed to start quiz'
        }
      )

      // Only redirect on certain errors
      if (error instanceof StudentAPIError &&
          ['MAX_ATTEMPTS_EXCEEDED', 'QUIZ_NOT_FOUND', 'UNAUTHORIZED'].includes(error.code || '')) {
        router.push('/student/browse')
      }
    } finally {
      setLoading(false)
    }
  }

  const saveProgress = useCallback(async () => {
    if (!attempt) return

    setSaveStatus('saving')
    setSaving(true)

    try {
      const response = await fetch(`/api/student/quizzes/${params.id}/attempt/${attempt.attemptId}/save`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          answers: attempt.answers,
          currentQuestionIndex: attempt.currentQuestionIndex,
          timeRemaining: attempt.timeRemaining,
          flaggedQuestions: attempt.flaggedQuestions
        })
      })

      if (response.ok) {
        setLastSaved(new Date())
        setSaveStatus('saved')
        // Reset to idle after showing saved status briefly
        setTimeout(() => setSaveStatus('idle'), 2000)
      } else {
        setSaveStatus('error')
        setTimeout(() => setSaveStatus('idle'), 3000)
      }
    } catch (error) {
      console.error('Error saving progress:', error)
      setSaveStatus('error')
      setTimeout(() => setSaveStatus('idle'), 3000)
    } finally {
      setSaving(false)
    }
  }, [attempt])

  const handleAnswerChange = (questionId: string, answer: any) => {
    if (!attempt) return

    setAttempt(prev => {
      const updated = {
        ...prev!,
        answers: {
          ...prev!.answers,
          [questionId]: answer
        }
      }

      // Send live progress update
      sendProgress({
        questionIndex: updated.currentQuestionIndex,
        timeRemaining: updated.timeRemaining || 0,
        answered: !!answer,
        totalQuestions: updated.questions.length
      })

      return updated
    })

    // Save answer immediately to ensure it's not lost
    setTimeout(() => {
      saveProgress()
    }, 100) // Small delay to ensure state update completes
  }

  const handleQuestionNavigation = (index: number) => {
    if (!attempt) return

    setAttempt(prev => {
      const updated = {
        ...prev!,
        currentQuestionIndex: index
      }

      // Send live progress update
      const currentQuestion = updated.questions[index]
      sendProgress({
        questionIndex: index,
        timeRemaining: updated.timeRemaining || 0,
        answered: !!updated.answers[currentQuestion?.id],
        totalQuestions: updated.questions.length
      })

      return updated
    })
  }

  const handleFlagQuestion = (questionId: string) => {
    if (!attempt) return

    setAttempt(prev => ({
      ...prev!,
      flaggedQuestions: prev!.flaggedQuestions.includes(questionId)
        ? prev!.flaggedQuestions.filter(id => id !== questionId)
        : [...prev!.flaggedQuestions, questionId]
    }))
  }

  const handlePauseResume = async () => {
    if (!attempt || !attempt.quiz.allowPause) return

    const newPausedState = !attempt.isPaused
    setPauseResuming(true)

    try {
             const response = await fetch(`/api/student/quizzes/${params.id}/attempt`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          isPaused: newPausedState,
          attemptId: attempt.attemptId
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to ${newPausedState ? 'pause' : 'resume'} quiz`)
      }

      const data = await response.json()
             // Update local state
      setAttempt(prev => ({
        ...prev!,
        isPaused: newPausedState
      }))

      toast.success(newPausedState ? 'Quiz paused' : 'Quiz resumed')
    } catch (error) {
      console.error('Error pausing/resuming quiz:', error)
      toast.error(error instanceof Error ? error.message : `Failed to ${newPausedState ? 'pause' : 'resume'} quiz`)
    } finally {
      setPauseResuming(false)
    }
  }

  const handleTimeUpdate = (timeRemaining: number) => {
    if (!attempt) return

    setAttempt(prev => ({
      ...prev!,
      timeRemaining
    }))
  }

  const handleTimeExpired = () => {
    if (!attempt) return
    
    toast.warning('Time expired! Submitting quiz automatically.')
    handleSubmitQuiz(true)
  }

  const handleSubmitQuiz = async (autoSubmit = false) => {
    if (!attempt || submitting) return

    const currentUnansweredCount = attempt.questions.length - Object.keys(attempt.answers).length

    if (!autoSubmit && currentUnansweredCount > 0) {
      setUnansweredCount(currentUnansweredCount)
      setShowSubmitDialog(true)
      return
    }

    await performSubmit()
  }

  const performSubmit = async () => {
    if (!attempt || submitting) return

    setSubmitting(true)

    try {
      // Save current answers before completing the quiz
             toast.info('Saving your answers...')
      await saveProgress()

      // Small delay to ensure save completes
      await new Promise(resolve => setTimeout(resolve, 500))

      toast.info('Submitting quiz...')
      const response = await fetch(`/api/student/quizzes/${params.id}/attempt/${attempt.attemptId}/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) throw new Error('Failed to submit quiz')

      const result = await response.json()
      toast.success('Quiz submitted successfully!')

      // Send live quiz completion notification
      const socketClient = getSocketClient()
      if (socketClient.isConnected()) {
        socketClient.completeQuiz({
          quizId: attempt.quizId,
          score: result.data?.percentage || 0,
          timeSpent: (attempt.quiz.duration * 60) - (attempt.timeRemaining || 0),
          rank: result.data?.rank
        })
      }

      // Navigate to results page
      router.push(`/student/quiz/${attempt.quizId}/result/${attempt.attemptId}`)

    } catch (error) {
      console.error('Error submitting quiz:', error)
      toast.error('Failed to submit quiz')
    } finally {
      setSubmitting(false)
    }
  }

  const handleSubmitConfirm = async () => {
    setShowSubmitDialog(false)
    await performSubmit()
  }

  const handleSubmitCancel = () => {
    setShowSubmitDialog(false)
  }

  const handleExitConfirm = async () => {
    setShowExitDialog(false)
    await saveProgress()
    router.push('/student/browse')
  }

  const handleExitCancel = () => {
    setShowExitDialog(false)
  }

  const currentQuestion = attempt?.questions[attempt.currentQuestionIndex]
  const progress = attempt ? ((attempt.currentQuestionIndex + 1) / attempt.questions.length) * 100 : 0
  const answeredCount = attempt ? Object.keys(attempt.answers).length : 0

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading quiz...</p>
        </div>
      </div>
    )
  }

  if (!attempt) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <XCircle className="h-16 w-16 text-destructive mx-auto" />
              <h3 className="text-xl font-semibold">Quiz Not Available</h3>
              <p className="text-muted-foreground">
                Unable to load the quiz. Please try again later.
              </p>
              <Button onClick={() => router.push('/student/browse')}>
                Back to Browse
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show instructions before starting
  if (showInstructions) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <Card className="max-w-2xl w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6" />
              {attempt.quiz.title}
            </CardTitle>
            <CardDescription>
              Please read the instructions carefully before starting
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Timer className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="font-semibold">
                  {attempt.quiz.duration > 0 ? `${attempt.quiz.duration} minutes` : 'No time limit'}
                </div>
                <div className="text-sm text-muted-foreground">Time Limit</div>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="font-semibold">{attempt.questions.length} questions</div>
                <div className="text-sm text-muted-foreground">Total Questions</div>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <CheckCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <div className="font-semibold">
                  {(attempt.quiz.passingScore || 0) > 0 ? `${attempt.quiz.passingScore}%` : 'No minimum'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {(attempt.quiz.passingScore || 0) > 0 ? 'Minimum to Pass' : 'Passing Score'}
                </div>
                {(attempt.quiz.passingScore || 0) > 0 && (
                  <div className="text-xs text-purple-600 mt-1 font-medium">
                    Score {attempt.quiz.passingScore}% or higher to pass
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Instructions:</h3>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                  <span>Read each question carefully before selecting your answer</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                  <span>You can navigate between questions using the navigation panel</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                  <span>Flag questions you want to review later</span>
                </li>
                {attempt.quiz.allowPause && (
                  <li className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                    <span>You can pause the quiz if needed</span>
                  </li>
                )}
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                  <span>Your progress is automatically saved every 30 seconds</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                  <span>Make sure to submit your quiz before time runs out</span>
                </li>
              </ul>
            </div>

            <div className="flex items-center justify-between pt-4 border-t">
              <Button variant="outline" onClick={() => router.push('/student/browse')}>
                Cancel
              </Button>
              <Button onClick={() => setShowInstructions(false)} size="lg">
                Start Quiz
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <QuizKeyboardShortcuts
        onNext={attempt.currentQuestionIndex < attempt.questions.length - 1 ?
          () => handleQuestionNavigation(attempt.currentQuestionIndex + 1) : undefined}
        onPrevious={attempt.currentQuestionIndex > 0 ?
          () => handleQuestionNavigation(attempt.currentQuestionIndex - 1) : undefined}
        onSubmit={() => handleSubmitQuiz()}
        onFlag={currentQuestion ? () => handleFlagQuestion(currentQuestion.id) : undefined}
        disabled={attempt.isPaused || attempt.isCompleted}
      />
      <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    <BookOpen className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {attempt.quiz.title}
                    </h1>
                    <p className="text-lg text-muted-foreground">
                      Question {attempt.currentQuestionIndex + 1} of {attempt.questions.length}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {/* Save Status Indicator */}
                <div className="flex items-center gap-2 text-sm p-3 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-xl shadow-lg">
                  {saveStatus === 'saving' && (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="text-blue-600 font-medium">Saving...</span>
                    </>
                  )}
                  {saveStatus === 'saved' && (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-green-600 font-medium">Saved</span>
                    </>
                  )}
                  {saveStatus === 'error' && (
                    <>
                      <XCircle className="h-4 w-4 text-red-500" />
                      <span className="text-red-600 font-medium">Save failed</span>
                    </>
                  )}
                </div>

                {attempt.quiz.showTimer && attempt.timeRemaining !== null && (
                  <QuizTimer
                    timeRemaining={attempt.timeRemaining}
                    isPaused={attempt.isPaused}
                    onTimeUpdate={handleTimeUpdate}
                    onTimeExpired={handleTimeExpired}
                  />
                )}

                {attempt.quiz.allowPause && (
                  <Button
                    variant="outline"
                    onClick={handlePauseResume}
                    disabled={attempt.isCompleted || pauseResuming}
                    className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    {pauseResuming ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                        {attempt.isPaused ? 'Resuming...' : 'Pausing...'}
                      </>
                    ) : attempt.isPaused ? (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Resume
                      </>
                    ) : (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{answeredCount} of {attempt.questions.length} answered</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Question Content */}
          <div className="lg:col-span-3">
            <AnimatePresence mode="wait">
              <motion.div
                key={attempt.currentQuestionIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="shadow-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                          {currentQuestion?.type.replace('_', ' ')}
                        </Badge>
                        <Badge variant="outline" className="shadow-lg bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                          {currentQuestion?.points} point{currentQuestion?.points !== 1 ? 's' : ''}
                        </Badge>
                        <Badge variant="outline" className={`shadow-lg ${
                          currentQuestion?.difficulty === 'EASY' ? 'text-green-600 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20' :
                          currentQuestion?.difficulty === 'MEDIUM' ? 'text-yellow-600 bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20' :
                          'text-red-600 bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20'
                        }`}>
                          {currentQuestion?.difficulty}
                        </Badge>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => currentQuestion && handleFlagQuestion(currentQuestion.id)}
                        className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all duration-300 shadow-lg"
                      >
                        <Flag className={`h-4 w-4 ${
                          currentQuestion && attempt.flaggedQuestions.includes(currentQuestion.id)
                            ? 'fill-red-500 text-red-500'
                            : 'text-muted-foreground'
                        }`} />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {currentQuestion && (
                      <QuestionRenderer
                        question={currentQuestion}
                        answer={attempt.answers[currentQuestion.id]}
                        onAnswerChange={(answer) => handleAnswerChange(currentQuestion.id, answer)}
                        disabled={attempt.isPaused || attempt.isCompleted}
                      />
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Controls */}
            <div className="flex items-center justify-between mt-8 p-6 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-800/20 shadow-xl">
              <Button
                variant="outline"
                onClick={() => handleQuestionNavigation(attempt.currentQuestionIndex - 1)}
                disabled={attempt.currentQuestionIndex === 0 || attempt.isPaused}
                className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={saveProgress}
                  className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Progress
                </Button>

                {lastSaved && (
                  <span className="text-xs text-muted-foreground p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    Last saved: {lastSaved.toLocaleTimeString()}
                  </span>
                )}
              </div>

              {attempt.currentQuestionIndex === attempt.questions.length - 1 ? (
                <Button
                  onClick={() => handleSubmitQuiz()}
                  disabled={attempt.isPaused || submitting}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    'Submit Quiz'
                  )}
                </Button>
              ) : (
                <Button
                  onClick={() => handleQuestionNavigation(attempt.currentQuestionIndex + 1)}
                  disabled={attempt.isPaused}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>

          {/* Navigation Panel */}
          <div className="lg:col-span-1">
            <QuizNavigation
              questions={attempt.questions}
              answers={attempt.answers}
              flaggedQuestions={attempt.flaggedQuestions}
              currentQuestionIndex={attempt.currentQuestionIndex}
              onQuestionSelect={handleQuestionNavigation}
              disabled={attempt.isPaused}
            />

            {/* Live Quiz Status */}
            <div className="mt-6">
              <LiveQuizStatus
                quizId={Array.isArray(params.id) ? params.id[0] : params.id}
                onProgressUpdate={(_progress) => {
                  // Update live quiz progress when question changes
                  if (attempt && quizId) {
                    sendProgress({
                      questionIndex: attempt.currentQuestionIndex,
                      timeRemaining: attempt.timeRemaining || 0,
                      answered: !!attempt.answers[attempt.questions[attempt.currentQuestionIndex]?.id],
                      totalQuestions: attempt.questions.length,
                      score: attempt.score
                    })
                  }
                }}
              />
            </div>

            {/* Quiz Actions */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-base">Quiz Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => setAutoSaveEnabled(!autoSaveEnabled)}
                >
                  {autoSaveEnabled ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                  Auto-save: {autoSaveEnabled ? 'On' : 'Off'}
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => handleSubmitQuiz()}
                  disabled={attempt.isPaused || submitting}
                >
                  {submitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Submit Quiz
                    </>
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  className="w-full justify-start text-destructive"
                  onClick={() => setShowExitDialog(true)}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Exit Quiz
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

