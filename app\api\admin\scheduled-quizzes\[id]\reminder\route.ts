import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const sendReminderSchema = z.object({
  message: z.string().min(1, "Message is required"),
  sendToAll: z.boolean().default(true),
  userIds: z.array(z.string()).optional()
})

// POST /api/admin/scheduled-quizzes/[id]/reminder - Send reminder to participants
export const POST = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN', validateBody: sendReminderSchema }, async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    const { id } = await params
    const validatedData = (request as any).validatedBody

    // Get the scheduled quiz with enrollments
    const scheduledQuiz = await prisma.scheduledQuiz.findUnique({
      where: { id },
      include: {
        quiz: {
          select: {
            id: true,
            title: true
          }
        },
        enrollments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    })

    if (!scheduledQuiz) {
      return NextResponse.json(
        { error: 'Scheduled quiz not found' },
        { status: 404 }
      )
    }

    // Determine which users to send reminders to
    let targetUsers = scheduledQuiz.enrollments.map(e => e.user)
    
    if (!validatedData.sendToAll && validatedData.userIds) {
      targetUsers = targetUsers.filter(user => validatedData.userIds!.includes(user.id))
    }

    if (targetUsers.length === 0) {
      return NextResponse.json(
        { error: 'No users selected for reminder' },
        { status: 400 }
      )
    }

    // Create notification
    const notification = await prisma.notification.create({
      data: {
        title: `Quiz Reminder: ${scheduledQuiz.title}`,
        message: validatedData.message,
        type: 'QUIZ_REMINDER',
        category: 'quiz',
        actionUrl: `/student/quiz/${scheduledQuiz.quizId}`,
        priority: 'normal',
        scheduledAt: new Date()
      }
    })

    // Create user notifications for each target user
    await prisma.userNotification.createMany({
      data: targetUsers.map(user => ({
        userId: user.id,
        notificationId: notification.id
      }))
    })

    // Log the reminder activity
    await prisma.systemSetting.upsert({
      where: { key: `reminder_log_${id}_${Date.now()}` },
      update: {},
      create: {
        key: `reminder_log_${id}_${Date.now()}`,
        value: JSON.stringify({
          scheduledQuizId: id,
           sentBy: (request as any).session.user.id,
          sentAt: new Date(),
          recipientCount: targetUsers.length,
          message: validatedData.message
        }),
        category: 'reminder_logs',
        description: `Reminder sent for scheduled quiz: ${scheduledQuiz.title}`
      }
    })

    return NextResponse.json({
      success: true,
      message: `Reminder sent to ${targetUsers.length} participant(s)`,
      recipientCount: targetUsers.length,
      recipients: targetUsers.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email
      }))
    })
  } catch (error) {
    console.error('Error sending reminder:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to send reminder' },
      { status: 500 }
    )
  }
})
