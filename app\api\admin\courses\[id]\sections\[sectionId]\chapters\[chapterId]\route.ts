import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateChapterSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional(),
   
  isPublished: z.boolean().optional()
})

// GET /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId] - Get chapter
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      const chapter = await prisma.courseChapter.findFirst({
        where: {
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        },
        include: {
          lessons: {
            orderBy: { updatedAt: 'asc' },
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              isPublished: true,
              isFree: true
            }
          }
        }
      })

      if (!chapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      return APIResponse.success({ chapter })
    } catch (error) {
      console.error('Error fetching chapter:', error)
      return APIResponse.error('Failed to fetch chapter', 500)
    }
  }
)

// PUT /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId] - Update chapter
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateChapterSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      // Verify chapter exists
      const existingChapter = await prisma.courseChapter.findFirst({
        where: {
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        }
      })

      if (!existingChapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      // Update chapter
      const chapter = await prisma.courseChapter.update({
        where: { id: chapterId },
        data: validatedBody,
        include: {
          lessons: {
            orderBy: { updatedAt: 'asc' },
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              isPublished: true,
              isFree: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Chapter updated successfully',
        chapter
      })
    } catch (error) {
      console.error('Error updating chapter:', error)
      return APIResponse.error('Failed to update chapter', 500)
    }
  }
)

// DELETE /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId] - Delete chapter
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string

      if (!courseId || !sectionId || !chapterId) {
        return APIResponse.error('Course ID, Section ID, and Chapter ID are required', 400)
      }

      // Verify chapter exists
      const existingChapter = await prisma.courseChapter.findFirst({
        where: {
          id: chapterId,
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        }
      })

      if (!existingChapter) {
        return APIResponse.error('Chapter not found', 404)
      }

      // Delete chapter (this will cascade delete lessons)
      await prisma.courseChapter.delete({
        where: { id: chapterId }
      })

      return APIResponse.success({
        message: 'Chapter deleted successfully'
      })
    } catch (error) {
      console.error('Error deleting chapter:', error)
      return APIResponse.error('Failed to delete chapter', 500)
    }
  }
)
