/**
 * Mission Notification Service
 * 
 * Handles push notifications for mission milestones, achievements, and rewards.
 */

import { prisma } from '@/lib/prisma'

export interface NotificationData {
  userId: string
  type: 'mission_completed' | 'achievement_unlocked' | 'milestone_reached' | 'streak_achieved'
  title: string
  message: string
  data?: Record<string, any>
  priority: 'low' | 'normal' | 'high'
}

// Map mission notification types to Prisma NotificationType enum
function mapNotificationType(type: NotificationData['type']): string {
  const typeMap = {
    'mission_completed': 'QUIZ_COMPLETED',
    'achievement_unlocked': 'ACHIEVEMENT_UNLOCKED',
    'milestone_reached': 'STREAK_MILESTONE',
    'streak_achieved': 'STREAK_MILESTONE'
  }
  return typeMap[type] || 'ANNOUNCEMENT'
}

export interface MissionNotificationContext {
  missionId: string
  missionTitle: string
  courseId: string
  courseTitle: string
  pointsEarned: number
  completionRate: number
  isFirstMission?: boolean
  streakCount?: number
}

export class MissionNotificationService {
  /**
   * Send notification for mission completion
   */
  static async notifyMissionCompleted(
    userId: string, 
    context: MissionNotificationContext
  ): Promise<void> {
    try {
      const notifications: NotificationData[] = []
      
      // Basic mission completion notification
      notifications.push({
        userId,
        type: 'mission_completed',
        title: '🎯 Mission Completed!',
        message: `You've completed "${context.missionTitle}" and earned ${context.pointsEarned} points!`,
        data: {
          missionId: context.missionId,
          courseId: context.courseId,
          pointsEarned: context.pointsEarned
        },
        priority: 'normal'
      })
      
      // Special notification for first mission
      if (context.isFirstMission) {
        notifications.push({
          userId,
          type: 'milestone_reached',
          title: '🚀 Welcome to Your Learning Journey!',
          message: 'Congratulations on completing your first mission! Keep up the great work!',
          data: {
            milestone: 'first_mission',
            missionId: context.missionId
          },
          priority: 'high'
        })
      }
      
      // Streak notifications
      if (context.streakCount && context.streakCount > 1) {
        const streakEmoji = this.getStreakEmoji(context.streakCount)
        notifications.push({
          userId,
          type: 'streak_achieved',
          title: `${streakEmoji} ${context.streakCount} Mission Streak!`,
          message: `You're on fire! ${context.streakCount} missions completed in a row!`,
          data: {
            streakCount: context.streakCount,
            type: 'mission_streak'
          },
          priority: context.streakCount >= 5 ? 'high' : 'normal'
        })
      }
      
      // Send all notifications
      for (const notification of notifications) {
        await this.sendNotification(notification)
      }
      
    } catch (error) {
      console.error('Error sending mission completion notifications:', error)
    }
  }
  
  /**
   * Send notification for achievement unlock
   */
  static async notifyAchievementUnlocked(
    userId: string,
    achievement: {
      id: string
      title: string
      description: string
      icon: string
      rarity: string
      points: number
    }
  ): Promise<void> {
    try {
      const rarityEmoji = this.getRarityEmoji(achievement.rarity)
      
      await this.sendNotification({
        userId,
        type: 'achievement_unlocked',
        title: `${rarityEmoji} Achievement Unlocked!`,
        message: `${achievement.icon} ${achievement.title} - ${achievement.description}`,
        data: {
          achievementId: achievement.id,
          rarity: achievement.rarity,
          points: achievement.points
        },
        priority: achievement.rarity === 'legendary' || achievement.rarity === 'epic' ? 'high' : 'normal'
      })
      
    } catch (error) {
      console.error('Error sending achievement notification:', error)
    }
  }
  
  /**
   * Send notification for milestone reached
   */
  static async notifyMilestoneReached(
    userId: string,
    milestone: {
      type: 'points_milestone' | 'course_completion' | 'streak_milestone'
      title: string
      message: string
      value: number
      data?: Record<string, any>
    }
  ): Promise<void> {
    try {
      const milestoneEmoji = this.getMilestoneEmoji(milestone.type)
      
      await this.sendNotification({
        userId,
        type: 'milestone_reached',
        title: `${milestoneEmoji} ${milestone.title}`,
        message: milestone.message,
        data: {
          milestoneType: milestone.type,
          value: milestone.value,
          ...milestone.data
        },
        priority: milestone.value >= 1000 ? 'high' : 'normal'
      })
      
    } catch (error) {
      console.error('Error sending milestone notification:', error)
    }
  }
  
  /**
   * Send notification to user
   */
  private static async sendNotification(notification: NotificationData): Promise<void> {
    try {
      // Store notification in database
      // First create the notification
      const createdNotification = await prisma.notification.create({
        data: {
          type: mapNotificationType(notification.type) as any,
          title: notification.title,
          message: notification.message,
          data: notification.data || {},
          priority: notification.priority,
          sentAt: new Date()
        }
      })

      // Then link it to the user
      await prisma.userNotification.create({
        data: {
          userId: notification.userId,
          notificationId: createdNotification.id,
          isRead: false
        }
      })
      
      // Push notification service integration can be added here
      // Currently using database storage and real-time socket updates
      
      // In a real implementation, you would:
      // 1. Send push notification via service like Firebase Cloud Messaging
      // 2. Send email notification if user has email notifications enabled
      // 3. Send in-app notification via WebSocket/Socket.io
      
    } catch (error) {
      console.error('Error storing notification:', error)
    }
  }
  
  /**
   * Get emoji for streak count
   */
  private static getStreakEmoji(streakCount: number): string {
    if (streakCount >= 10) return '🔥'
    if (streakCount >= 7) return '⚡'
    if (streakCount >= 5) return '🌟'
    if (streakCount >= 3) return '✨'
    return '🎯'
  }
  
  /**
   * Get emoji for achievement rarity
   */
  private static getRarityEmoji(rarity: string): string {
    switch (rarity) {
      case 'legendary': return '👑'
      case 'epic': return '💎'
      case 'rare': return '🏆'
      case 'uncommon': return '🥉'
      case 'common': return '🎖️'
      default: return '🏅'
    }
  }
  
  /**
   * Get emoji for milestone type
   */
  private static getMilestoneEmoji(type: string): string {
    switch (type) {
      case 'points_milestone': return '💰'
      case 'course_completion': return '🎓'
      case 'streak_milestone': return '🔥'
      default: return '🎯'
    }
  }
  
  /**
   * Check and send milestone notifications
   */
  static async checkMilestones(userId: string, stats: {
    totalPoints: number
    completedMissions: number
    completedCourses: number
    currentStreak: number
  }): Promise<void> {
    try {
      const milestones = []
      
      // Points milestones
      const pointMilestones = [100, 500, 1000, 2500, 5000, 10000]
      for (const milestone of pointMilestones) {
        if (stats.totalPoints >= milestone) {
          // Check if we've already sent this milestone notification
          const existing = await prisma.userNotification.findFirst({
            where: {
              userId,
              notification: {
                type: 'STREAK_MILESTONE',
                data: {
                  path: ['milestoneType'],
                  equals: 'points_milestone'
                }
              }
            }
          })
          
          if (!existing) {
            milestones.push({
              type: 'points_milestone' as const,
              title: 'Points Milestone Reached!',
              message: `You've earned ${milestone} points from missions!`,
              value: milestone
            })
          }
        }
      }
      
      // Mission completion milestones
      const missionMilestones = [5, 10, 25, 50, 100]
      for (const milestone of missionMilestones) {
        if (stats.completedMissions >= milestone) {
          milestones.push({
            type: 'points_milestone' as const,
            title: 'Mission Master!',
            message: `You've completed ${milestone} missions!`,
            value: milestone
          })
        }
      }
      
      // Send milestone notifications
      for (const milestone of milestones) {
        await this.notifyMilestoneReached(userId, milestone)
      }
      
    } catch (error) {
      console.error('Error checking milestones:', error)
    }
  }
  
  /**
   * Get user's unread notifications
   */
  static async getUnreadNotifications(userId: string) {
    try {
      return await prisma.userNotification.findMany({
        where: {
          userId,
          isRead: false
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 20
      })
    } catch (error) {
      console.error('Error getting unread notifications:', error)
      return []
    }
  }
  
  /**
   * Mark notifications as read
   */
  static async markNotificationsAsRead(userId: string, notificationIds: string[]) {
    try {
      await prisma.userNotification.updateMany({
        where: {
          userId,
          id: { in: notificationIds }
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })
    } catch (error) {
      console.error('Error marking notifications as read:', error)
    }
  }
}
