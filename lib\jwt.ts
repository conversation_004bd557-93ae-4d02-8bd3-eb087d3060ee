import crypto from 'crypto'

type Header = {
  alg: 'HS256'
  typ: 'JWT'
}

export type BasePayload = {
  iat: number
  exp: number
  [key: string]: any
}

function base64UrlEncode(input: Buffer | string): string {
  const source = Buffer.isBuffer(input) ? input : Buffer.from(input)
  return source.toString('base64').replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')
}

function base64UrlDecode(input: string): Buffer {
  input = input.replace(/-/g, '+').replace(/_/g, '/')
  const pad = input.length % 4
  if (pad) input += '='.repeat(4 - pad)
  return Buffer.from(input, 'base64')
}

export function signHmacJwt(payload: Record<string, any>, secret: string, expiresInSeconds: number): string {
  const header: Header = { alg: 'HS256', typ: 'JWT' }
  const iat = Math.floor(Date.now() / 1000)
  const exp = iat + expiresInSeconds
  const body: BasePayload = { ...payload, iat, exp }

  const headerB64 = base64UrlEncode(JSON.stringify(header))
  const payloadB64 = base64UrlEncode(JSON.stringify(body))
  const data = `${headerB64}.${payloadB64}`
  const signature = crypto.createHmac('sha256', secret).update(data).digest()
  const signatureB64 = base64UrlEncode(signature)
  return `${data}.${signatureB64}`
}

export function verifyHmacJwt<T extends BasePayload = BasePayload>(token: string, secret: string): T | null {
  try {
    const [headerB64, payloadB64, signatureB64] = token.split('.')
    if (!headerB64 || !payloadB64 || !signatureB64) return null

    const data = `${headerB64}.${payloadB64}`
    const expectedSig = crypto.createHmac('sha256', secret).update(data).digest()
    const providedSig = base64UrlDecode(signatureB64)

    if (!crypto.timingSafeEqual(expectedSig, providedSig)) return null

    const payloadJson = base64UrlDecode(payloadB64).toString('utf8')
    const payload = JSON.parse(payloadJson) as T

    const now = Math.floor(Date.now() / 1000)
    if (payload.exp && now > payload.exp) return null

    return payload
  } catch {
    return null
  }
}


