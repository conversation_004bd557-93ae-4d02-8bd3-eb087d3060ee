'use client'

import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CloudArrowUpIcon,
  PlayIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface VideoUploadProps {
  lessonId?: string
  existingVideo?: {
    id: string
    url: string
    filename: string
    originalName: string
    duration?: number
    size: number
    thumbnail?: string
  }
  onUploadComplete: (video: any) => void
  onUploadError: (error: string) => void
  disabled?: boolean
}

interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export default function VideoUpload({
  lessonId,
  existingVideo,
  onUploadComplete,
  onUploadError,
  disabled = false
}: VideoUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [videoDuration, setVideoDuration] = useState<number>(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const allowedTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/mkv'
  ]

  const maxFileSize = 100 * 1024 * 1024 // 100MB

  const handleFileSelect = (file: File) => {
    if (!allowedTypes.includes(file.type)) {
      toast.error('Invalid file type. Please select a video file.')
      return
    }

    if (file.size > maxFileSize) {
      toast.error('File size exceeds 100MB limit.')
      return
    }

    setSelectedFile(file)

    // Create preview URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)

    // Extract video duration
    const video = document.createElement('video')
    video.preload = 'metadata'
    video.onloadedmetadata = () => {
      const duration = Math.round(video.duration)
      setVideoDuration(duration)
      URL.revokeObjectURL(video.src) // Clean up
    }
    video.onerror = () => {
      console.warn('Could not extract video duration')
      setVideoDuration(0)
      URL.revokeObjectURL(video.src) // Clean up
    }
    video.src = url
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const uploadVideo = async () => {
    if (!selectedFile) return

    if (!lessonId) {
      onUploadError('Lesson must be created before uploading video')
      return
    }

    try {
      setUploading(true)
      setUploadProgress({ loaded: 0, total: selectedFile.size, percentage: 0 })

      const formData = new FormData()
      formData.append('video', selectedFile)
      formData.append('lessonId', lessonId)
      formData.append('duration', videoDuration.toString())
      formData.append('generateThumbnail', 'true')

      const xhr = new XMLHttpRequest()

      // Track upload progress
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentage = Math.round((e.loaded / e.total) * 100)
          setUploadProgress({
            loaded: e.loaded,
            total: e.total,
            percentage
          })
        }
      })

      // Handle completion
      xhr.addEventListener('load', () => {
        try {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText)
            if (response.success !== false) {
              toast.success('Video uploaded successfully!')
              onUploadComplete(response.video || response.data)
              setSelectedFile(null)
              setPreviewUrl(null)
              setUploadProgress(null)
            } else {
              throw new Error(response.message || response.error || 'Upload failed')
            }
          } else {
            let errorMessage = 'Upload failed'
            try {
              const error = JSON.parse(xhr.responseText)
              errorMessage = error.message || error.error || errorMessage
            } catch (e) {
              errorMessage = `Upload failed with status ${xhr.status}`
            }
            throw new Error(errorMessage)
          }
        } catch (error: any) {
          console.error('Upload error:', error)
          toast.error(error.message || 'Failed to upload video')
          onUploadError(error.message || 'Upload failed')
          setUploadProgress(null)
        }
      })

      // Handle errors
      xhr.addEventListener('error', () => {
        const errorMessage = 'Network error during upload'
        console.error('Network error:', errorMessage)
        toast.error(errorMessage)
        onUploadError(errorMessage)
        setUploadProgress(null)
      })

      // Handle timeout
      xhr.addEventListener('timeout', () => {
        const errorMessage = 'Upload timeout - please try again'
        console.error('Upload timeout')
        toast.error(errorMessage)
        onUploadError(errorMessage)
        setUploadProgress(null)
      })

      xhr.open('POST', '/api/admin/courses/videos/upload')
      xhr.timeout = 300000 // 5 minutes timeout for video uploads
      xhr.send(formData)
    } catch (error: any) {
      console.error('Upload error:', error)
      toast.error(error.message || 'Failed to upload video')
      onUploadError(error.message || 'Upload failed')
      setUploadProgress(null)
    } finally {
      setUploading(false)
    }
  }

  const cancelUpload = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setVideoDuration(0)
    setUploadProgress(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* Existing Video */}
      {existingVideo && !selectedFile && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Current Video</h3>
            <div className="flex items-center space-x-2 text-xs lg:text-sm text-gray-600 dark:text-gray-300">
              <PlayIcon className="w-4 h-4" />
              <span>{formatDuration(existingVideo.duration)}</span>
              <span>•</span>
              <span>{formatFileSize(existingVideo.size)}</span>
            </div>
          </div>

          <div className="aspect-video bg-gray-100 dark:bg-slate-700 rounded-xl overflow-hidden mb-4 shadow-inner">
            <video
              src={existingVideo.url}
              controls
              className="w-full h-full object-cover"
              preload="metadata"
              poster={existingVideo.thumbnail}
            >
              Your browser does not support the video tag.
            </video>
          </div>

          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="min-w-0 flex-1">
              <p className="font-medium text-gray-800 dark:text-white truncate">{existingVideo.originalName}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{existingVideo.filename}</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200 text-sm lg:text-base"
            >
              Replace Video
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Upload Area */}
      {!existingVideo || selectedFile ? (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/20 p-4 lg:p-6"
        >
          {!selectedFile ? (
            <div
              onDrop={disabled ? undefined : handleDrop}
              onDragOver={disabled ? undefined : handleDragOver}
              onDragLeave={disabled ? undefined : handleDragLeave}
              className={`border-2 border-dashed rounded-xl p-6 lg:p-8 text-center transition-all duration-200 ${
                disabled
                  ? 'border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800/50 opacity-50 cursor-not-allowed'
                  : dragOver
                  ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
              }`}
            >
              <CloudArrowUpIcon className="w-10 lg:w-12 h-10 lg:h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-base lg:text-lg font-semibold text-gray-800 dark:text-white mb-2">
                Upload Video
              </h3>
              <p className="text-sm lg:text-base text-gray-600 dark:text-gray-300 mb-4">
                {disabled
                  ? 'Save the lesson first to enable video upload'
                  : 'Drag and drop your video file here, or click to browse'
                }
              </p>
              <motion.button
                whileHover={disabled ? {} : { scale: 1.02 }}
                whileTap={disabled ? {} : { scale: 0.98 }}
                onClick={disabled ? undefined : () => fileInputRef.current?.click()}
                disabled={disabled}
                className={`px-4 lg:px-6 py-2 lg:py-3 rounded-xl font-medium transition-all duration-200 text-sm lg:text-base ${
                  disabled
                    ? 'bg-gray-300 dark:bg-slate-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg hover:shadow-xl'
                }`}
              >
                {disabled ? 'Create Lesson First' : 'Choose Video File'}
              </motion.button>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                Supported formats: MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV<br />
                Maximum file size: 100MB
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* File Preview */}
              <div className="flex items-center justify-between p-3 lg:p-4 bg-gray-50/50 dark:bg-slate-700/50 rounded-xl">
                <div className="flex items-center space-x-3 min-w-0 flex-1">
                  <PlayIcon className="w-6 lg:w-8 h-6 lg:h-8 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-800 dark:text-white text-sm lg:text-base truncate">{selectedFile.name}</p>
                    <p className="text-xs lg:text-sm text-gray-600 dark:text-gray-300">
                      {formatFileSize(selectedFile.size)} • {formatDuration(videoDuration)}
                    </p>
                  </div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={cancelUpload}
                  className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-600 rounded-lg transition-colors duration-200 flex-shrink-0"
                >
                  <XMarkIcon className="w-4 lg:w-5 h-4 lg:h-5" />
                </motion.button>
              </div>

              {/* Video Preview */}
              {previewUrl && (
                <div className="aspect-video bg-gray-100 rounded-xl overflow-hidden">
                  <video
                    src={previewUrl}
                    controls
                    className="w-full h-full object-cover"
                    preload="metadata"
                  />
                </div>
              )}

              {/* Upload Progress */}
              <AnimatePresence>
                {uploadProgress && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Uploading...</span>
                      <span className="font-medium text-blue-600">
                        {uploadProgress.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${uploadProgress.percentage}%` }}
                        className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full transition-all duration-300"
                      />
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatFileSize(uploadProgress.loaded)} of {formatFileSize(uploadProgress.total)}</span>
                      <span>
                        {uploadProgress.percentage === 100 ? 'Processing...' : 'Uploading...'}
                      </span>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Upload Button */}
              {!uploading && (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={uploadVideo}
                  className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  Upload Video
                </motion.button>
              )}
            </div>
          )}
        </motion.div>
      ) : null}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        onChange={handleFileInputChange}
        disabled={disabled}
        className="hidden"
      />
    </div>
  )
}
