const { PrismaClient } = require('@prisma/client');

async function checkRoadmap() {
  const prisma = new PrismaClient();
  
  try {
    // Check the current values by ID
    const course = await prisma.course.findUnique({
      where: { id: 'cmdq81wbn0005kx94o23xwb7f' },
      select: {
        id: true,
        title: true,
        slug: true,
        hasRoadmap: true,
        roadmapTitle: true,
        roadmapDescription: true,
        isActive: true,
        isPublished: true
      }
    });
    
         
    // Also check by slug
    const courseBySlug = await prisma.course.findUnique({
      where: { slug: 'data-science' },
      select: {
        id: true,
        title: true,
        slug: true,
        hasRoadmap: true,
        roadmapTitle: true,
        roadmapDescription: true,
        isActive: true,
        isPublished: true
      }
    });
    
         
    // Update hasRoadmap to true if it's false
    if (!course?.hasRoadmap) {
             const updated = await prisma.course.update({
        where: { id: 'cmdq81wbn0005kx94o23xwb7f' },
        data: { hasRoadmap: true }
      });
           }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRoadmap();
