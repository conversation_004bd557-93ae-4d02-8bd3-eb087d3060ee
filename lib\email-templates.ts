/**
 * Email templates for various notifications
 */

interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export class EmailTemplates {
  /**
   * Welcome email for new users
   */
  static welcome(userName: string, userRole: string): EmailTemplate {
    return {
      subject: 'Welcome to PrepLocus!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">Welcome to PrepLocus!</h1>
          </div>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #334155; margin-top: 0;">Hello ${userName}!</h2>
            <p style="color: #64748b; line-height: 1.6;">
              Welcome to PrepLocus, the AI-powered learning platform! We're excited to have you join our community.
            </p>
            <p style="color: #64748b; line-height: 1.6;">
              Your account has been created with <strong>${userRole}</strong> privileges.
            </p>
          </div>

          <div style="margin-bottom: 30px;">
            <h3 style="color: #334155;">What's Next?</h3>
            ${userRole === 'STUDENT' ? `
              <ul style="color: #64748b; line-height: 1.8;">
                <li>Browse available quizzes and test series</li>
                <li>Enroll in quizzes that interest you</li>
                <li>Track your progress and achievements</li>
                <li>Compete on the leaderboard</li>
              </ul>
            ` : `
              <ul style="color: #64748b; line-height: 1.8;">
                <li>Create your first quiz using AI generation</li>
                <li>Manage students and monitor their progress</li>
                <li>Set up quiz schedules and time limits</li>
                <li>View comprehensive analytics</li>
              </ul>
            `}
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Get Started
            </a>
          </div>

          <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
          <p style="color: #94a3b8; font-size: 14px; text-align: center;">
            This email was sent by PrepLocus. If you have any questions, please contact our support team.
          </p>
        </div>
      `,
      text: `
        Welcome to PrepLocus!
        
        Hello ${userName}!
        
        Welcome to PrepLocus, the AI-powered learning platform! We're excited to have you join our community.
        
        Your account has been created with ${userRole} privileges.
        
        ${userRole === 'STUDENT' ? `
        What's Next?
        - Browse available quizzes and test series
        - Enroll in quizzes that interest you
        - Track your progress and achievements
        - Compete on the leaderboard
        ` : `
        What's Next?
        - Create your first quiz using AI generation
        - Manage students and monitor their progress
        - Set up quiz schedules and time limits
        - View comprehensive analytics
        `}
        
        Get started: ${process.env.NEXTAUTH_URL}
        
        This email was sent by PrepLocus. If you have any questions, please contact our support team.
      `
    }
  }

  /**
   * Quiz bundle purchase confirmation
   */
  static bundlePurchased(userName: string, bundleTitle: string, bundleId: string): EmailTemplate {
    return {
      subject: `Quiz Bundle Purchase Confirmed - ${bundleTitle}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">📚 Quiz Bundle Purchase Successful!</h2>

          <p style="color: #334155;">Hello ${userName},</p>

          <p style="color: #64748b; line-height: 1.6;">
            Congratulations! You have successfully purchased the <strong>${bundleTitle}</strong> quiz bundle.
            Your payment has been processed and you now have full access to all quizzes in this bundle.
          </p>

          <div style="background: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #2563eb; margin: 20px 0;">
            <p style="margin: 0; color: #1e40af;">
              🎯 <strong>What's Included:</strong> Access to all quizzes in the bundle with detailed explanations, progress tracking, and performance analytics.
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/student/quiz-bundles/${bundleId}"
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Start Practicing Now
            </a>
          </div>

          <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #334155; margin-top: 0;">Bundle Features:</h3>
            <ul style="color: #64748b; line-height: 1.6;">
              <li>✅ Unlimited quiz attempts</li>
              <li>✅ Detailed explanations for all answers</li>
              <li>✅ Progress tracking and analytics</li>
              <li>✅ Performance insights and recommendations</li>
              <li>✅ Mobile and desktop access</li>
            </ul>
          </div>

          <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            Need help? Contact our support <NAME_EMAIL>
          </p>

          <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">

          <p style="color: #94a3b8; font-size: 12px; text-align: center;">
            This email was sent by PrepLocus. You received this because you purchased a quiz bundle.
          </p>
        </div>
      `,
      text: `
        Quiz Bundle Purchase Successful!

        Hello ${userName},

        Congratulations! You have successfully purchased the ${bundleTitle} quiz bundle.
        Your payment has been processed and you now have full access to all quizzes.

        What's Included:
        - Unlimited quiz attempts
        - Detailed explanations for all answers
        - Progress tracking and analytics
        - Performance insights and recommendations
        - Mobile and desktop access

        Start practicing: ${process.env.NEXTAUTH_URL}/student/quiz-bundles/${bundleId}

        Need help? Contact <EMAIL>

        This email was sent by PrepLocus.
      `
    }
  }

  /**
   * Course enrollment confirmation
   */
  static courseEnrolled(userName: string, courseTitle: string, courseId: string): EmailTemplate {
    return {
      subject: `Welcome to ${courseTitle} - Enrollment Confirmed!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">🎓 Course Enrollment Successful!</h2>

          <p style="color: #334155;">Hello ${userName},</p>

          <p style="color: #64748b; line-height: 1.6;">
            Congratulations! You have successfully enrolled in <strong>${courseTitle}</strong>.
            Your payment has been processed and you now have full access to all course materials.
          </p>

          <div style="background: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #2563eb; margin: 20px 0;">
            <p style="margin: 0; color: #1e40af;">
              🚀 <strong>What's Next:</strong> Start learning immediately with lifetime access to all course content, including videos, assignments, and quizzes.
            </p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/student/courses/${courseId}"
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
              Start Learning Now
            </a>
          </div>

          <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #334155; margin-top: 0;">Course Access Details:</h3>
            <ul style="color: #64748b; line-height: 1.6;">
              <li>✅ Lifetime access to all course materials</li>
              <li>✅ Access to course community and discussions</li>
              <li>✅ Certificate of completion upon finishing</li>
              <li>✅ Mobile and desktop access</li>
            </ul>
          </div>

          <p style="color: #64748b; font-size: 14px; margin-top: 30px;">
            Need help? Contact our support <NAME_EMAIL>
          </p>

          <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">

          <p style="color: #94a3b8; font-size: 12px; text-align: center;">
            This email was sent by PrepLocus. You received this because you enrolled in a course.
          </p>
        </div>
      `,
      text: `
        Course Enrollment Successful!

        Hello ${userName},

        Congratulations! You have successfully enrolled in ${courseTitle}.
        Your payment has been processed and you now have full access to all course materials.

        What's Next:
        - Start learning immediately with lifetime access
        - Access all videos, assignments, and quizzes
        - Join the course community
        - Earn your certificate upon completion

        Start learning: ${process.env.NEXTAUTH_URL}/student/courses/${courseId}

        Need help? Contact <EMAIL>

        This email was sent by PrepLocus.
      `
    }
  }

  /**
   * Quiz enrollment confirmation
   */
  static quizEnrolled(userName: string, quizTitle: string, quizId: string): EmailTemplate {
    return {
      subject: `Enrolled in Quiz: ${quizTitle}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">Quiz Enrollment Confirmed</h2>
          
          <p style="color: #334155;">Hello ${userName},</p>
          
          <p style="color: #64748b; line-height: 1.6;">
            You have successfully enrolled in the quiz: <strong>${quizTitle}</strong>
          </p>
          
          <div style="background: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #2563eb; margin: 20px 0;">
            <p style="margin: 0; color: #1e40af;">
              💡 <strong>Tip:</strong> You can start the quiz anytime from your dashboard.
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/quiz/${quizId}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Start Quiz
            </a>
          </div>
          
          <p style="color: #94a3b8; font-size: 14px;">
            Good luck with your quiz!
          </p>
        </div>
      `,
      text: `
        Quiz Enrollment Confirmed
        
        Hello ${userName},
        
        You have successfully enrolled in the quiz: ${quizTitle}
        
        You can start the quiz anytime from your dashboard.
        
        Start Quiz: ${process.env.NEXTAUTH_URL}/quiz/${quizId}
        
        Good luck with your quiz!
      `
    }
  }

  /**
   * Quiz completion notification
   */
  static quizCompleted(userName: string, quizTitle: string, score: number, percentage: number): EmailTemplate {
    const performanceMessage = percentage >= 80 ? 'Excellent work!' : 
                              percentage >= 60 ? 'Good job!' : 
                              'Keep practicing!'

    return {
      subject: `Quiz Completed: ${quizTitle} - ${percentage}%`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">Quiz Completed! 🎉</h2>
          
          <p style="color: #334155;">Hello ${userName},</p>
          
          <p style="color: #64748b; line-height: 1.6;">
            Congratulations! You have completed the quiz: <strong>${quizTitle}</strong>
          </p>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
            <h3 style="color: #334155; margin-top: 0;">Your Results</h3>
            <div style="font-size: 24px; font-weight: bold; color: ${percentage >= 80 ? '#059669' : percentage >= 60 ? '#d97706' : '#dc2626'}; margin: 10px 0;">
              ${percentage}%
            </div>
            <p style="color: #64748b; margin: 5px 0;">Score: ${score} points</p>
            <p style="color: #64748b; margin: 5px 0; font-weight: bold;">${performanceMessage}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Dashboard
            </a>
          </div>
          
          <p style="color: #94a3b8; font-size: 14px;">
            Keep up the great work and continue learning!
          </p>
        </div>
      `,
      text: `
        Quiz Completed!
        
        Hello ${userName},
        
        Congratulations! You have completed the quiz: ${quizTitle}
        
        Your Results:
        - Score: ${percentage}%
        - Points: ${score}
        - ${performanceMessage}
        
        View Dashboard: ${process.env.NEXTAUTH_URL}/dashboard
        
        Keep up the great work and continue learning!
      `
    }
  }

  /**
   * Achievement unlocked notification
   */
  static achievementUnlocked(userName: string, achievementTitle: string, achievementDescription: string): EmailTemplate {
    return {
      subject: `Achievement Unlocked: ${achievementTitle}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h2 style="color: #2563eb;">🏆 Achievement Unlocked!</h2>
          </div>
          
          <p style="color: #334155;">Hello ${userName},</p>
          
          <div style="background: linear-gradient(135deg, #fbbf24, #f59e0b); padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center; color: white;">
            <div style="font-size: 48px; margin-bottom: 10px;">🏆</div>
            <h3 style="margin: 10px 0; color: white;">${achievementTitle}</h3>
            <p style="margin: 5px 0; color: rgba(255,255,255,0.9);">${achievementDescription}</p>
          </div>
          
          <p style="color: #64748b; line-height: 1.6; text-align: center;">
            Congratulations on reaching this milestone! Keep up the excellent work.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/achievements" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View All Achievements
            </a>
          </div>
        </div>
      `,
      text: `
        Achievement Unlocked!
        
        Hello ${userName},
        
        🏆 ${achievementTitle}
        ${achievementDescription}
        
        Congratulations on reaching this milestone! Keep up the excellent work.
        
        View All Achievements: ${process.env.NEXTAUTH_URL}/achievements
      `
    }
  }

  /**
   * Quiz reminder notification
   */
  static quizReminder(userName: string, quizTitle: string, quizId: string, startTime: Date): EmailTemplate {
    const timeString = startTime.toLocaleString()

    return {
      subject: `Reminder: Quiz "${quizTitle}" starts soon`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">📅 Quiz Reminder</h2>
          
          <p style="color: #334155;">Hello ${userName},</p>
          
          <p style="color: #64748b; line-height: 1.6;">
            This is a friendly reminder that the quiz <strong>${quizTitle}</strong> is starting soon.
          </p>
          
          <div style="background: #fef3c7; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;">
            <p style="margin: 0; color: #92400e;">
              ⏰ <strong>Start Time:</strong> ${timeString}
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXTAUTH_URL}/quiz/${quizId}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Go to Quiz
            </a>
          </div>
          
          <p style="color: #94a3b8; font-size: 14px;">
            Make sure you're ready and have a stable internet connection. Good luck!
          </p>
        </div>
      `,
      text: `
        Quiz Reminder
        
        Hello ${userName},
        
        This is a friendly reminder that the quiz "${quizTitle}" is starting soon.
        
        Start Time: ${timeString}
        
        Go to Quiz: ${process.env.NEXTAUTH_URL}/quiz/${quizId}
        
        Make sure you're ready and have a stable internet connection. Good luck!
      `
    }
  }
}
