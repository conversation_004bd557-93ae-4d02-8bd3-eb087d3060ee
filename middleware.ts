import { auth } from "@/auth"
import { NextResponse } from "next/server"

// Define protected routes
const protectedRoutes = ['/admin', '/dashboard', '/student']
const adminRoutes = ['/admin']
const studentRoutes = ['/student']
const authRoutes = ['/auth/signin', '/auth/signup']

export default auth((req) => {
  const { nextUrl } = req
  const isLoggedIn = !!req.auth
  const userRole = req.auth?.user?.role

  // Check if the current route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  )

  // Check if the current route requires admin access
  const isAdminRoute = adminRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  )

  // Check if the current route requires student access
  const isStudentRoute = studentRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  )

  // Check if the current route is an auth route
  const isAuthRoute = authRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  )

  // Redirect authenticated users away from auth pages
  if (isAuthRoute && isLoggedIn) {
    if (userRole === 'ADMIN') {
      return NextResponse.redirect(new URL('/admin', nextUrl))
    } else {
      return NextResponse.redirect(new URL('/student', nextUrl))
    }
  }

  // Redirect to sign-in if accessing protected route without authentication
  if (isProtectedRoute && !isLoggedIn) {
    return NextResponse.redirect(new URL('/auth/signin', nextUrl))
  }

  // Redirect to unauthorized if accessing admin route without admin role
  if (isAdminRoute && userRole !== 'ADMIN') {
    return NextResponse.redirect(new URL('/unauthorized', nextUrl))
  }

  // Redirect admins away from student routes
  if (isStudentRoute && userRole === 'ADMIN') {
    return NextResponse.redirect(new URL('/admin', nextUrl))
  }

  // Allow signed-in users to access the landing page
  // Remove automatic redirect from root path

  return NextResponse.next()
})

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
}
