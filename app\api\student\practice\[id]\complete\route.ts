import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON> } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { APIResponse } from '@/lib/api-middleware'

export const POST = createAPIHandler({ requireAuth: true, requireRole: 'STUDENT' }, async (
  request: NextRequest,
  { params, session }: { params: Promise<{ id: string }>, session: any }
) => {
  try {
    const { id } = await params
    const practiceId = id
    const body = await request.json()
    const { attemptId, answers, timeSpent } = body

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { practiceStats: true }
    })

    if (!user) {
      return APIResponse.notFound('User not found')
    }

    const practiceQuiz = await prisma.quiz.findUnique({
      where: { id: practiceId, type: 'DAILY_PRACTICE' },
      include: {
        questions: { select: { id: true, correctAnswer: true, points: true } }
      }
    })

    if (!practiceQuiz) {
      return APIResponse.notFound('Practice session not found')
    }

    const attempt = await prisma.quizAttempt.findUnique({
      where: { id: attemptId, userId: user.id, quizId: practiceId }
    })

    if (!attempt) {
      return APIResponse.notFound('Practice attempt not found')
    }

    if (attempt.completedAt) {
      return APIResponse.error('Practice session already completed', 400)
    }

    let correctAnswers = 0
    let totalPoints = 0
    let earnedPoints = 0

    practiceQuiz.questions.forEach(question => {
      totalPoints += question.points
      const userAnswer = answers[question.id]
      if (userAnswer === question.correctAnswer) {
        correctAnswers++
        earnedPoints += question.points
      }
    })

    const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0
    const practicePoints = Math.round((percentage / 100) * (practiceQuiz.points || 50))

    const completedAttempt = await prisma.quizAttempt.update({
      where: { id: attemptId },
      data: {
        answers,
        score: earnedPoints,
        percentage,
        correctAnswers,
        incorrectAnswers: practiceQuiz.questions.length - correctAnswers,
        unansweredQuestions: 0,
        completedAt: new Date()
      }
    })

    let practiceStats = user.practiceStats
    if (!practiceStats) {
      practiceStats = await prisma.userPracticeStats.create({
        data: {
          userId: user.id,
          totalSessions: 1,
          totalPoints: practicePoints,
          currentStreak: 1,
          longestStreak: 1,
          lastPracticeDate: new Date()
        }
      })
    } else {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const lastPracticeDate = practiceStats.lastPracticeDate
      let newStreak = practiceStats.currentStreak
      if (lastPracticeDate) {
        const daysSinceLastPractice = Math.floor((today.getTime() - lastPracticeDate.getTime()) / (1000 * 60 * 60 * 24))
        if (daysSinceLastPractice === 1) {
          newStreak += 1
        } else if (daysSinceLastPractice > 1) {
          newStreak = 1
        }
      } else {
        newStreak = 1
      }
      const newLongestStreak = Math.max(practiceStats.longestStreak, newStreak)
      practiceStats = await prisma.userPracticeStats.update({
        where: { id: practiceStats.id },
        data: {
          totalSessions: practiceStats.totalSessions + 1,
          totalPoints: practiceStats.totalPoints + practicePoints,
          currentStreak: newStreak,
          longestStreak: newLongestStreak,
          lastPracticeDate: new Date()
        }
      })
    }

    await prisma.user.update({
      where: { id: user.id },
      data: { points: user.points + practicePoints }
    })

    return APIResponse.success(
      {
        attempt: {
          id: completedAttempt.id,
          score: completedAttempt.score,
          percentage: completedAttempt.percentage,
          correctAnswers: completedAttempt.correctAnswers,
          totalQuestions: practiceQuiz.questions.length,
          pointsEarned: practicePoints
        },
        stats: {
          currentStreak: practiceStats.currentStreak,
          longestStreak: practiceStats.longestStreak,
          totalSessions: practiceStats.totalSessions,
          totalPoints: practiceStats.totalPoints
        }
      },
      'Practice session completed successfully'
    )

  } catch (error) {
    console.error('Error completing practice session:', error)
    return APIResponse.error('Failed to complete practice session', 500)
  }
})
