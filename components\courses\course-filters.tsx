'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown,
  SlidersHorizontal,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'

export interface CourseFilters {
  search: string
  category: string
  minPrice: number
  maxPrice: number
  sortBy: string
}

interface Category {
  name: string
  courseCount: number
  icon: string
  color: string
}

interface CourseFiltersProps {
  filters: CourseFilters
  onFiltersChange: (filters: Partial<CourseFilters>) => void
  categories: Category[]
  isLoading?: boolean
  onSync?: () => void
  className?: string
}

export function CourseFilters({
  filters,
  onFiltersChange,
  categories,
  isLoading = false,
  onSync,
  className
}: CourseFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [priceRange, setPriceRange] = useState([filters.minPrice, filters.maxPrice])

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleCategoryChange = (category: string) => {
    onFiltersChange({ category: category === 'all' ? '' : category })
  }

  const handleSortChange = (sortBy: string) => {
    onFiltersChange({ sortBy })
  }

  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange(values)
    onFiltersChange({ minPrice: values[0], maxPrice: values[1] })
  }

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      category: '',
      minPrice: 0,
      maxPrice: 10000,
      sortBy: 'popular'
    })
    setPriceRange([0, 10000])
  }

  const activeFiltersCount = [
    filters.search,
    filters.category,
    filters.minPrice > 0 || filters.maxPrice < 10000
  ].filter(Boolean).length

  return (
    <Card className={cn("bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg", className)}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Search and Quick Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search courses..."
                value={filters.search}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10 bg-white/50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="bg-white/50 dark:bg-gray-800/50"
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center">
                    {activeFiltersCount}
                  </Badge>
                )}
                <ChevronDown className={cn("h-4 w-4 ml-2 transition-transform", isExpanded && "rotate-180")} />
              </Button>
              
              {onSync && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onSync}
                  disabled={isLoading}
                  className="bg-white/50 dark:bg-gray-800/50"
                >
                  <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
                </Button>
              )}
            </div>
          </div>

          {/* Category Pills */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={!filters.category ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategoryChange('all')}
              className={cn(
                "transition-all duration-200",
                !filters.category 
                  ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white" 
                  : "bg-white/50 dark:bg-gray-800/50"
              )}
            >
              All Categories
            </Button>
            {categories.map((category) => (
              <Button
                key={category.name}
                variant={filters.category === category.name ? "default" : "outline"}
                size="sm"
                onClick={() => handleCategoryChange(category.name)}
                className={cn(
                  "transition-all duration-200",
                  filters.category === category.name
                    ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                    : "bg-white/50 dark:bg-gray-800/50"
                )}
              >
                {category.name}
                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                  {category.courseCount}
                </Badge>
              </Button>
            ))}
          </div>

          {/* Advanced Filters */}
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleContent>
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Sort By */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Sort By
                    </label>
                    <Select value={filters.sortBy} onValueChange={handleSortChange}>
                      <SelectTrigger className="bg-white/50 dark:bg-gray-800/50">
                        <SelectValue placeholder="Sort by..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="popular">Most Popular</SelectItem>
                        <SelectItem value="rating">Highest Rated</SelectItem>
                        <SelectItem value="price-low">Price: Low to High</SelectItem>
                        <SelectItem value="price-high">Price: High to Low</SelectItem>
                        <SelectItem value="newest">Newest First</SelectItem>
                        <SelectItem value="title">Title A-Z</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Price Range */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Price Range: ₹{priceRange[0]} - ₹{priceRange[1]}
                    </label>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        onValueChange={handlePriceRangeChange}
                        max={10000}
                        min={0}
                        step={100}
                        className="w-full"
                      />
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>₹0</span>
                      <span>₹10,000+</span>
                    </div>
                  </div>
                </div>

                {/* Clear Filters */}
                {activeFiltersCount > 0 && (
                  <div className="flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Clear All Filters
                    </Button>
                  </div>
                )}
              </motion.div>
            </CollapsibleContent>
          </Collapsible>

          {/* Active Filters Display */}
          {activeFiltersCount > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex flex-wrap gap-2"
            >
              {filters.search && (
                <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300">
                  Search: {filters.search}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleSearchChange('')}
                  />
                </Badge>
              )}
              {filters.category && (
                <Badge variant="secondary" className="bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300">
                  Category: {filters.category}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleCategoryChange('all')}
                  />
                </Badge>
              )}
              {(filters.minPrice > 0 || filters.maxPrice < 10000) && (
                <Badge variant="secondary" className="bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300">
                  Price: ₹{filters.minPrice} - ₹{filters.maxPrice}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handlePriceRangeChange([0, 10000])}
                  />
                </Badge>
              )}
            </motion.div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
