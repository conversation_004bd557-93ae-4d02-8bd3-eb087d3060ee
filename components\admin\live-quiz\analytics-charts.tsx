"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON>Chart,
  Line,
  Legend
} from 'recharts'

interface LiveQuizAnalyticsChartsProps {
  dailyStats: Array<{
    date: string
    sessions: number
    completed_sessions: number
  }>
  sessionsByStatus: Record<string, number>
  difficultyDistribution: Record<string, number>
  averageScoresByDifficulty: Record<string, number>
}

const COLORS = {
  primary: '#3b82f6',
  secondary: '#10b981',
  accent: '#f59e0b',
  destructive: '#ef4444',
  muted: '#6b7280'
}

const STATUS_COLORS = {
  WAITING: '#f59e0b',
  ACTIVE: '#10b981',
  PAUSED: '#f97316',
  COMPLETED: '#3b82f6',
  CANCELLED: '#ef4444'
}

const DIFFICULTY_COLORS = {
  EASY: '#10b981',
  MEDIUM: '#f59e0b',
  HARD: '#ef4444'
}

export function LiveQuizAnalyticsCharts({
  dailyStats,
  sessionsByStatus,
  difficultyDistribution,
  averageScoresByDifficulty
}: LiveQuizAnalyticsChartsProps) {
  // Prepare data for charts
  const dailyChartData = dailyStats.map(stat => ({
    date: new Date(stat.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
    sessions: stat.sessions,
    completed: stat.completed_sessions
  })).reverse()

  const statusChartData = Object.entries(sessionsByStatus).map(([status, count]) => ({
    name: status,
    value: count,
    color: STATUS_COLORS[status as keyof typeof STATUS_COLORS] || COLORS.muted
  }))

  const difficultyChartData = Object.entries(difficultyDistribution).map(([difficulty, count]) => ({
    name: difficulty,
    sessions: count,
    averageScore: averageScoresByDifficulty[difficulty] || 0,
    color: DIFFICULTY_COLORS[difficulty as keyof typeof DIFFICULTY_COLORS] || COLORS.muted
  }))

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.dataKey}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Daily Sessions Chart */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="text-base">Daily Session Activity</CardTitle>
          <CardDescription>Sessions created and completed over time</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={dailyChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="sessions" 
                stroke={COLORS.primary} 
                strokeWidth={2}
                name="Total Sessions"
                dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
              />
              <Line 
                type="monotone" 
                dataKey="completed" 
                stroke={COLORS.secondary} 
                strokeWidth={2}
                name="Completed Sessions"
                dot={{ fill: COLORS.secondary, strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Session Status Distribution */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="text-base">Session Status Distribution</CardTitle>
          <CardDescription>Breakdown of sessions by current status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusChartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {statusChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex flex-wrap justify-center gap-2 mt-4">
            {statusChartData.map((entry) => (
              <Badge 
                key={entry.name} 
                variant="outline" 
                className="text-xs"
                style={{ borderColor: entry.color, color: entry.color }}
              >
                {entry.name}: {entry.value}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Difficulty Distribution */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="text-base">Quiz Difficulty Distribution</CardTitle>
          <CardDescription>Number of sessions by quiz difficulty</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={difficultyChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="name" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="sessions" 
                name="Sessions"
                radius={[4, 4, 0, 0]}
              >
                {difficultyChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Performance by Difficulty */}
      <Card className="glass">
        <CardHeader>
          <CardTitle className="text-base">Average Scores by Difficulty</CardTitle>
          <CardDescription>Performance comparison across difficulty levels</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={difficultyChartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="name" 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis 
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="averageScore" 
                name="Average Score"
                radius={[4, 4, 0, 0]}
              >
                {difficultyChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
