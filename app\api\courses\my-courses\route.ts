import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['active', 'completed', 'paused', 'all']).optional().default('all')
})

// GET /api/courses/my-courses - Get user's enrolled courses
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, status } = validatedQuery

      // Build where clause for filtering
      const where: any = {
        userId: user.id
      }

      if (status && status !== 'all') {
        where.status = status
      }

      // Get total count for pagination
      const total = await prisma.courseEnrollment.count({ where })

      // Get enrollments with course details
      const enrollments = await prisma.courseEnrollment.findMany({
        where,
        include: {
          course: {
            include: {
              instructor: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              },
              sections: {
                where: { isPublished: true },
                include: {
                  chapters: {
                    where: { isPublished: true },
                    include: {
                      lessons: {
                        where: { isPublished: true },
                        select: { id: true, duration: true }
                      }
                    }
                  }
                }
              },
              _count: {
                select: { enrollments: true }
              }
            }
          }
        },
        orderBy: [
          { lastAccessedAt: 'desc' },
          { enrolledAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit
      })

      // Transform data for response
      const coursesWithProgress = enrollments.map(enrollment => {
        // Calculate total lessons and duration
        const totalLessons = enrollment.course.sections.reduce((acc, section) =>
          acc + section.chapters.reduce((chapterAcc, chapter) =>
            chapterAcc + chapter.lessons.length, 0), 0)

        const totalDuration = enrollment.course.sections.reduce((acc, section) =>
          acc + section.chapters.reduce((chapterAcc, chapter) =>
            chapterAcc + chapter.lessons.reduce((lessonAcc, lesson) =>
              lessonAcc + (lesson.duration || 0), 0), 0), 0)

        return {
          enrollmentId: enrollment.id,
          enrolledAt: enrollment.enrolledAt,
          progress: enrollment.progress,
          status: enrollment.status,
          lastAccessedAt: enrollment.lastAccessedAt,
          completedAt: enrollment.completedAt,
          course: {
            ...enrollment.course,
            totalLessons,
            totalDuration: Math.round(totalDuration / 60), // Convert to minutes
            continueUrl: `/student/courses/${enrollment.course.slug}`,
            sections: undefined // Remove sections from response to keep it clean
          }
        }
      })

      // Calculate summary statistics
      const totalEnrollments = await prisma.courseEnrollment.count({
        where: { userId: user.id }
      })

      const completedCourses = await prisma.courseEnrollment.count({
        where: {
          userId: user.id,
          status: 'completed'
        }
      })

      const activeCourses = await prisma.courseEnrollment.count({
        where: {
          userId: user.id,
          status: 'active'
        }
      })

      const averageProgress = await prisma.courseEnrollment.aggregate({
        where: {
          userId: user.id,
          status: { not: 'completed' }
        },
        _avg: {
          progress: true
        }
      })

      return APIResponse.success({
        enrollments: coursesWithProgress,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        summary: {
          totalEnrollments,
          completedCourses,
          activeCourses,
          averageProgress: Math.round(averageProgress._avg.progress || 0)
        }
      })

    } catch (error) {
      console.error('Error fetching user courses:', error)
      return APIResponse.error('Failed to fetch enrolled courses', 500)
    }
  }
)
