import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

// GET /api/student/courses/mission-progress - Get user's mission progress for a course
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { courseId } = validatedQuery

      // Verify course exists and user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        },
        include: {
          course: {
            select: {
              id: true,
              hasRoadmap: true
            }
          }
        }
      })

      if (!enrollment) {
        return APIResponse.forbidden('You are not enrolled in this course')
      }

      if (!enrollment.course.hasRoadmap) {
        return APIResponse.badRequest('This course does not have a roadmap enabled')
      }

      // Get all mission progress for the user in this course
      const missionProgress = await prisma.missionProgress.findMany({
        where: {
          userId: user.id,
          mission: {
            courseId
          }
        },
        include: {
          mission: {
            select: {
              id: true,
              title: true,
              order: true,
              pointsReward: true,
              isRequired: true
            }
          }
        },
        orderBy: {
          mission: {
            order: 'asc'
          }
        }
      })

      // Transform the data
      const progressData = missionProgress.map(progress => ({
        id: progress.id,
        missionId: progress.missionId,
        missionTitle: progress.mission.title,
        missionOrder: progress.mission.order,
        isStarted: progress.isStarted,
        isCompleted: progress.isCompleted,
        completionRate: progress.completionRate,
        pointsEarned: progress.pointsEarned,
        startedAt: progress.startedAt?.toISOString(),
        completedAt: progress.completedAt?.toISOString(),
        lastAccessAt: progress.lastAccessAt.toISOString()
      }))

      // Calculate summary statistics
      const totalMissions = missionProgress.length
      const completedMissions = missionProgress.filter(p => p.isCompleted).length
      const totalPoints = missionProgress.reduce((sum, p) => sum + p.pointsEarned, 0)
      const averageCompletion = totalMissions > 0 
        ? missionProgress.reduce((sum, p) => sum + p.completionRate, 0) / totalMissions 
        : 0

      return APIResponse.success({
        progress: progressData,
        summary: {
          totalMissions,
          completedMissions,
          totalPoints,
          averageCompletion: Math.round(averageCompletion * 100) / 100,
          completionPercentage: totalMissions > 0 ? (completedMissions / totalMissions) * 100 : 0
        }
      }, 'Mission progress retrieved successfully')

    } catch (error) {
      console.error('Error fetching mission progress:', error)
      return APIResponse.internalServerError('Failed to fetch mission progress')
    }
  }
)
