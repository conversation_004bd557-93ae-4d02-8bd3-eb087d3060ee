'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Award, 
  CheckCircle,
  Lock,
  Trophy,
  Target,
  BarChart3,
  Calendar
} from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'

interface Quiz {
  id: string
  title: string
  description?: string
  type: string
  difficulty: string
  timeLimit?: number
  estimatedTime?: number
  points?: number
  category?: string
  tags: string[]
  order: number
  isRequired: boolean
  isPublished: boolean
  attempts: {
    total: number
    completed: number
    bestScore: number | null
    latestScore: number | null
    lastAttemptAt: string | null
    isCompleted: boolean
  }
}

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  price: number
  purchaseInfo: {
    id: string
    status: string
    purchasedAt: string
    expiresAt?: string
    progress: number
    lastAccessedAt?: string
  }
  progress: {
    totalQuizzes: number
    completedQuizzes: number
    progressPercentage: number
    lastAccessedAt?: string
  }
  quizzes: Quiz[]
}

export default function BundleDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const bundleSlug = params?.slug as string

  const [bundle, setBundle] = useState<Bundle | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'quizzes' | 'progress'>('overview')

  useEffect(() => {
    if (bundleSlug) {
      fetchBundleDetails()
    }
  }, [bundleSlug])

  const fetchBundleDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/student/quiz-bundles/${bundleSlug}/access`)
      
      if (!response.ok) {
        if (response.status === 403) {
          toast.error('You do not have access to this bundle')
          router.push('/student/quiz-bundles')
          return
        }
        throw new Error('Failed to fetch bundle details')
      }

      const data = await response.json()
      setBundle(data.data.bundle)
    } catch (error) {
      console.error('Error fetching bundle details:', error)
      toast.error('Failed to load bundle details')
    } finally {
      setLoading(false)
    }
  }

  const handleStartQuiz = (quizId: string) => {
    router.push(`/student/quiz/${quizId}`)
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 rounded-lg"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!bundle) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bundle not found</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              The quiz bundle you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
            </p>
            <Button onClick={() => router.push('/student/quiz-bundles')} className="mt-4">
              Back to Bundles
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent">
                {bundle.title}
              </h1>
              <div className="flex items-center gap-4 mt-2">
                {bundle.level && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    {bundle.level}
                  </Badge>
                )}
                {bundle.category && (
                  <Badge variant="secondary">{bundle.category}</Badge>
                )}
                <div className="flex items-center gap-1 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  Purchased {new Date(bundle.purchaseInfo.purchasedAt).toLocaleDateString()}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                {bundle.progress.progressPercentage}%
              </div>
              <div className="text-sm text-gray-500">Complete</div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <Progress value={bundle.progress.progressPercentage} className="h-3" />
            <div className="flex justify-between text-sm text-gray-600">
              <span>{bundle.progress.completedQuizzes} of {bundle.progress.totalQuizzes} quizzes completed</span>
              <span>{bundle.progress.totalQuizzes - bundle.progress.completedQuizzes} remaining</span>
            </div>
          </div>
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="quizzes">Quizzes</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Bundle Info */}
              <div className="md:col-span-2 space-y-6">
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>About This Bundle</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {bundle.description ? (
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {bundle.description}
                      </p>
                    ) : (
                      <p className="text-gray-500 italic">No description available</p>
                    )}
                  </CardContent>
                </Card>

                {/* Features */}
                {bundle.features.length > 0 && (
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle>What&apos;s Included</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {bundle.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Stats Sidebar */}
              <div className="space-y-6">
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle>Bundle Stats</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4 text-blue-500" />
                        <span className="text-sm">Total Quizzes</span>
                      </div>
                      <span className="font-semibold">{bundle.progress.totalQuizzes}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">Completed</span>
                      </div>
                      <span className="font-semibold">{bundle.progress.completedQuizzes}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-orange-500" />
                        <span className="text-sm">Remaining</span>
                      </div>
                      <span className="font-semibold">{bundle.progress.totalQuizzes - bundle.progress.completedQuizzes}</span>
                    </div>

                    {bundle.duration && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-purple-500" />
                          <span className="text-sm">Duration</span>
                        </div>
                        <span className="font-semibold">{bundle.duration}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Tags */}
                {bundle.tags.length > 0 && (
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                    <CardHeader>
                      <CardTitle>Tags</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {bundle.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </TabsContent>

          {/* Quizzes Tab */}
          <TabsContent value="quizzes" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {bundle.quizzes.map((quiz, index) => (
                <motion.div
                  key={quiz.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline" className="text-xs">
                          Quiz {quiz.order}
                        </Badge>
                        <Badge className={getDifficultyColor(quiz.difficulty)}>
                          {quiz.difficulty}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg line-clamp-2">{quiz.title}</CardTitle>
                      {quiz.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                          {quiz.description}
                        </p>
                      )}
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="flex items-center gap-1">
                          <BookOpen className="h-3 w-3" />
                          {quiz.type}
                        </div>
                        {quiz.timeLimit && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {quiz.timeLimit}m
                          </div>
                        )}
                        {quiz.points && (
                          <div className="flex items-center gap-1">
                            <Trophy className="h-3 w-3" />
                            {quiz.points} pts
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <BarChart3 className="h-3 w-3" />
                          {quiz.attempts.total} attempts
                        </div>
                      </div>

                      {/* Attempt Stats */}
                      {quiz.attempts.total > 0 && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span>Best Score:</span>
                            <span className={quiz.attempts.bestScore ? getScoreColor(quiz.attempts.bestScore) : 'text-gray-500'}>
                              {quiz.attempts.bestScore ? `${quiz.attempts.bestScore}%` : 'N/A'}
                            </span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span>Latest Score:</span>
                            <span className={quiz.attempts.latestScore ? getScoreColor(quiz.attempts.latestScore) : 'text-gray-500'}>
                              {quiz.attempts.latestScore ? `${quiz.attempts.latestScore}%` : 'N/A'}
                            </span>
                          </div>
                        </div>
                      )}

                      <Button
                        onClick={() => handleStartQuiz(quiz.id)}
                        className="w-full"
                        variant={quiz.attempts.isCompleted ? "outline" : "default"}
                      >
                        {quiz.attempts.isCompleted ? (
                          <>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Retake Quiz
                          </>
                        ) : (
                          <>
                            <Play className="mr-2 h-4 w-4" />
                            Start Quiz
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          {/* Progress Tab */}
          <TabsContent value="progress" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Overall Progress */}
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Overall Progress</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-600 mb-2">
                      {bundle.progress.progressPercentage}%
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      {bundle.progress.completedQuizzes} of {bundle.progress.totalQuizzes} quizzes completed
                    </p>
                  </div>
                  
                  <Progress value={bundle.progress.progressPercentage} className="h-4" />
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {bundle.progress.completedQuizzes}
                      </div>
                      <div className="text-sm text-gray-500">Completed</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-orange-600">
                        {bundle.progress.totalQuizzes - bundle.progress.completedQuizzes}
                      </div>
                      <div className="text-sm text-gray-500">Remaining</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quiz Progress List */}
              <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>Quiz Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {bundle.quizzes.map(quiz => (
                      <div key={quiz.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          {quiz.attempts.isCompleted ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <div className="h-5 w-5 rounded-full border-2 border-gray-300" />
                          )}
                          <div>
                            <p className="font-medium text-sm">{quiz.title}</p>
                            <p className="text-xs text-gray-500">
                              {quiz.attempts.total} attempts
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          {quiz.attempts.bestScore && (
                            <div className={`text-sm font-medium ${getScoreColor(quiz.attempts.bestScore)}`}>
                              {quiz.attempts.bestScore}%
                            </div>
                          )}
                          <div className="text-xs text-gray-500">
                            {quiz.attempts.isCompleted ? 'Completed' : 'Not started'}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
