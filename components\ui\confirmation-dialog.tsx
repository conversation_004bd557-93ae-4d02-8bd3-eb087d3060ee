"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

interface ConfirmationDialogProps {
  trigger: React.ReactNode
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive"
  onConfirm: () => Promise<void> | void
  disabled?: boolean
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function ConfirmationDialog({
  trigger,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
  disabled = false,
  open,
  onOpenChange
}: ConfirmationDialogProps) {
  const [internalIsOpen, setInternalIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Use external control if provided, otherwise use internal state
  const isOpen = open !== undefined ? open : internalIsOpen
  const setIsOpen = onOpenChange || setInternalIsOpen

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await onConfirm()
      setIsOpen(false)
    } catch (error) {
      console.error('Confirmation action failed:', error)
      // Keep dialog open on error so user can retry
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild disabled={disabled}>
        {trigger}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={variant === "destructive" ? "bg-destructive text-destructive-foreground hover:bg-destructive/90" : ""}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Specialized delete confirmation dialog
interface DeleteConfirmationDialogProps {
  trigger: React.ReactNode
  itemName: string
  itemType?: string
  onDelete: () => Promise<void> | void
  disabled?: boolean
}

export function DeleteConfirmationDialog({
  trigger,
  itemName,
  itemType = "item",
  onDelete,
  disabled = false
}: DeleteConfirmationDialogProps) {
  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`Delete ${itemType}`}
      description={`Are you sure you want to delete "${itemName}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      variant="destructive"
      onConfirm={onDelete}
      disabled={disabled}
    />
  )
}

// Bulk action confirmation dialog
interface BulkActionDialogProps {
  trigger: React.ReactNode
  action: string
  itemCount: number
  itemType?: string
  onConfirm: () => Promise<void> | void
  disabled?: boolean
}

export function BulkActionDialog({
  trigger,
  action,
  itemCount,
  itemType = "items",
  onConfirm,
  disabled = false
}: BulkActionDialogProps) {
  const getDescription = () => {
    if (action.toLowerCase().includes('delete')) {
      return `Are you sure you want to ${action.toLowerCase()} ${itemCount} ${itemType}? This action cannot be undone.`
    }
    return `Are you sure you want to ${action.toLowerCase()} ${itemCount} ${itemType}?`
  }

  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`${action} ${itemType}`}
      description={getDescription()}
      confirmText={action}
      cancelText="Cancel"
      variant={action.toLowerCase().includes('delete') ? "destructive" : "default"}
      onConfirm={onConfirm}
      disabled={disabled}
    />
  )
}

// Export confirmation dialog with progress
interface ExportConfirmationDialogProps {
  trigger: React.ReactNode
  exportType: string
  itemCount?: number
  onConfirm: () => Promise<void> | void
  disabled?: boolean
}

export function ExportConfirmationDialog({
  trigger,
  exportType,
  itemCount,
  onConfirm,
  disabled = false
}: ExportConfirmationDialogProps) {
  const getDescription = () => {
    if (itemCount) {
      return `This will export ${itemCount} ${exportType} items. The export will be processed in the background and you'll be notified when it's ready for download.`
    }
    return `This will create a ${exportType} export. The export will be processed in the background and you'll be notified when it's ready for download.`
  }

  return (
    <ConfirmationDialog
      trigger={trigger}
      title={`Export ${exportType}`}
      description={getDescription()}
      confirmText="Start Export"
      cancelText="Cancel"
      variant="default"
      onConfirm={onConfirm}
      disabled={disabled}
    />
  )
}
