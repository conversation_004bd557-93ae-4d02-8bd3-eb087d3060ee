'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CloudArrowUpIcon,
  DocumentIcon,
  DocumentTextIcon,
  PhotoIcon,
  FilmIcon,
  MusicalNoteIcon,
  ArchiveBoxIcon,
  XMarkIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface FileAttachment {
  id: string
  name: string
  originalName: string
  url: string
  type: string
  size: number
  uploadedAt: string
}

interface FileManagerProps {
  lessonId?: string
  courseId?: string
  existingFiles?: FileAttachment[]
  onFilesChange: (files: FileAttachment[]) => void
  allowedTypes?: string[]
  maxFileSize?: number
  maxFiles?: number
}

export default function FileManager({
  lessonId,
  courseId,
  existingFiles = [],
  onFilesChange,
  allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/webm',
    'audio/mpeg',
    'audio/wav',
    'application/zip',
    'application/x-rar-compressed'
  ],
  maxFileSize = 50 * 1024 * 1024, // 50MB
  maxFiles = 10
}: FileManagerProps) {
  const [files, setFiles] = useState<FileAttachment[]>(existingFiles)
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Update files when existingFiles changes
  useEffect(() => {
    setFiles(existingFiles)
  }, [existingFiles])

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return PhotoIcon
    if (type.startsWith('video/')) return FilmIcon
    if (type.startsWith('audio/')) return MusicalNoteIcon
    if (type.includes('pdf')) return DocumentIcon
    if (type.includes('word') || type.includes('document')) return DocumentTextIcon
    if (type.includes('powerpoint') || type.includes('presentation')) return DocumentIcon
    if (type.includes('excel') || type.includes('spreadsheet')) return DocumentIcon
    if (type.includes('zip') || type.includes('rar')) return ArchiveBoxIcon
    return DocumentIcon
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File) => {
    if (!allowedTypes.includes(file.type)) {
      toast.error(`File type ${file.type} is not allowed`)
      return false
    }

    if (file.size > maxFileSize) {
      toast.error(`File size exceeds ${formatFileSize(maxFileSize)} limit`)
      return false
    }

    if (files.length >= maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`)
      return false
    }

    return true
  }

  const uploadFile = async (file: File) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('lessonId', lessonId || 'temp')
      formData.append('type', 'attachment')

      // Add courseId if provided
      if (courseId) {
        formData.append('courseId', courseId)
      } else {
        // For backward compatibility, use a default courseId or show warning
        console.warn('No courseId provided for file upload. Using fallback.')
        formData.append('courseId', 'default-course')
      }

      const response = await fetch('/api/admin/upload/files', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to upload file')
      }

      const result = await response.json()
      const data = result.data || result

      return {
        id: data.id || `file-${Date.now()}`,
        name: data.filename || file.name,
        originalName: file.name,
        url: data.url,
        type: file.type,
        size: file.size,
        uploadedAt: new Date().toISOString()
      }
    } catch (error) {
      console.error('Upload error:', error)
      throw error
    }
  }

  const handleFileSelect = async (selectedFiles: FileList) => {
    const validFiles = Array.from(selectedFiles).filter(validateFile)
    
    if (validFiles.length === 0) return

    try {
      setUploading(true)
      const uploadPromises = validFiles.map(uploadFile)
      const uploadedFiles = await Promise.all(uploadPromises)
      
      const newFiles = [...files, ...uploadedFiles]
      setFiles(newFiles)
      onFilesChange(newFiles)
      
      toast.success(`${uploadedFiles.length} file(s) uploaded successfully`)
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload files')
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeFile = (fileId: string) => {
    const newFiles = files.filter(f => f.id !== fileId)
    setFiles(newFiles)
    onFilesChange(newFiles)
    toast.success('File removed')
  }

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        className={`border-2 border-dashed rounded-xl p-4 lg:p-6 text-center transition-all duration-200 ${
          dragOver
            ? 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-slate-600 hover:border-gray-400 dark:hover:border-slate-500'
        }`}
      >
        <CloudArrowUpIcon className="w-8 lg:w-10 h-8 lg:h-10 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
        <h3 className="text-base lg:text-lg font-medium text-gray-800 dark:text-white mb-2">
          Upload Files
        </h3>
        <p className="text-sm lg:text-base text-gray-600 dark:text-gray-300 mb-4">
          Drag and drop files here, or click to browse
        </p>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || files.length >= maxFiles}
          className="px-4 lg:px-6 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 text-sm lg:text-base"
        >
          {uploading ? 'Uploading...' : 'Choose Files'}
        </motion.button>
        <p className="text-xs text-gray-500 mt-3">
          Max {maxFiles} files, {formatFileSize(maxFileSize)} each<br />
          Supported: PDF, Word, PowerPoint, Excel, Images, Videos, Audio, Archives
        </p>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-800">Attached Files ({files.length})</h4>
          <AnimatePresence>
            {files.map((file) => {
              const Icon = getFileIcon(file.type)
              return (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center justify-between p-4 bg-white/50 border border-gray-200 rounded-xl"
                >
                  <div className="flex items-center space-x-3">
                    <Icon className="w-8 h-8 text-blue-600" />
                    <div>
                      <p className="font-medium text-gray-800">{file.originalName}</p>
                      <p className="text-sm text-gray-600">
                        {formatFileSize(file.size)} • {new Date(file.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => window.open(file.url, '_blank')}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                      title="Preview"
                    >
                      <EyeIcon className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => {
                        const a = document.createElement('a')
                        a.href = file.url
                        a.download = file.originalName
                        a.click()
                      }}
                      className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200"
                      title="Download"
                    >
                      <ArrowDownTrayIcon className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => removeFile(file.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                      title="Remove"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </motion.button>
                  </div>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={allowedTypes.join(',')}
        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        className="hidden"
      />
    </div>
  )
}
