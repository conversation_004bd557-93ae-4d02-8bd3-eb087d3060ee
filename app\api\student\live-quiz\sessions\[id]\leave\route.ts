import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'


// POST /api/student/live-quiz/sessions/[id]/leave - Leave live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Find participation
      const participation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: { sessionId, userId: user.id }
        }
      })

      if (!participation || !participation.isActive) {
        return APIResponse.error('Not participating in this session', 400, 'NOT_PARTICIPATING')
      }

      const updated = await prisma.liveQuizParticipant.update({
        where: { id: participation.id },
        data: { isActive: false, leftAt: new Date() }
      })

      // Notify session room
      try {
        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()
        socketManager?.broadcastToRoom(`live-quiz:${sessionId}`, 'live-quiz:participant-left', {
          sessionId,
          participant: {
            userId: updated.userId,
            leftAt: updated.leftAt
          }
        })
      } catch (e) {
        // ignore
      }

      return APIResponse.success({ success: true }, 'Left session')
    } catch (error) {
      console.error('Error leaving session:', error)
      return APIResponse.error('Failed to leave session', 500)
    }
  }
)
