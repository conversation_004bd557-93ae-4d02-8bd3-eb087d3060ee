import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/public/categories - Get all course categories (public access)
export async function GET(request: NextRequest) {
  try {
    // Get distinct categories from courses
    const categories = await prisma.course.findMany({
      where: {
        isActive: true,
        category: { not: null }
      },
      select: {
        category: true,
        _count: {
          select: { enrollments: true }
        }
      },
      distinct: ['category']
    })

    // Get course count per category
    const categoryStats = await prisma.course.groupBy({
      by: ['category'],
      where: {
        isActive: true,
        category: { not: null }
      },
      _count: {
        id: true
      },
      _avg: {
        rating: true,
        price: true
      }
    })

    // Combine categories with stats
    const categoriesWithStats = categories.map(cat => {
      const stats = categoryStats.find(stat => stat.category === cat.category)
      return {
        id: cat.category?.toLowerCase() || 'uncategorized',
        name: cat.category || 'Uncategorized',
        courseCount: stats?._count.id || 0,
        averageRating: stats?._avg.rating || 0,
        averagePrice: stats?._avg.price || 0
      }
    })

    // Add predefined category metadata
    const categoryMetadata: Record<string, { icon: string; color: string; description: string }> = {
      'engineering': {
        icon: 'Calculator',
        color: 'blue',
        description: 'Engineering entrance exams and technical courses'
      },
      'medical': {
        icon: 'Stethoscope',
        color: 'pink',
        description: 'Medical entrance exams and healthcare courses'
      },
      'government': {
        icon: 'Building',
        color: 'green',
        description: 'Government job preparation and civil services'
      },
      'teaching': {
        icon: 'Users',
        color: 'orange',
        description: 'Teaching exams and education courses'
      },
      'business': {
        icon: 'Briefcase',
        color: 'purple',
        description: 'Business and management courses'
      },
      'technology': {
        icon: 'Laptop',
        color: 'indigo',
        description: 'Technology and programming courses'
      },
      'language': {
        icon: 'Globe',
        color: 'teal',
        description: 'Language learning and communication'
      },
      'finance': {
        icon: 'DollarSign',
        color: 'yellow',
        description: 'Finance and accounting courses'
      }
    }

    // Enhance categories with metadata
    const enhancedCategories = categoriesWithStats.map(category => ({
      ...category,
      ...categoryMetadata[category.name.toLowerCase()] || {
        icon: 'BookOpen',
        color: 'gray',
        description: 'General courses'
      }
    }))

    // Sort by course count descending
    enhancedCategories.sort((a, b) => b.courseCount - a.courseCount)

    return NextResponse.json({
      success: true,
      data: {
        categories: enhancedCategories,
        totalCategories: enhancedCategories.length
      }
    })

  } catch (error) {
    console.error('Error fetching public categories:', error)
    
    // Return fallback categories on error
    const fallbackCategories = [
      {
        id: 'engineering',
        name: 'Engineering',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Calculator',
        color: 'blue',
        description: 'Engineering entrance exams and technical courses'
      },
      {
        id: 'medical',
        name: 'Medical',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Stethoscope',
        color: 'pink',
        description: 'Medical entrance exams and healthcare courses'
      },
      {
        id: 'government',
        name: 'Government',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Building',
        color: 'green',
        description: 'Government job preparation and civil services'
      },
      {
        id: 'teaching',
        name: 'Teaching',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Users',
        color: 'orange',
        description: 'Teaching exams and education courses'
      },
      {
        id: 'business',
        name: 'Business',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Briefcase',
        color: 'purple',
        description: 'Business and management courses'
      },
      {
        id: 'technology',
        name: 'Technology',
        courseCount: 0,
        averageRating: 0,
        averagePrice: 0,
        icon: 'Laptop',
        color: 'indigo',
        description: 'Technology and programming courses'
      }
    ]

    return NextResponse.json({
      success: true,
      data: {
        categories: fallbackCategories,
        totalCategories: fallbackCategories.length
      }
    })
  }
}
