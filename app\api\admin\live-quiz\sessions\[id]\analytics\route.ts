import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/admin/live-quiz/sessions/[id]/analytics - Get detailed session analytics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session with all related data
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  correctAnswer: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            },
            orderBy: [
              { rank: 'asc' },
              { score: 'desc' }
            ]
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Calculate session duration
      const sessionDuration = session.endTime && session.startTime
        ? Math.round((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / 1000)
        : null

      // Calculate participant statistics
      const activeParticipants = session.participants.filter(p => p.isActive)
      const totalParticipants = session.participants.length

      const participantStats = {
        total: totalParticipants,
        active: activeParticipants.length,
        completed: session.participants.filter(p => p.totalAnswered === session.quiz.questions.length).length,
        averageScore: activeParticipants.length > 0 
          ? Math.round(activeParticipants.reduce((sum, p) => sum + p.score, 0) / activeParticipants.length)
          : 0,
        averageAccuracy: activeParticipants.length > 0
          ? Math.round(activeParticipants.reduce((sum, p) => 
              p.totalAnswered > 0 ? sum + (p.correctAnswers / p.totalAnswered) * 100 : sum, 0
            ) / activeParticipants.length)
          : 0,
        averageTimeSpent: activeParticipants.length > 0
          ? Math.round(activeParticipants.reduce((sum, p) => sum + p.timeSpent, 0) / activeParticipants.length)
          : 0
      }

      // Calculate question-by-question analytics
      const questionAnalytics = session.quiz.questions.map(question => {
        const questionAnswers: Array<{ answer: any, isCorrect: boolean, timeSpent: number }> = []
        
        activeParticipants.forEach(participant => {
          const answers = participant.answers as Record<string, any>
          const answer = answers[question.id]
          
          if (answer !== undefined) {
            const isCorrect = answer === question.correctAnswer
            questionAnswers.push({
              answer,
              isCorrect,
              timeSpent: 0 // Would need to track per-question time
            })
          }
        })

        const totalAnswers = questionAnswers.length
        const correctAnswers = questionAnswers.filter(a => a.isCorrect).length
        
        // Calculate answer distribution
        const answerDistribution: Record<string, number> = {}
        questionAnswers.forEach(qa => {
          const answerKey = String(qa.answer)
          answerDistribution[answerKey] = (answerDistribution[answerKey] || 0) + 1
        })

        return {
          questionId: question.id,
          questionText: question.text.substring(0, 100) + (question.text.length > 100 ? '...' : ''),
          questionType: question.type,
          questionOrder: question.order,
          points: question.points,
          totalAnswers,
          correctAnswers,
          accuracy: totalAnswers > 0 ? Math.round((correctAnswers / totalAnswers) * 100) : 0,
          answerRate: Math.round((totalAnswers / activeParticipants.length) * 100),
          answerDistribution,
          difficulty: correctAnswers === 0 ? 'Very Hard' 
                     : correctAnswers / totalAnswers < 0.3 ? 'Hard'
                     : correctAnswers / totalAnswers < 0.7 ? 'Medium'
                     : 'Easy'
        }
      })

      // Calculate score distribution
      const scoreRanges = {
        '0-20': 0,
        '21-40': 0,
        '41-60': 0,
        '61-80': 0,
        '81-100': 0
      }

      const maxPossibleScore = session.quiz.questions.reduce((sum, q) => sum + q.points, 0)
      
      activeParticipants.forEach(participant => {
        const scorePercentage = maxPossibleScore > 0 ? (participant.score / maxPossibleScore) * 100 : 0
        
        if (scorePercentage <= 20) scoreRanges['0-20']++
        else if (scorePercentage <= 40) scoreRanges['21-40']++
        else if (scorePercentage <= 60) scoreRanges['41-60']++
        else if (scorePercentage <= 80) scoreRanges['61-80']++
        else scoreRanges['81-100']++
      })

      // Get top performers
      const topPerformers = activeParticipants
        .slice(0, 10)
        .map(participant => ({
          userId: participant.userId,
          userName: participant.user.name,
          userEmail: participant.user.email,
          score: participant.score,
          rank: participant.rank,
          correctAnswers: participant.correctAnswers,
          totalAnswered: participant.totalAnswered,
          accuracy: participant.totalAnswered > 0 
            ? Math.round((participant.correctAnswers / participant.totalAnswered) * 100)
            : 0,
          timeSpent: participant.timeSpent,
          joinedAt: participant.joinedAt,
          completionRate: Math.round((participant.totalAnswered / session.quiz.questions.length) * 100)
        }))

      // Calculate engagement metrics
      const engagementMetrics = {
        participationRate: totalParticipants > 0 
          ? Math.round((activeParticipants.length / totalParticipants) * 100)
          : 0,
        completionRate: activeParticipants.length > 0
          ? Math.round((session.participants.filter(p => p.totalAnswered === session.quiz.questions.length).length / activeParticipants.length) * 100)
          : 0,
        averageQuestionsAnswered: activeParticipants.length > 0
          ? Math.round(activeParticipants.reduce((sum, p) => sum + p.totalAnswered, 0) / activeParticipants.length)
          : 0,
        dropoffRate: totalParticipants > 0
          ? Math.round(((totalParticipants - activeParticipants.length) / totalParticipants) * 100)
          : 0
      }

      // Time-based analytics (if session is completed)
      const timeAnalytics = session.status === 'COMPLETED' && sessionDuration ? {
        totalDuration: sessionDuration,
        averageTimePerQuestion: session.quiz.questions.length > 0 
          ? Math.round(sessionDuration / session.quiz.questions.length)
          : 0,
        estimatedVsActual: session.quiz.timeLimit 
          ? {
              estimated: session.quiz.timeLimit * 60, // Convert minutes to seconds
              actual: sessionDuration,
              variance: Math.round(((sessionDuration - (session.quiz.timeLimit * 60)) / (session.quiz.timeLimit * 60)) * 100)
            }
          : null
      } : null

      return APIResponse.success({
        session: {
          id: session.id,
          title: session.title,
          status: session.status,
          startTime: session.startTime,
          endTime: session.endTime,
          duration: sessionDuration,
          quiz: {
            id: session.quiz.id,
            title: session.quiz.title,
            difficulty: session.quiz.difficulty,
            questionCount: session.quiz.questions.length,
            maxPossibleScore
          },
          creator: session.creator
        },
        participantStats,
        questionAnalytics,
        scoreDistribution: scoreRanges,
        topPerformers,
        engagementMetrics,
        timeAnalytics,
        generatedAt: new Date()
      }, 'Session analytics retrieved successfully')

    } catch (error) {
      console.error('Error fetching session analytics:', error)
      return APIResponse.error('Failed to fetch session analytics', 500)
    }
  }
)
