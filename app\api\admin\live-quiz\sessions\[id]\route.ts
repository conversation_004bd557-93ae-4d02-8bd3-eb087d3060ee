import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateLiveQuizSessionSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  maxParticipants: z.number().min(1).optional(),
  questionTimeLimit: z.number().min(10).max(300).optional(),
  autoAdvance: z.boolean().optional(),
  showLeaderboard: z.boolean().optional(),
  allowLateJoin: z.boolean().optional(),
  scheduledStart: z.string().datetime().optional()
})

// GET /api/admin/live-quiz/sessions/[id] - Get specific live quiz session
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                select: {
                  id: true,
                  type: true,
                  text: true,
                  options: true,
                  correctAnswer: true,
                  explanation: true,
                  points: true,
                  order: true
                },
                orderBy: { order: 'asc' }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              }
            },
            orderBy: [
              { rank: 'asc' },
              { score: 'desc' },
              { joinedAt: 'asc' }
            ]
          },
          _count: {
            select: {
              participants: true
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      return APIResponse.success({
        ...session,
        participantCount: session._count.participants,
        questionCount: session.quiz.questions.length
      }, 'Live quiz session retrieved successfully')

    } catch (error) {
      console.error('Error fetching live quiz session:', error)
      return APIResponse.error('Failed to fetch live quiz session', 500)
    }
  }
)

// PUT /api/admin/live-quiz/sessions/[id] - Update live quiz session
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateLiveQuizSessionSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Check if session exists and user has permission
      const existingSession = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: { id: true, status: true, createdBy: true }
      })

      if (!existingSession) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Only allow updates if session is not active or completed
      if (existingSession.status === 'ACTIVE') {
        return APIResponse.error('Cannot update active session', 400, 'SESSION_ACTIVE')
      }

      if (existingSession.status === 'COMPLETED') {
        return APIResponse.error('Cannot update completed session', 400, 'SESSION_COMPLETED')
      }

      // Validate scheduled start time if provided
      if (validatedBody.scheduledStart) {
        const scheduledDate = new Date(validatedBody.scheduledStart)
        if (scheduledDate <= new Date()) {
          return APIResponse.error('Scheduled start time must be in the future', 400, 'INVALID_SCHEDULED_TIME')
        }
      }

      // Update session
      const updatedSession = await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          ...validatedBody,
          scheduledStart: validatedBody.scheduledStart ? new Date(validatedBody.scheduledStart) : undefined
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true,
              timeLimit: true,
              questions: {
                select: { id: true }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              participants: true
            }
          }
        }
      })

      return APIResponse.success({
        ...updatedSession,
        participantCount: updatedSession._count.participants,
        questionCount: updatedSession.quiz.questions.length
      }, 'Live quiz session updated successfully')

    } catch (error) {
      console.error('Error updating live quiz session:', error)
      return APIResponse.error('Failed to update live quiz session', 500)
    }
  }
)

// DELETE /api/admin/live-quiz/sessions/[id] - Delete live quiz session
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Check if session exists
      const existingSession = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: { id: true, status: true, createdBy: true }
      })

      if (!existingSession) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Only allow deletion if session is not active
      if (existingSession.status === 'ACTIVE') {
        return APIResponse.error('Cannot delete active session', 400, 'SESSION_ACTIVE')
      }

      // Delete session (participants will be deleted due to cascade)
      await prisma.liveQuizSession.delete({
        where: { id: sessionId }
      })

      return APIResponse.success(null, 'Live quiz session deleted successfully')

    } catch (error) {
      console.error('Error deleting live quiz session:', error)
      return APIResponse.error('Failed to delete live quiz session', 500)
    }
  }
)
