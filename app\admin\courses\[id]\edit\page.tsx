'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import {
  ArrowLeftIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import ImageUpload from '@/components/admin/image-upload'

interface CourseFormData {
  title: string
  description: string
  shortDescription: string
  price: number
  originalPrice: number
  category: string
  level: string
  language: string
  duration: string
  thumbnailImage: string
  features: string[]
  tags: string[]
  requirements: string[]
  whatYouLearn: string[]
  isPublished: boolean
}

interface Category {
  id: string
  name: string
  description?: string
}

export default function EditCoursePage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params?.id as string

  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [categories, setCategories] = useState<Category[]>([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  const [formData, setFormData] = useState<CourseFormData>({
    title: '',
    description: '',
    shortDescription: '',
    price: 0,
    originalPrice: 0,
    category: '',
    level: '',
    language: 'en',
    duration: '',
    thumbnailImage: '',
    features: [],
    tags: [],
    requirements: [],
    whatYouLearn: [],
    isPublished: false
  })

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoadingCategories(true)
        const response = await fetch('/api/admin/courses/categories')
        if (!response.ok) throw new Error('Failed to fetch categories')

        const data = await response.json()
                 // Handle different response formats
        const categories = data.data?.categories || data.categories || []
                 setCategories(categories)
      } catch (error) {
        console.error('Error fetching categories:', error)
        toast.error('Failed to load categories')
        setCategories([])
      } finally {
        setLoadingCategories(false)
      }
    }

    fetchCategories()
  }, [])

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      setInitialLoading(true)
             const response = await fetch(`/api/admin/courses/${courseId}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Course fetch failed:', response.status, errorData)
        throw new Error(errorData.message || 'Failed to fetch course')
      }

      const result = await response.json()
             const data = result.data || result
      const course = data.course || data
             // Populate form with existing course data
      const newFormData = {
        title: course.title || '',
        description: course.description || '',
        shortDescription: course.shortDescription || '',
        price: course.price || 0,
        originalPrice: course.originalPrice || 0,
        category: course.category || '',
        level: course.level || '',
        language: course.language || 'en',
        duration: course.duration || '',
        thumbnailImage: course.thumbnailImage || '',
        features: course.features || [],
        tags: course.tags || [],
        requirements: course.requirements || [],
        whatYouLearn: course.whatYouLearn || [],
        isPublished: course.isPublished || false
      }

             setFormData(newFormData)
    } catch (error) {
      console.error('Error fetching course:', error)
      toast.error('Failed to load course data')
      router.push('/admin/courses')
    } finally {
      setInitialLoading(false)
    }
  }

  const handleInputChange = (field: keyof CourseFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleThumbnailChange = (imageUrl: string | null) => {
    setFormData(prev => ({ ...prev, thumbnailImage: imageUrl || '' }))
  }

  const handleThumbnailUpload = (imageData: any) => {
         toast.success('Course thumbnail updated successfully!')
  }

  const addArrayItem = (field: keyof CourseFormData, value: string) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...(prev[field] as string[]), value.trim()]
      }))
    }
  }

  const removeArrayItem = (field: keyof CourseFormData, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[]).filter((_, i) => i !== index)
    }))
  }

  const handleSaveAsDraft = async () => {
    const updatedFormData = { ...formData, isPublished: false }
    await submitForm(updatedFormData)
  }

  const handlePublishCourse = async () => {
    const updatedFormData = { ...formData, isPublished: true }
    await submitForm(updatedFormData)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await submitForm(formData)
  }

  const submitForm = async (dataToSubmit: CourseFormData) => {
    // Validation
    if (!dataToSubmit.title.trim()) {
      toast.error('Course title is required')
      return
    }

    if (!dataToSubmit.category) {
      toast.error('Please select a category')
      return
    }

    if (!dataToSubmit.level) {
      toast.error('Please select a level')
      return
    }

    if (dataToSubmit.shortDescription && !dataToSubmit.shortDescription.trim()) {
      toast.error('Short description cannot be empty if provided')
      return
    }

    if (dataToSubmit.price < 0) {
      toast.error('Price cannot be negative')
      return
    }

    try {
      setLoading(true)
             const response = await fetch(`/api/admin/courses/${courseId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSubmit)
      })

             if (!response.ok) {
        const error = await response.json()
        console.error('API Error:', error)
        throw new Error(error.message || 'Failed to update course')
      }

      const result = await response.json()
             const data = result.data || result
             toast.success(data.message || 'Course updated successfully!')
      
      // Navigate back to course detail page
      router.push(`/admin/courses/${courseId}`)

    } catch (error: any) {
      console.error('Error updating course:', error)
      toast.error(error.message || 'Failed to update course')
    } finally {
      setLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading course data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href={`/admin/courses/${courseId}`}>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 text-gray-600 hover:bg-white/50 rounded-lg transition-colors duration-200"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </motion.button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Edit Course
              </h1>
              <p className="text-gray-600 mt-2">Update course information and settings</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <motion.button
              type="button"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleSaveAsDraft}
              disabled={loading}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 disabled:opacity-50"
            >
              {loading ? 'Saving...' : 'Save as Draft'}
            </motion.button>
            <motion.button
              type="button"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handlePublishCourse}
              disabled={loading}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
            >
              {loading ? 'Publishing...' : formData.isPublished ? 'Update & Publish' : 'Publish Course'}
            </motion.button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
          >
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Course Title */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Course Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter course title"
                  required
                />
              </div>

              {/* Course Thumbnail */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Course Thumbnail
                </label>
                <ImageUpload
                  existingImage={formData.thumbnailImage}
                  onImageChange={handleThumbnailChange}
                  onImageUpload={handleThumbnailUpload}
                  aspectRatio="video"
                  placeholder="Upload course thumbnail (16:9 recommended)"
                  courseId={courseId}
                  imageType="thumbnail"
                />
                <p className="text-sm text-gray-500 mt-2">
                  Upload an attractive thumbnail image for your course. Recommended size: 1280x720 pixels.
                </p>
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                  disabled={loadingCategories}
                >
                  <option value="">
                    {loadingCategories ? 'Loading categories...' : 'Select Category'}
                  </option>
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>{category.name}</option>
                  ))}
                </select>
              </div>

              {/* Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Level *
                </label>
                <select
                  value={formData.level}
                  onChange={(e) => handleInputChange('level', e.target.value)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  required
                >
                  <option value="">Select Level</option>
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>

              {/* Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="0.00"
                />
              </div>

              {/* Original Price */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Original Price ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.originalPrice}
                  onChange={(e) => handleInputChange('originalPrice', parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="0.00"
                />
              </div>
            </div>

            {/* Short Description */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Short Description
              </label>
              <textarea
                value={formData.shortDescription}
                onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="Brief description of the course"
              />
            </div>

            {/* Full Description */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={6}
                className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
                placeholder="Detailed description of the course content, objectives, and benefits"
              />
            </div>
          </motion.div>

          {/* Array Fields */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/60 backdrop-blur-xl rounded-2xl border border-white/20 p-8"
          >
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Course Details</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Features */}
              <ArrayField
                label="Course Features"
                items={formData.features}
                onAdd={(value) => addArrayItem('features', value)}
                onRemove={(index) => removeArrayItem('features', index)}
                placeholder="Add a course feature"
              />

              {/* Tags */}
              <ArrayField
                label="Tags"
                items={formData.tags}
                onAdd={(value) => addArrayItem('tags', value)}
                onRemove={(index) => removeArrayItem('tags', index)}
                placeholder="Add a tag"
              />

              {/* Requirements */}
              <ArrayField
                label="Requirements"
                items={formData.requirements}
                onAdd={(value) => addArrayItem('requirements', value)}
                onRemove={(index) => removeArrayItem('requirements', index)}
                placeholder="Add a requirement"
              />

              {/* What You'll Learn */}
              <ArrayField
                label="What You'll Learn"
                items={formData.whatYouLearn}
                onAdd={(value) => addArrayItem('whatYouLearn', value)}
                onRemove={(index) => removeArrayItem('whatYouLearn', index)}
                placeholder="Add a learning outcome"
              />
            </div>
          </motion.div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex justify-center"
          >
            <motion.button
              type="submit"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={loading}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
            >
              {loading ? 'Updating...' : 'Update Course'}
            </motion.button>
          </motion.div>
        </form>
      </div>
    </div>
  )
}

interface ArrayFieldProps {
  label: string
  items: string[]
  onAdd: (value: string) => void
  onRemove: (index: number) => void
  placeholder: string
}

function ArrayField({ label, items, onAdd, onRemove, placeholder }: ArrayFieldProps) {
  const [inputValue, setInputValue] = useState('')

  const handleAdd = () => {
    if (inputValue.trim()) {
      onAdd(inputValue.trim())
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAdd()
    }
  }

  return (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>

      {/* Input */}
      <div className="flex space-x-2 mb-3">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          className="flex-1 px-3 py-2 bg-white/50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          placeholder={placeholder}
        />
        <motion.button
          type="button"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={handleAdd}
          className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <PlusIcon className="w-4 h-4" />
        </motion.button>
      </div>

      {/* Items List */}
      <div className="space-y-2 max-h-32 overflow-y-auto">
        {items.map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
          >
            <span className="text-sm text-gray-700 flex-1">{item}</span>
            <motion.button
              type="button"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onRemove(index)}
              className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
            >
              <XMarkIcon className="w-4 h-4" />
            </motion.button>
          </motion.div>
        ))}
      </div>

      {items.length === 0 && (
        <p className="text-sm text-gray-500 italic">No {label.toLowerCase()} added yet</p>
      )}
    </div>
  )
}
