import { NextRequest } from 'next/server';
import { APIResponse, createAPIHandler } from '@/lib/api-middleware';
import { prisma } from '@/lib/prisma';

const defaultSettings = {
  companyName: 'PrepLocus',
  companyDescription: 'India\'s leading online coaching platform',
  companyMission: 'Empowering students to achieve their dreams through quality education',
  companyVision: 'To be the most trusted educational platform in India',
  contactEmail: '<EMAIL>',
  contactPhone: '+91 98765 43210',
  contactAddress: 'New Delhi, India',
  supportEmail: '<EMAIL>',
  privacyEmail: '<EMAIL>',
  legalEmail: '<EMAIL>',
  youtubeChannel: 'https://www.youtube.com/@YourChannelName',
  facebookPage: '',
  twitterHandle: '',
  instagramHandle: '',
  linkedinPage: '',
  logoUrl: '',
  faviconUrl: '',
  brandColor: '#8B5CF6',
  siteUrl: 'http://localhost:3000',
  timezone: 'Asia/Kolkata',
  language: 'en',
  businessHours: 'Mon-Fri, 9 AM - 6 PM IST',
  teamMembers: [
    {
      id: '1',
      name: '<PERSON><PERSON> <PERSON>',
      title: 'Founder & CEO',
      bio: 'Former IIT graduate with 15+ years in education technology. Passionate about making quality education accessible to every student in India.',
      image: '/images/team/founder.jpg',
      expertise: ['Education Technology', 'Strategic Planning', 'Team Leadership']
    },
    {
      id: '2',
      name: 'Priya Sharma',
      title: 'Co-Founder & CTO',
      bio: 'Ex-Google engineer with expertise in AI and machine learning. Leading the technical innovation at PrepLocus to create personalized learning experiences.',
      image: '/images/team/cofounder.jpg',
      expertise: ['Artificial Intelligence', 'Software Architecture', 'Product Development']
    }
  ],
  totalStudents: 500000,
  successRate: 98,
  coursesOffered: 50
};

// POST /api/admin/system-settings/init - Initialize default system settings
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
       
      // Check if settings already exist
      const existingSettings = await prisma.systemSetting.findMany({
        where: { category: 'company' }
      });

      if (existingSettings.length > 0) {
        return APIResponse.success({
          message: 'System settings already exist',
          count: existingSettings.length,
          initialized: false
        });
      }

      // Create settings
      const settingsToCreate = Object.entries(defaultSettings).map(([key, value]) => ({
        key: `company.${key}`,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        category: 'company',
        description: `Company ${key}`
      }));

      await prisma.systemSetting.createMany({
        data: settingsToCreate
      });

       
      return APIResponse.success({
        message: 'System settings initialized successfully',
        count: settingsToCreate.length,
        initialized: true,
        settings: defaultSettings
      });

    } catch (error: any) {
      console.error('❌ Error initializing system settings:', error);
      return APIResponse.error('Failed to initialize system settings: ' + error.message, 500);
    }
  }
);

// GET /api/admin/system-settings/init - Check initialization status
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const existingSettings = await prisma.systemSetting.findMany({
        where: { category: 'company' }
      });

      return APIResponse.success({
        message: 'System settings status',
        initialized: existingSettings.length > 0,
        count: existingSettings.length,
        needsInitialization: existingSettings.length === 0
      });

    } catch (error: any) {
      console.error('❌ Error checking system settings status:', error);
      return APIResponse.error('Failed to check system settings status: ' + error.message, 500);
    }
  }
);
