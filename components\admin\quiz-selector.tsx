'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MagnifyingGlassIcon,
  PlusIcon,
  AcademicCapIcon,
  ClockIcon,
  QuestionMarkCircleIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

interface Quiz {
  id: string
  title: string
  description: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  timeLimit: number
  totalQuestions: number
  totalPoints: number
  isPublished: boolean
  createdAt: string
}

interface QuizSelectorProps {
  courseId: string
  selectedQuizId?: string
  onQuizSelect: (quiz: Quiz | null) => void
  onCreateNew: () => void
}

export default function QuizSelector({
  courseId,
  selectedQuizId,
  onQuizSelect,
  onCreateNew
}: QuizSelectorProps) {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null)

  useEffect(() => {
    fetchQuizzes()
  }, [courseId])

  useEffect(() => {
         if (selectedQuizId && quizzes.length > 0) {
      const quiz = quizzes.find(q => q.id === selectedQuizId)
             if (quiz) {
        setSelectedQuiz(quiz)
        onQuizSelect(quiz)
      } else {
        console.warn('Quiz not found with ID:', selectedQuizId)
      }
    } else if (!selectedQuizId) {
      // Clear selection if no quiz ID provided
      setSelectedQuiz(null)
    }
  }, [selectedQuizId, quizzes, onQuizSelect])

  const fetchQuizzes = async () => {
    try {
      setLoading(true)
             // First try to fetch course-specific quizzes
      let response = await fetch(`/api/admin/courses/${courseId}/quizzes`)

      if (!response.ok) {
        console.warn('Course-specific quiz fetch failed, trying general quizzes API')
        // Fallback to general quizzes API
        response = await fetch('/api/admin/quizzes')
      }

      if (!response.ok) {
        console.error('Quiz fetch failed:', response.status, response.statusText)
        throw new Error(`Failed to fetch quizzes: ${response.status}`)
      }

      const result = await response.json()
             // Handle both wrapped and direct response formats
      let rawQuizList = []

      if (result.success && result.quizzes) {
        // Admin quizzes API format: { success: true, quizzes: [...] }
        rawQuizList = result.quizzes
      } else if (result.data && result.data.quizzes) {
        // Course quizzes API format: { data: { quizzes: [...] } }
        rawQuizList = result.data.quizzes
      } else if (result.quizzes) {
        // Direct format: { quizzes: [...] }
        rawQuizList = result.quizzes
      } else if (Array.isArray(result)) {
        // Direct array format
        rawQuizList = result
      }

             // Normalize quiz data to match expected interface
      const quizList = rawQuizList.map((quiz: any) => ({
        ...quiz,
        totalQuestions: quiz.totalQuestions || quiz.questionCount || 0,
        totalPoints: quiz.totalPoints || (quiz.questions?.reduce((acc: number, q: any) => acc + (q.points || 1), 0)) || 0,
        description: quiz.description || '',
        createdAt: quiz.createdAt || new Date().toISOString()
      }))

             setQuizzes(quizList)
    } catch (error: any) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to load quizzes. Please check if any quizzes exist.')
      setQuizzes([])
    } finally {
      setLoading(false)
    }
  }

  const filteredQuizzes = quizzes.filter(quiz =>
    quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    quiz.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleQuizSelect = (quiz: Quiz) => {
    setSelectedQuiz(quiz)
    onQuizSelect(quiz)
    toast.success(`Quiz "${quiz.title}" selected`)
  }

  const handleQuizRemove = () => {
    setSelectedQuiz(null)
    onQuizSelect(null)
    toast.success('Quiz removed from lesson')
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'text-green-600 bg-green-50'
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-50'
      case 'HARD': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUIZ': return AcademicCapIcon
      case 'TEST_SERIES': return QuestionMarkCircleIcon
      case 'DAILY_PRACTICE': return ClockIcon
      default: return AcademicCapIcon
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading quizzes...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Selected Quiz Display */}
      {selectedQuiz && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-green-50 border border-green-200 rounded-xl p-4"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <CheckCircleIcon className="w-6 h-6 text-green-600 mt-1" />
              <div>
                <h4 className="font-medium text-green-800">{selectedQuiz.title}</h4>
                <p className="text-green-600 text-sm mt-1">{selectedQuiz.description}</p>
                <div className="flex items-center space-x-4 mt-2 text-sm text-green-600">
                  <span>{selectedQuiz.totalQuestions} questions</span>
                  <span>{selectedQuiz.timeLimit} minutes</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(selectedQuiz.difficulty)}`}>
                    {selectedQuiz.difficulty}
                  </span>
                </div>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleQuizRemove}
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
              title="Remove Quiz"
            >
              <XMarkIcon className="w-5 h-5" />
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Quiz Selection Interface */}
      {!selectedQuiz && (
        <div className="space-y-4">
          {/* Search and Create */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="w-4 lg:w-5 h-4 lg:h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search quizzes..."
                className="w-full pl-9 lg:pl-10 pr-4 py-2 lg:py-3 bg-white/50 dark:bg-slate-700/50 border border-gray-200 dark:border-slate-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm lg:text-base"
              />
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={onCreateNew}
              className="px-3 lg:px-4 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 text-sm lg:text-base"
            >
              <PlusIcon className="w-4 lg:w-5 h-4 lg:h-5 mr-2 inline" />
              Create New
            </motion.button>
          </div>

          {/* Quiz List */}
          {filteredQuizzes.length === 0 ? (
            <div className="text-center py-8">
              <AcademicCapIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                {searchTerm ? 'No quizzes found' : 'No quizzes available'}
              </h3>
              <p className="text-gray-600 mb-4">
                {searchTerm 
                  ? 'Try adjusting your search terms'
                  : 'Create your first quiz for this course'
                }
              </p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={onCreateNew}
                className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200"
              >
                Create Quiz
              </motion.button>
            </div>
          ) : (
            <div className="grid gap-3 max-h-64 overflow-y-auto">
              <AnimatePresence>
                {filteredQuizzes.map((quiz) => {
                  const TypeIcon = getTypeIcon(quiz.type)
                  return (
                    <motion.div
                      key={quiz.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      whileHover={{ scale: 1.01 }}
                      onClick={() => handleQuizSelect(quiz)}
                      className="p-4 bg-white/50 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-sm transition-all duration-200 cursor-pointer"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <TypeIcon className="w-6 h-6 text-blue-600 mt-1" />
                          <div>
                            <h4 className="font-medium text-gray-800">{quiz.title}</h4>
                            {quiz.description && (
                              <p className="text-gray-600 text-sm mt-1 line-clamp-2">
                                {quiz.description}
                              </p>
                            )}
                            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                              <span>{quiz.totalQuestions} questions</span>
                              <span>{quiz.timeLimit} min</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(quiz.difficulty)}`}>
                                {quiz.difficulty}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`px-2 py-1 rounded-full text-xs ${
                            quiz.isPublished 
                              ? 'text-green-600 bg-green-50' 
                              : 'text-yellow-600 bg-yellow-50'
                          }`}>
                            {quiz.isPublished ? 'Published' : 'Draft'}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </AnimatePresence>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
