import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { quizBundleService } from '@/lib/quiz-bundle-service'
import { z } from 'zod'

const updateBundleSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0).optional(),
  originalPrice: z.number().optional(),
  slug: z.string().regex(/^[a-z0-9-]+$/).optional(),
  thumbnailImage: z.string().url().optional(),
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  duration: z.string().optional(),
  tags: z.array(z.string()).optional(),
  features: z.array(z.string()).optional(),
  quizIds: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
  isPublished: z.boolean().optional()
})

// GET /api/quiz-bundles/[id] - Get bundle by ID
export const GET = createAPIHandler(
  {
    requireAuth: false
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const bundleId = resolvedParams?.id as string

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      const bundle = await quizBundleService.getBundleById(bundleId, true)

      if (!bundle) {
        return APIResponse.error('Bundle not found', 404)
      }

      // Format response
      const formattedBundle = {
        id: bundle.id,
        title: bundle.title,
        description: bundle.description,
        shortDescription: bundle.shortDescription,
        price: bundle.price,
        originalPrice: bundle.originalPrice,
        slug: bundle.slug,
        thumbnailImage: bundle.thumbnailImage,
        category: bundle.category,
        level: bundle.level,
        duration: bundle.duration,
        tags: bundle.tags || [],
        features: bundle.features || [],
        isActive: bundle.isActive,
        isPublished: bundle.isPublished,
        publishedAt: bundle.publishedAt,
        createdAt: bundle.createdAt,
        updatedAt: bundle.updatedAt,
        quizzes: bundle.items?.map(item => ({
          id: (item as any).quiz?.id || '',
          title: (item as any).quiz?.title || '',
          description: (item as any).quiz?.description || '',
          type: (item as any).quiz?.type || '',
          difficulty: (item as any).quiz?.difficulty || '',
          timeLimit: (item as any).quiz?.timeLimit,
          estimatedTime: (item as any).quiz?.estimatedTime,
          points: (item as any).quiz?.points,
          category: (item as any).quiz?.category,
          tags: (item as any).quiz?.tags || [],
          order: item.order,
          isRequired: item.isRequired,
          isPublished: (item as any).quiz?.isPublished || false
        })) || [],
        stats: {
          totalQuizzes: bundle.items?.length || 0,
          totalPurchases: (bundle as any).purchases?.length || 0,
          activePurchases: (bundle as any).purchases?.filter((p: any) => p.status === 'active').length || 0
        }
      }

      return APIResponse.success({ bundle: formattedBundle })

    } catch (error) {
      console.error('Error fetching quiz bundle:', error)
      return APIResponse.error('Failed to fetch quiz bundle', 500)
    }
  }
)

// PUT /api/quiz-bundles/[id] - Update bundle (admin only)
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateBundleSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const bundleId = resolvedParams?.id as string

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      const updatedBundle = await quizBundleService.updateBundle({
        id: bundleId,
        ...validatedBody
      })

      return APIResponse.success({
        message: 'Quiz bundle updated successfully',
        bundle: {
          id: updatedBundle.id,
          title: updatedBundle.title,
          slug: updatedBundle.slug,
          price: updatedBundle.price,
          isActive: updatedBundle.isActive,
          isPublished: updatedBundle.isPublished
        }
      })

    } catch (error: any) {
      console.error('Error updating quiz bundle:', error)
      
      if (error.message.includes('not found')) {
        return APIResponse.error('Bundle not found', 404)
      }
      
      if (error.message.includes('slug already exists')) {
        return APIResponse.error('Bundle slug already exists', 400)
      }

      return APIResponse.error('Failed to update quiz bundle', 500)
    }
  }
)

// DELETE /api/quiz-bundles/[id] - Delete bundle (admin only)
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const bundleId = resolvedParams?.id as string

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      await quizBundleService.deleteBundle(bundleId)

      return APIResponse.success({
        message: 'Quiz bundle deleted successfully'
      })

    } catch (error: any) {
      console.error('Error deleting quiz bundle:', error)
      
      if (error.message.includes('existing purchases')) {
        return APIResponse.error('Cannot delete bundle with existing purchases', 400)
      }

      return APIResponse.error('Failed to delete quiz bundle', 500)
    }
  }
)
