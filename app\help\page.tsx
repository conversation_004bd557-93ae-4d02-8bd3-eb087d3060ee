'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  BookOpen, 
  Users, 
  CreditCard, 
  Settings, 
  HelpCircle,
  ChevronDown,
  ChevronRight,
  ArrowLeft,
  Mail,
  Phone,
  MessageCircle
} from 'lucide-react';
import Link from 'next/link';
import { useSystemSettings } from '@/hooks/use-system-settings';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
}

export default function HelpCenterPage() {
  const { settings } = useSystemSettings();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const categories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of using PrepLocus',
      icon: BookOpen,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'account',
      title: 'Account & Profile',
      description: 'Manage your account settings',
      icon: Users,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'courses',
      title: 'Courses & Learning',
      description: 'Everything about courses and studying',
      icon: BookOpen,
      color: 'from-purple-500 to-violet-500'
    },
    {
      id: 'billing',
      title: 'Billing & Payments',
      description: 'Payment and subscription help',
      icon: CreditCard,
      color: 'from-orange-500 to-red-500'
    },
    {
      id: 'technical',
      title: 'Technical Support',
      description: 'Troubleshooting and technical issues',
      icon: Settings,
      color: 'from-gray-500 to-slate-500'
    }
  ];

  const faqs: FAQItem[] = [
    {
      category: 'getting-started',
      question: `How do I create an account on ${settings.companyName}?`,
      answer: 'To create an account, click the "Get Started" button on our homepage, then select "Sign Up". Fill in your details including name, email, and password. You\'ll receive a verification email to activate your account.'
    },
    {
      category: 'getting-started',
      question: `What exams does ${settings.companyName} support?`,
      answer: `${settings.companyName} supports a wide range of competitive exams including JEE, NEET, UPSC, SSC, CUET, Banking exams, NDA, CDS, CTET, and many more. We continuously add new exam categories based on student demand.`
    },
    {
      category: 'getting-started',
      question: 'How do I start my first course?',
      answer: 'After logging in, go to the "Courses" section, browse available courses, and click "Enroll" on your desired course. Once enrolled, you can access course materials, videos, and practice tests immediately.'
    },
    {
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'On the login page, click "Forgot Password". Enter your registered email address, and we\'ll send you a password reset link. Follow the instructions in the email to create a new password.'
    },
    {
      category: 'account',
      question: 'Can I change my email address?',
      answer: 'Yes, you can change your email address in your profile settings. Go to "Profile" > "Account Settings" > "Email". You\'ll need to verify the new email address before the change takes effect.'
    },
    {
      category: 'account',
      question: 'How do I delete my account?',
      answer: 'To delete your account, go to "Profile" > "Account Settings" > "Delete Account". Please note that this action is irreversible and will permanently remove all your data, progress, and purchased courses.'
    },
    {
      category: 'courses',
      question: 'How do I access my enrolled courses?',
      answer: 'After logging in, go to "My Courses" in your dashboard. Here you\'ll see all your enrolled courses, progress, and can continue learning from where you left off.'
    },
    {
      category: 'courses',
      question: 'Can I download course materials for offline study?',
      answer: 'Yes, most course materials including PDFs, notes, and some videos can be downloaded for offline study. Look for the download icon next to the content you want to save.'
    },
    {
      category: 'courses',
      question: 'How long do I have access to a course after purchase?',
      answer: 'Once you purchase a course, you have lifetime access to it. You can revisit the content anytime, and you\'ll also receive any future updates to the course material.'
    },
    {
      category: 'billing',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit/debit cards, UPI, net banking, and digital wallets. All payments are processed securely through our payment partners.'
    },
    {
      category: 'billing',
      question: 'Can I get a refund if I\'m not satisfied?',
      answer: 'Yes, we offer a 7-day money-back guarantee. If you\'re not satisfied with a course, you can request a refund within 7 days of purchase. Please see our Refund Policy for detailed terms.'
    },
    {
      category: 'billing',
      question: 'Do you offer student discounts?',
      answer: 'Yes, we regularly offer student discounts and promotional offers. Follow us on social media or subscribe to our newsletter to stay updated on the latest deals and discounts.'
    },
    {
      category: 'technical',
      question: 'The website is loading slowly. What should I do?',
      answer: 'Try clearing your browser cache and cookies, disable browser extensions, or try using a different browser. If the issue persists, check your internet connection or contact our technical support.'
    },
    {
      category: 'technical',
      question: 'I can\'t access my course videos. What\'s wrong?',
      answer: 'This could be due to browser compatibility or internet connectivity issues. Try refreshing the page, clearing cache, or using a different browser. Ensure you have a stable internet connection for video streaming.'
    },
    {
      category: 'technical',
      question: 'How do I report a bug or technical issue?',
      answer: `You can report bugs through our contact form, email us at ${settings.supportEmail}, or use the "Report Issue" button in your dashboard. Please include details about the issue and your browser/device information.`
    }
  ];

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Link>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6">
            Help Center
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
            Find answers to common questions and get the help you need to succeed with PrepLocus.
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 py-4 border border-gray-300 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-lg"
            />
          </div>
        </motion.div>

        {/* Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Browse by Category
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.button
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                onClick={() => setSelectedCategory(category.id)}
                className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left ${
                  selectedCategory === category.id
                    ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                    : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-violet-300 dark:hover:border-violet-600'
                }`}
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${category.color} flex items-center justify-center mb-4`}>
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {category.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  {category.description}
                </p>
              </motion.button>
            ))}
          </div>

          <div className="text-center mt-6">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-6 py-2 rounded-lg transition-colors ${
                selectedCategory === 'all'
                  ? 'bg-violet-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Show All Categories
            </button>
          </div>
        </motion.div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Frequently Asked Questions
          </h2>

          <div className="max-w-4xl mx-auto space-y-4">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 + index * 0.05 }}
                className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 overflow-hidden"
              >
                <button
                  onClick={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white pr-4">
                    {faq.question}
                  </h3>
                  {expandedFAQ === index ? (
                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-500 flex-shrink-0" />
                  )}
                </button>
                
                {expandedFAQ === index && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="px-6 pb-6"
                  >
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {faq.answer}
                    </p>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>

          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No results found
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Try adjusting your search terms or browse different categories.
              </p>
            </div>
          )}
        </motion.div>

        {/* Contact Support */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-2xl p-8 text-white text-center"
        >
          <h2 className="text-2xl font-bold mb-4">Still need help?</h2>
          <p className="text-violet-100 mb-6 max-w-2xl mx-auto">
            Can&apos;t find what you&apos;re looking for? Our support team is here to help you succeed.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 bg-white text-violet-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <Mail className="w-5 h-5" />
              Contact Support
            </Link>
            
            <a
              href={`tel:${settings.contactPhone}`}
              className="inline-flex items-center gap-2 border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-violet-600 transition-colors"
            >
              <Phone className="w-5 h-5" />
              Call Us
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
