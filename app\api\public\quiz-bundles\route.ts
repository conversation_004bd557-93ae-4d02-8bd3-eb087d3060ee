import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { z } from 'zod'
import { prisma } from '@/lib/prisma'

const querySchema = commonSchemas.pagination.extend({
  category: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  search: z.string().optional(),
  minPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  maxPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  slug: z.string().optional()
})

// GET /api/public/quiz-bundles - Get published quiz bundles (public access)
export const GET = createAPIHandler(
  {
    requireAuth: false, // Public access
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    try {
      const {
        page = 1,
        limit = 20,
        category,
        level,
        search,
        minPrice,
        maxPrice,
        slug
      } = validatedQuery

      // Build where clause for published bundles only
      const where: any = {
        isPublished: true,
        isActive: true
      }

      // Add filters
      if (category) {
        where.category = {
          contains: category,
          mode: 'insensitive'
        }
      }

      if (level) {
        where.level = level
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { shortDescription: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (minPrice !== undefined || maxPrice !== undefined) {
        where.price = {}
        if (minPrice !== undefined) where.price.gte = minPrice
        if (maxPrice !== undefined) where.price.lte = maxPrice
      }

      if (slug) {
        where.slug = slug
      }

      // Fetch bundles with related data
      const bundles = await prisma.quizBundle.findMany({
        where,
        include: {
          items: {
            include: {
              quiz: {
                select: {
                  id: true,
                  title: true,
                  description: true,
                  type: true,
                  difficulty: true,
                  timeLimit: true,
                  estimatedTime: true,
                  points: true,
                  category: true,
                  tags: true,
                  isPublished: true
                }
              }
            },
            orderBy: { order: 'asc' }
          },
          purchases: {
            where: { status: 'active' },
            select: { id: true }
          },
          _count: {
            select: {
              purchases: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      // Apply pagination
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedBundles = limit > 0 ? bundles.slice(startIndex, endIndex) : bundles

      // Format response
      const formattedBundles = paginatedBundles.map(bundle => ({
        id: bundle.id,
        title: bundle.title,
        description: bundle.description,
        shortDescription: bundle.shortDescription,
        slug: bundle.slug,
        thumbnailImage: bundle.thumbnailImage,
        category: bundle.category,
        level: bundle.level,
        duration: bundle.duration,
        tags: bundle.tags,
        features: bundle.features,
        price: bundle.price,
        originalPrice: bundle.originalPrice,
        isActive: bundle.isActive,
        isPublished: bundle.isPublished,
        publishedAt: bundle.publishedAt?.toISOString(),
        createdAt: bundle.createdAt.toISOString(),
        updatedAt: bundle.updatedAt.toISOString(),
        quizCount: bundle.items?.length || 0,
        quizzes: bundle.items?.map(item => ({
          id: item.quiz.id,
          title: item.quiz.title,
          description: item.quiz.description,
          type: item.quiz.type,
          difficulty: item.quiz.difficulty,
          timeLimit: item.quiz.timeLimit,
          estimatedTime: item.quiz.estimatedTime,
          points: item.quiz.points,
          category: item.quiz.category,
          tags: item.quiz.tags,
          order: item.order,
          isRequired: item.isRequired,
          isPublished: item.quiz.isPublished
        })) || [],
        purchaseCount: bundle._count?.purchases || 0,
        stats: {
          totalQuizzes: bundle.items?.length || 0,
          totalPurchases: bundle.purchases?.length || 0,
          activePurchases: bundle.purchases?.length || 0
        }
      }))

      return APIResponse.success({
        bundles: formattedBundles,
        pagination: {
          page,
          limit,
          total: bundles.length,
          totalPages: Math.ceil(bundles.length / limit),
          hasNext: endIndex < bundles.length,
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching public quiz bundles:', error)
      return APIResponse.error('Failed to fetch quiz bundles', 500)
    }
  }
)
