"use client"

import { signIn, getProviders } from "next-auth/react"
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { GitIcon } from "@/components/icons"
import { Chrome, BookOpen, ArrowRight } from "lucide-react"
import { motion } from "framer-motion"

interface Provider {
  id: string
  name: string
  type: string
  signinUrl: string
  callbackUrl: string
}

export default function SignIn() {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null)

  useEffect(() => {
    const fetchProviders = async () => {
      const res = await getProviders()
      setProviders(res)
    }
    fetchProviders()
  }, [])

  const getProviderIcon = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return <Chrome className="w-5 h-5" />
      case 'github':
        return <GitIcon className="w-5 h-5" />
      default:
        return null
    }
  }

  const getProviderColor = (providerId: string) => {
    switch (providerId) {
      case 'google':
        return 'bg-red-600 hover:bg-red-700'
      case 'github':
        return 'bg-gray-800 hover:bg-gray-900'
      default:
        return 'bg-primary hover:bg-primary/90'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            >
              <BookOpen className="w-8 h-8 text-white" />
            </motion.div>
            <div>
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Welcome Back
              </CardTitle>
              <CardDescription className="text-lg mt-2">
                Sign in to continue your learning journey
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="space-y-3">
              {providers &&
                Object.values(providers).map((provider) => (
                  <motion.div
                    key={provider.name}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      onClick={() => signIn(provider.id, { callbackUrl: '/' })}
                      className={`w-full h-12 ${getProviderColor(provider.id)} text-white shadow-lg hover:shadow-xl transition-all duration-200`}
                      size="lg"
                    >
                      <div className="flex items-center justify-center space-x-3">
                        {getProviderIcon(provider.id)}
                        <span className="font-semibold">Continue with {provider.name}</span>
                        <ArrowRight className="w-4 h-4 ml-auto" />
                      </div>
                    </Button>
                  </motion.div>
                ))}

              {!providers && (
                <div className="text-center text-muted-foreground py-4">
                  <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                  Loading sign-in options...
                </div>
              )}
            </div>

            <div className="text-center text-sm text-muted-foreground pt-4 border-t">
              <p>Don&apos;t have an account?{' '}
                <a href="/auth/signup" className="text-blue-600 hover:text-blue-700 font-semibold">
                  Sign up here
                </a>
              </p>
              <p className="mt-2">
                By signing in, you agree to our{' '}
                <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a>
                {' '}and{' '}
                <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
