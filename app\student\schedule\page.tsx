"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  Clock, 
  Users, 
  BookOpen,
  Play,
  AlertCircle,
  CheckCircle,
  Timer,
  Filter,
  RefreshCw,
  Eye
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"

interface ScheduledQuiz {
  id: string
  title: string
  description: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  startTime: string
  endTime: string
  duration: number
  questionCount: number
  isEnrolled: boolean
  status: 'upcoming' | 'active' | 'completed' | 'missed'
  instructor: {
    name: string
    email: string
  }
  enrollmentCount: number
  maxAttempts: number
  userAttempts: number
}

export default function StudentSchedule() {
  const [scheduledQuizzes, setScheduledQuizzes] = useState<ScheduledQuiz[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState("all")
  const [timeFilter, setTimeFilter] = useState("week")

  useEffect(() => {
    fetchScheduledQuizzes()
  }, [filter, timeFilter])

  const fetchScheduledQuizzes = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        filter,
        timeFilter
      })

      const response = await fetch(`/api/student/schedule?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch scheduled quizzes')
      }

      const data = await response.json()

      if (data.success) {
        setScheduledQuizzes(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch scheduled quizzes')
      }
    } catch (error) {
      console.error('Error fetching scheduled quizzes:', error)
      toast.error('Failed to load scheduled quizzes')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'missed':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'upcoming':
        return <Clock className="h-4 w-4" />
      case 'active':
        return <Play className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'missed':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const filteredQuizzes = scheduledQuizzes.filter(quiz => {
    if (filter === "all") return true
    return quiz.status === filter
  })

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  return (
    <div className="p-6 space-y-8 min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      {/* Header */}
      <div className="relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

        <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Calendar className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Quiz Schedule
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    View and manage your scheduled quizzes and assessments
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={fetchScheduledQuizzes}
                className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filter by status:</span>
            </div>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-48 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Quizzes</SelectItem>
                <SelectItem value="upcoming">Upcoming</SelectItem>
                <SelectItem value="active">Active Now</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="missed">Missed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-48 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quiz Schedule */}
      {loading ? (
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredQuizzes.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No scheduled quizzes</h3>
              <p className="text-muted-foreground mb-6">
                You don&apos;t have any quizzes scheduled for the selected time period.
              </p>
              <Button asChild>
                <Link href="/student/browse">
                  Browse Available Quizzes
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredQuizzes.map((quiz, index) => {
            const startDateTime = formatDateTime(quiz.startTime)
            const endDateTime = formatDateTime(quiz.endTime)
            
            return (
              <motion.div
                key={quiz.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 * index }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold truncate">{quiz.title}</h3>
                          <Badge variant="outline" className={getStatusColor(quiz.status)}>
                            {getStatusIcon(quiz.status)}
                            <span className="ml-1 capitalize">{quiz.status}</span>
                          </Badge>
                          <Badge variant="outline">
                            {quiz.difficulty}
                          </Badge>
                        </div>
                        
                        <p className="text-muted-foreground mb-4">{quiz.description}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>{startDateTime.date}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{startDateTime.time} - {endDateTime.time}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Timer className="h-4 w-4 text-muted-foreground" />
                            <span>{quiz.duration} minutes</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4 text-muted-foreground" />
                            <span>{quiz.questionCount} questions</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 mt-4 text-sm text-muted-foreground">
                          <span>Instructor: {quiz.instructor.name}</span>
                          <span>•</span>
                          <span>{quiz.enrollmentCount} enrolled</span>
                          <span>•</span>
                          <span>Attempts: {quiz.userAttempts}/{quiz.maxAttempts}</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-2 ml-4">
                        {quiz.status === 'active' && (
                          <Button asChild>
                            <Link href={`/student/quiz/${quiz.id}`}>
                              <Play className="h-4 w-4 mr-2" />
                              Start Now
                            </Link>
                          </Button>
                        )}
                        {quiz.status === 'completed' && (
                          <Button variant="outline" asChild>
                            <Link href={`/student/quiz/${quiz.id}/result`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Results
                            </Link>
                          </Button>
                        )}
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/student/browse/${quiz.id}`}>
                            Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      )}
    </div>
  )
}
