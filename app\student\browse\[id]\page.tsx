"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  BookOpen,
  Clock,
  Users,
  Calendar,
  Target,
  Play,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Zap,
  Info
} from "lucide-react"
import { toast } from "@/lib/toast-utils"
import Link from "next/link"
import { EnhancedQuizAnalytics } from "@/components/student/enhanced-quiz-analytics"
import { QuizReviews } from "@/components/student/quiz-reviews"
import { QuizFavoriteButton } from "@/components/student/quiz-favorite-button"
import { QuizShareButton } from "@/components/student/quiz-share-button"

interface QuizDetails {
  id: string
  title: string
  description: string
  instructions?: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags: string[]
  thumbnail?: string
  timeLimit: number
  passingScore?: number
  maxAttempts?: number
  questionCount: number
  totalAttempts: number
  enrollmentCount: number
  averageScore: number
  isEnrolled: boolean
  enrolledAt?: string | null
  userAttempts: Array<{
    id: string
    score: number
    percentage: number
    completedAt: string
    startedAt: string
    isCompleted: boolean
  }>
  bestAttempt?: {
    id: string
    score: number
    percentage: number
    completedAt: string
    startedAt: string
    isCompleted: boolean
  } | null
  instructor: {
    name: string
    email: string
    bio: string
    totalQuizzes: number
    totalStudents: number
    avatar?: string
  }
  objectives: string[]
  syllabus: Array<{
    topic: string
    description: string
    questionCount: number
  }>
  reviews: Array<{
    id: string
    user: {
      name: string
      avatar?: string
    }
    rating: number
    comment: string
    createdAt: string
  }>
  rating: number
  reviewCount: number
  prerequisites: string[]
  isFavorite: boolean
  duration: number
  schedule?: {
    startTime: string
    endTime: string
    isActive: boolean
  }
  createdAt: string
  updatedAt: string
  startTime?: string | null
  endTime?: string | null
  canAttempt: boolean
  hasActiveAttempt: boolean
  isAvailable: boolean
  hasCompleted: boolean
}

export default function QuizPreview() {
  const params = useParams()
  const [quiz, setQuiz] = useState<QuizDetails | null>(null)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [enrolling, setEnrolling] = useState(false)

  useEffect(() => {
    fetchQuizDetails()
    fetchUserData()
  }, [params.id])

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  const fetchQuizDetails = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/student/quizzes/${params.id}`)

      if (!response.ok) {
        throw new Error('Failed to fetch quiz details')
      }

      const data = await response.json()

      if (data.success) {
        setQuiz(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch quiz details')
      }

    } catch (error) {
      console.error('Error fetching quiz details:', error)
      toast.error('Failed to load quiz details')
    } finally {
      setLoading(false)
    }
  }

  const handleEnroll = async () => {
    if (!quiz) return

    setEnrolling(true)
    try {
             const response = await fetch(`/api/student/quizzes/${quiz.id}/enroll`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

             if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Enrollment error:', errorData)
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to enroll`)
      }

      const data = await response.json()
             if (data.success) {
        setQuiz(prev => prev ? {
          ...prev,
          isEnrolled: true,
          enrollmentCount: prev.enrollmentCount + 1,
          enrolledAt: new Date().toISOString()
        } : null)
        toast.success('Successfully enrolled in quiz!')
      } else {
        throw new Error(data.message || 'Failed to enroll')
      }
    } catch (error) {
      console.error('Enrollment error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to enroll in quiz')
    } finally {
      setEnrolling(false)
    }
  }



  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUIZ':
        return <BookOpen className="h-5 w-5 text-white" />
      case 'TEST_SERIES':
        return <Target className="h-5 w-5 text-white" />
      case 'DAILY_PRACTICE':
        return <Zap className="h-5 w-5 text-white" />
      default:
        return <BookOpen className="h-5 w-5 text-white" />
    }
  }



  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/4"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  if (!quiz) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Quiz Not Found</h3>
              <p className="text-muted-foreground mb-6">
                The quiz you&apos;re looking for doesn&apos;t exist or has been removed.
              </p>
              <Button asChild>
                <Link href="/student/browse">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Browse
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="p-6 space-y-8">
        {/* Header */}
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-2xl blur-3xl -z-10" />

          <div className="relative bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-2xl p-8 border border-white/20 dark:border-gray-800/20 shadow-xl">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <Link href="/student/browse">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Browse
                </Link>
              </Button>
              <div className="flex-1">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
                    {getTypeIcon(quiz.type)}
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                      {quiz.title}
                    </h1>
                    <p className="text-lg text-muted-foreground mt-1">
                      by {quiz.instructor.name}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Thumbnail */}
          {quiz.thumbnail && (
            <Card>
              <CardContent className="p-0">
                <div className="aspect-video w-full overflow-hidden rounded-lg">
                  <img
                    src={quiz.thumbnail}
                    alt={quiz.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Overview */}
          <Card className="border-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg shadow-lg">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Overview
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed text-base">
                {quiz.description}
              </p>

              <div className="flex items-center gap-4 mt-6">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`${getDifficultyColor(quiz.difficulty)} shadow-lg`}>
                    {quiz.difficulty}
                  </Badge>
                  {quiz.tags.length > 0 && (
                    <Badge variant="outline" className="shadow-lg">
                      {quiz.tags[0]}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-1 p-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    {quiz.enrollmentCount} enrolled
                  </span>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mt-4">
                {quiz.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs shadow-lg bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Detailed Information */}
          <Tabs defaultValue="objectives" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="objectives">Objectives</TabsTrigger>
              <TabsTrigger value="syllabus">Syllabus</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="instructor">Instructor</TabsTrigger>
            </TabsList>

            <TabsContent value="objectives" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Learning Objectives
                  </CardTitle>
                  <CardDescription>
                    What you&apos;ll learn from this quiz
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {quiz.objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 shrink-0" />
                        <span className="text-sm">{objective}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5" />
                    Prerequisites
                  </CardTitle>
                  <CardDescription>
                    What you should know before taking this quiz
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {quiz.prerequisites.map((prerequisite, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
                        <span className="text-sm">{prerequisite}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="syllabus">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Quiz Syllabus
                  </CardTitle>
                  <CardDescription>
                    Topics covered in this quiz
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {quiz.syllabus.map((topic, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{topic.topic}</h4>
                          <Badge variant="outline">
                            {topic.questionCount} questions
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {topic.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews">
              <QuizReviews
                quizId={quiz.id}
                currentUserId={user?.id || ''}
                hasCompleted={quiz.hasCompleted || false}
              />
            </TabsContent>

            <TabsContent value="analytics">
              <EnhancedQuizAnalytics quizId={quiz.id} />
            </TabsContent>

            <TabsContent value="instructor">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    About the Instructor
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={quiz.instructor.avatar} />
                      <AvatarFallback className="text-lg">
                        {quiz.instructor.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold mb-2">{quiz.instructor.name}</h3>
                      <p className="text-muted-foreground mb-4">{quiz.instructor.bio}</p>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600">{quiz.instructor.totalQuizzes}</div>
                          <div className="text-xs text-muted-foreground">Quizzes Created</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-green-600">{quiz.instructor.totalStudents.toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">Students Taught</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Enrollment Card */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {quiz.isEnrolled ? 'Enrolled' : 'Free'}
                  </div>
                  {quiz.isEnrolled && (
                    <Badge className="bg-green-500 mb-4">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Enrolled
                    </Badge>
                  )}
                </div>

                <div className="space-y-2">
                  {quiz.isEnrolled ? (
                    <Button asChild className="w-full" size="lg">
                      <Link href={`/student/quiz/${quiz.id}`}>
                        <Play className="h-4 w-4 mr-2" />
                        Start Quiz
                      </Link>
                    </Button>
                  ) : (
                    <Button onClick={handleEnroll} className="w-full" size="lg" disabled={enrolling}>
                      {enrolling ? 'Enrolling...' : 'Enroll Now'}
                    </Button>
                  )}
                  
                  <div className="flex gap-2">
                    <QuizFavoriteButton
                      quizId={quiz.id}
                      className="flex-1"
                      variant="outline"
                    />
                    <QuizShareButton
                      quizId={quiz.id}
                      variant="outline"
                      size="icon"
                      showText={false}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quiz Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quiz Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Questions</span>
                </div>
                <span className="font-semibold">{quiz.questionCount}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Duration</span>
                </div>
                <span className="font-semibold">{quiz.duration} min</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Attempts</span>
                </div>
                <span className="font-semibold">{quiz.totalAttempts.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Avg Score</span>
                </div>
                <span className="font-semibold">{quiz.averageScore}%</span>
              </div>

              <div className="pt-2">
                <div className="flex justify-between text-sm mb-2">
                  <span>Success Rate</span>
                  <span>{quiz.averageScore}%</span>
                </div>
                <Progress value={quiz.averageScore} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Schedule Info */}
          {quiz.schedule && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Schedule
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Start Time:</span>
                    <span>{new Date(quiz.schedule.startTime).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>End Time:</span>
                    <span>{new Date(quiz.schedule.endTime).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Status:</span>
                    <Badge className={quiz.schedule.isActive ? 'bg-green-500' : 'bg-gray-500'}>
                      {quiz.schedule.isActive ? 'Active' : 'Scheduled'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
    </div>
  )
}
