'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  CloudArrowUpIcon, 
  PhotoIcon, 
  DocumentIcon, 
  VideoCameraIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'

interface UploadResult {
  success: boolean
  url?: string
  filename?: string
  folder?: string
  size?: number
  error?: string
}

export default function TestUpload() {
  const [courseId, setCourseId] = useState('test-course-123')
  const [lessonId, setLessonId] = useState('test-lesson-456')
  const [uploading, setUploading] = useState(false)
  const [results, setResults] = useState<{
    images: UploadResult[]
    files: UploadResult[]
    videos: UploadResult[]
  }>({
    images: [],
    files: [],
    videos: []
  })

  const uploadImage = async (file: File) => {
    const formData = new FormData()
    formData.append('image', file)
    formData.append('courseId', courseId)
    formData.append('type', 'test-thumbnail')
    formData.append('aspectRatio', 'video')

    const response = await fetch('/api/admin/upload/images', {
      method: 'POST',
      body: formData
    })

    return await response.json()
  }

  const uploadFile = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('courseId', courseId)
    formData.append('lessonId', lessonId)
    formData.append('type', 'test-attachment')

    const response = await fetch('/api/admin/upload/files', {
      method: 'POST',
      body: formData
    })

    return await response.json()
  }

  const uploadVideo = async (file: File) => {
    const formData = new FormData()
    formData.append('video', file)
    formData.append('lessonId', lessonId)

    const response = await fetch('/api/admin/courses/videos/upload', {
      method: 'POST',
      body: formData
    })

    return await response.json()
  }

  const handleFileUpload = async (files: FileList, type: 'image' | 'file' | 'video') => {
    setUploading(true)
    const newResults: UploadResult[] = []

    for (const file of Array.from(files)) {
      try {
        let result
        switch (type) {
          case 'image':
            result = await uploadImage(file)
            break
          case 'file':
            result = await uploadFile(file)
            break
          case 'video':
            result = await uploadVideo(file)
            break
        }

        newResults.push({
          success: result.success || !!result.url,
          url: result.url,
          filename: result.filename,
          folder: result.folder,
          size: result.size,
          error: result.error || result.message
        })
      } catch (error) {
        newResults.push({
          success: false,
          error: error instanceof Error ? error.message : 'Upload failed'
        })
      }
    }

    setResults(prev => ({
      ...prev,
      [type === 'image' ? 'images' : type === 'file' ? 'files' : 'videos']: [
        ...prev[type === 'image' ? 'images' : type === 'file' ? 'files' : 'videos'],
        ...newResults
      ]
    }))

    setUploading(false)
  }

  const UploadSection = ({ 
    title, 
    type, 
    icon: Icon, 
    accept, 
    results 
  }: {
    title: string
    type: 'image' | 'file' | 'video'
    icon: any
    accept: string
    results: UploadResult[]
  }) => (
    <div className="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-6">
      <div className="flex items-center space-x-3 mb-4">
        <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">{title}</h3>
      </div>

      <div className="mb-4">
        <input
          type="file"
          accept={accept}
          multiple
          onChange={(e) => e.target.files && handleFileUpload(e.target.files, type)}
          className="block w-full text-sm text-gray-500 dark:text-gray-400
                     file:mr-4 file:py-2 file:px-4
                     file:rounded-lg file:border-0
                     file:text-sm file:font-medium
                     file:bg-blue-50 file:text-blue-700
                     dark:file:bg-blue-900/20 dark:file:text-blue-400
                     hover:file:bg-blue-100 dark:hover:file:bg-blue-900/30
                     file:cursor-pointer cursor-pointer"
          disabled={uploading}
        />
      </div>

      {results.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Upload Results:</h4>
          {results.map((result, index) => (
            <div
              key={index}
              className={`p-3 rounded-lg border ${
                result.success
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}
            >
              <div className="flex items-start space-x-2">
                {result.success ? (
                  <CheckCircleIcon className="w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
                ) : (
                  <XCircleIcon className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                )}
                <div className="flex-1 min-w-0">
                  {result.success ? (
                    <div className="text-sm">
                      <p className="font-medium text-green-800 dark:text-green-200">Upload Successful</p>
                      <p className="text-green-600 dark:text-green-300 truncate">
                        <strong>File:</strong> {result.filename}
                      </p>
                      <p className="text-green-600 dark:text-green-300 truncate">
                        <strong>Folder:</strong> {result.folder}
                      </p>
                      <p className="text-green-600 dark:text-green-300">
                        <strong>Size:</strong> {result.size ? `${(result.size / 1024).toFixed(1)} KB` : 'Unknown'}
                      </p>
                      {result.url && (
                        <a
                          href={result.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 dark:text-blue-400 hover:underline text-xs"
                        >
                          View File
                        </a>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm">
                      <p className="font-medium text-red-800 dark:text-red-200">Upload Failed</p>
                      <p className="text-red-600 dark:text-red-300">{result.error}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-slate-800 rounded-xl border border-gray-200 dark:border-slate-700 p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">
          Course File Upload Test
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Course ID
            </label>
            <input
              type="text"
              value={courseId}
              onChange={(e) => setCourseId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg 
                         bg-white dark:bg-slate-700 text-gray-900 dark:text-white
                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter course ID"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Lesson ID (for files/videos)
            </label>
            <input
              type="text"
              value={lessonId}
              onChange={(e) => setLessonId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-slate-600 rounded-lg 
                         bg-white dark:bg-slate-700 text-gray-900 dark:text-white
                         focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter lesson ID"
            />
          </div>
        </div>

        {uploading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-300">Uploading...</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <UploadSection
          title="Course Images"
          type="image"
          icon={PhotoIcon}
          accept="image/*"
          results={results.images}
        />
        
        <UploadSection
          title="Course Files"
          type="file"
          icon={DocumentIcon}
          accept=".pdf,.doc,.docx,.txt,.zip,.ppt,.pptx,.xls,.xlsx"
          results={results.files}
        />
        
        <UploadSection
          title="Course Videos"
          type="video"
          icon={VideoCameraIcon}
          accept="video/*"
          results={results.videos}
        />
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
        <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
          Expected File Structure
        </h3>
        <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <p><strong>Images:</strong> courses/{courseId}/images/</p>
          <p><strong>Files:</strong> courses/{courseId}/files/</p>
          <p><strong>Videos:</strong> courses/{courseId}/videos/</p>
        </div>
      </div>
    </div>
  )
}
