import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
// Use server-side socket manager, not client
import { z } from 'zod'

const participantActionSchema = z.object({
  action: z.enum(['kick', 'mute', 'unmute']),
  participantId: z.string().min(1, "Participant ID is required"),
  reason: z.string().optional()
})

// GET /api/admin/live-quiz/sessions/[id]/participants - Get session participants
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Verify session exists
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: { id: true, title: true, status: true }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Get participants with detailed information
      const participants = await prisma.liveQuizParticipant.findMany({
        where: { sessionId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        },
        orderBy: [
          { rank: 'asc' },
          { score: 'desc' },
          { joinedAt: 'asc' }
        ]
      })

      // Calculate additional statistics
      const stats = {
        totalParticipants: participants.length,
        activeParticipants: participants.filter(p => p.isActive).length,
        averageScore: participants.length > 0 
          ? Math.round(participants.reduce((sum, p) => sum + p.score, 0) / participants.length)
          : 0,
        averageProgress: participants.length > 0
          ? Math.round(participants.reduce((sum, p) => sum + p.currentQuestion, 0) / participants.length)
          : 0,
        completionRate: participants.length > 0
          ? Math.round((participants.filter(p => p.totalAnswered > 0).length / participants.length) * 100)
          : 0
      }

      return APIResponse.success({
        session: {
          id: session.id,
          title: session.title,
          status: session.status
        },
        participants: participants.map(p => ({
          id: p.id,
          userId: p.userId,
          user: p.user,
          joinedAt: p.joinedAt,
          leftAt: p.leftAt,
          currentQuestion: p.currentQuestion,
          score: p.score,
          correctAnswers: p.correctAnswers,
          totalAnswered: p.totalAnswered,
          isActive: p.isActive,
          rank: p.rank,
          timeSpent: p.timeSpent,
          progressPercentage: session.status === 'ACTIVE' && p.currentQuestion > 0 
            ? Math.round((p.currentQuestion / (p.currentQuestion + 1)) * 100)
            : 0
        })),
        stats
      }, 'Participants retrieved successfully')

    } catch (error) {
      console.error('Error fetching session participants:', error)
      return APIResponse.error('Failed to fetch session participants', 500)
    }
  }
)

// POST /api/admin/live-quiz/sessions/[id]/participants - Manage participants
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: participantActionSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string
    const { action, participantId, reason } = validatedBody

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Verify session exists
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        select: { id: true, title: true, status: true }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Get participant
      const participant = await prisma.liveQuizParticipant.findUnique({
        where: { id: participantId },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          }
        }
      })

      if (!participant) {
        return APIResponse.error('Participant not found', 404, 'PARTICIPANT_NOT_FOUND')
      }

      if (participant.sessionId !== sessionId) {
        return APIResponse.error('Participant does not belong to this session', 400, 'INVALID_PARTICIPANT')
      }

      let updatedParticipant
      let notificationMessage = ''
      let notificationType: 'info' | 'warning' | 'error' = 'info'

      switch (action) {
        case 'kick':
          // Remove participant from session
          updatedParticipant = await prisma.liveQuizParticipant.update({
            where: { id: participantId },
            data: {
              isActive: false,
              leftAt: new Date()
            },
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          })
          
          notificationMessage = `You have been removed from ${session.title}${reason ? `: ${reason}` : ''}`
          notificationType = 'error'
          break

        case 'mute':
          // Note: Muting functionality would need to be implemented in the socket layer
          // For now, we'll just log the action
          notificationMessage = `You have been muted in ${session.title}${reason ? `: ${reason}` : ''}`
          notificationType = 'warning'
          break

        case 'unmute':
          notificationMessage = `You have been unmuted in ${session.title}`
          notificationType = 'info'
          break

        default:
          return APIResponse.error('Invalid action', 400, 'INVALID_ACTION')
      }

      // Send notification to participant
      try {
        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()

        if (action === 'kick') {
          // Inform session room that participant was kicked
          socketManager?.broadcastToRoom(`live-quiz:${sessionId}`, 'live-quiz:participant-kicked', {
            sessionId,
            userId: participant.userId,
            reason
          })
        }

        // Send direct notification to the participant (user-scoped room)
        socketManager?.broadcastToRoom(`user:${participant.userId}`, 'notification:received', {
          id: Math.random().toString(36).substring(7),
          type: notificationType,
          title: 'Session Update',
          message: notificationMessage,
          data: {
            sessionId,
            action: `participant-${action}`,
            reason
          },
          createdAt: new Date()
        })

        // Notify other participants/admins in the session room
        socketManager?.broadcastToRoom(`live-quiz:${sessionId}`, 'live-quiz:participant-action', {
          sessionId,
          action,
          participantId,
          participantName: participant.user.name,
          reason,
          timestamp: new Date()
        })

      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        participant: updatedParticipant || participant,
        action,
        reason
      }, `Participant ${action} action completed successfully`)

    } catch (error) {
      console.error('Error managing participant:', error)
      return APIResponse.error('Failed to manage participant', 500)
    }
  }
)
