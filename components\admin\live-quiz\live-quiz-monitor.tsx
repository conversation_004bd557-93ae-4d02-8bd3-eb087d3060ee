"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Play, 
  Pause, 
  Square, 
  Users, 
  Clock, 
  BarChart3,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  UserX,
  Crown,
  Target,
  TrendingUp
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { getSocketClient } from "@/lib/socket-client"

interface LiveQuizSession {
  id: string
  title: string
  status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  currentQuestion: number
  quiz: {
    id: string
    title: string
    questionCount: number
  }
  participantCount: number
}

interface Participant {
  id: string
  userId: string
  user: {
    name: string
    email: string
  }
  joinedAt: string
  currentQuestion: number
  score: number
  correctAnswers: number
  totalAnswered: number
  isActive: boolean
  rank?: number
  timeSpent: number
}

interface LiveQuizMonitorProps {
  session: LiveQuizSession
  open: boolean
  onOpenChange: (open: boolean) => void
  onSessionUpdate: () => void
}

export function LiveQuizMonitor({ session, open, onOpenChange, onSessionUpdate }: LiveQuizMonitorProps) {
  const [participants, setParticipants] = useState<Participant[]>([])
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState({
    totalParticipants: 0,
    activeParticipants: 0,
    averageScore: 0,
    averageProgress: 0,
    completionRate: 0
  })

  useEffect(() => {
    if (open && session) {
      fetchParticipants()
      setupRealTimeUpdates()
      
      // Refresh every 10 seconds
      const interval = setInterval(fetchParticipants, 10000)
      return () => clearInterval(interval)
    }
  }, [open, session])

  const fetchParticipants = async () => {
    if (!session) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/live-quiz/sessions/${session.id}/participants`)
      const data = await response.json()

      if (data.success) {
        setParticipants(data.data.participants)
        setStats(data.data.stats)
      } else {
        throw new Error(data.message || 'Failed to fetch participants')
      }
    } catch (error) {
      console.error('Error fetching participants:', error)
      toast.error('Failed to fetch participants')
    } finally {
      setLoading(false)
    }
  }

  const setupRealTimeUpdates = () => {
    const socketClient = getSocketClient()
    
    // Listen for real-time updates
    socketClient.on('live-quiz:participant-joined', (data: any) => {
      if (data.sessionId === session.id) {
        fetchParticipants()
      }
    })

    socketClient.on('live-quiz:participant-left', (data: any) => {
      if (data.sessionId === session.id) {
        fetchParticipants()
      }
    })

    socketClient.on('live-quiz:participant-progress', (data: any) => {
      if (data.sessionId === session.id) {
        fetchParticipants()
      }
    })

    return () => {
      socketClient.off('live-quiz:participant-joined')
      socketClient.off('live-quiz:participant-left')
      socketClient.off('live-quiz:participant-progress')
    }
  }

  const handleQuestionControl = async (action: 'next' | 'previous') => {
    try {
      const response = await fetch(`/api/admin/live-quiz/sessions/${session.id}/question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Question ${action === 'next' ? 'advanced' : 'moved back'}`)
        onSessionUpdate()
      } else {
        throw new Error(data.message || `Failed to ${action} question`)
      }
    } catch (error) {
      console.error(`Error ${action}ing question:`, error)
      toast.error(`Failed to ${action} question`)
    }
  }

  const handleParticipantAction = async (participantId: string, action: 'kick') => {
    try {
      const response = await fetch(`/api/admin/live-quiz/sessions/${session.id}/participants`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action, 
          participantId,
          reason: 'Removed by admin'
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Participant removed')
        fetchParticipants()
      } else {
        throw new Error(data.message || 'Failed to remove participant')
      }
    } catch (error) {
      console.error('Error removing participant:', error)
      toast.error('Failed to remove participant')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'PAUSED': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getProgress = () => {
    if (session.quiz.questionCount === 0) return 0
    return Math.round(((session.currentQuestion + 1) / session.quiz.questionCount) * 100)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Live Quiz Monitor
            <Badge className={getStatusColor(session.status)}>
              {session.status}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            {session.title} • {session.quiz.title}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Session Controls */}
          {session.status === 'ACTIVE' && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Question Control</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuestionControl('previous')}
                      disabled={session.currentQuestion === 0}
                    >
                      <ChevronLeft className="h-4 w-4 mr-2" />
                      Previous
                    </Button>
                    <div className="text-center">
                      <div className="text-lg font-semibold">
                        Question {session.currentQuestion + 1} of {session.quiz.questionCount}
                      </div>
                      <Progress value={getProgress()} className="w-32 mt-1" />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuestionControl('next')}
                      disabled={session.currentQuestion >= session.quiz.questionCount - 1}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchParticipants}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Stats Overview */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{stats.totalParticipants}</div>
                <p className="text-xs text-muted-foreground">Total Participants</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{stats.activeParticipants}</div>
                <p className="text-xs text-muted-foreground">Active Now</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{stats.averageScore}</div>
                <p className="text-xs text-muted-foreground">Avg Score</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{stats.averageProgress}</div>
                <p className="text-xs text-muted-foreground">Avg Progress</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="text-2xl font-bold">{stats.completionRate}%</div>
                <p className="text-xs text-muted-foreground">Completion Rate</p>
              </CardContent>
            </Card>
          </div>

          {/* Participants */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Participants ({participants.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {participants.map((participant, index) => (
                    <motion.div
                      key={participant.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="flex items-center justify-between p-3 rounded-lg border"
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {participant.rank === 1 && <Crown className="h-4 w-4 text-yellow-500" />}
                          <div className="text-sm font-medium">
                            #{participant.rank || '-'}
                          </div>
                        </div>
                        <div>
                          <div className="font-medium">{participant.user.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {participant.user.email}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-medium">{participant.score}</div>
                          <div className="text-xs text-muted-foreground">Score</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">
                            {participant.correctAnswers}/{participant.totalAnswered}
                          </div>
                          <div className="text-xs text-muted-foreground">Correct</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{Math.round(participant.timeSpent / 60)}m</div>
                          <div className="text-xs text-muted-foreground">Time</div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge variant={participant.isActive ? "default" : "secondary"}>
                            {participant.isActive ? "Active" : "Left"}
                          </Badge>
                          {participant.isActive && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleParticipantAction(participant.id, 'kick')}
                            >
                              <UserX className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
