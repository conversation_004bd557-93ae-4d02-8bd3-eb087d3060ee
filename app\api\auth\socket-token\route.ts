import { NextRequest, NextResponse } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON> } from '@/lib/api-middleware'
import { signHmacJwt } from '@/lib/jwt'

export const GET = createAPIHandler({ requireAuth: true }, async (request: NextRequest, { session }) => {
  const secret = process.env.AUTH_SECRET || process.env.NEXTAUTH_SECRET || ''
  if (!secret) {
    return NextResponse.json({ error: 'Server misconfiguration' }, { status: 500 })
  }

  const token = signHmacJwt(
    {
      sub: session!.user.id,
      name: session!.user.name,
      email: session!.user.email,
      role: session!.user.role,
      purpose: 'socket-auth'
    },
    secret,
    60 * 5
  )

  return NextResponse.json({ token })
})


