'use client'

import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Bars3Icon } from '@heroicons/react/24/outline'
import { ReactNode } from 'react'

interface SortableItemProps {
  id: string
  children: ReactNode
  disabled?: boolean
  className?: string
}

export default function SortableItem({ 
  id, 
  children, 
  disabled = false,
  className = '' 
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id,
    disabled 
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        relative group
        ${isDragging ? 'opacity-50 z-50' : ''}
        ${className}
      `}
    >
      {/* Drag Handle */}
      {!disabled && (
        <div
          {...attributes}
          {...listeners}
          className="absolute left-2 top-1/2 -translate-y-1/2 p-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing z-10"
        >
          <Bars3Icon className="w-4 h-4 text-gray-400 hover:text-gray-600" />
        </div>
      )}
      
      {/* Content */}
      <div className={`${!disabled ? 'pl-8' : ''}`}>
        {children}
      </div>
    </div>
  )
}
