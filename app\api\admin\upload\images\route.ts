import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>andler, APIResponse } from '@/lib/api-middleware'
import { courseFileManager } from '@/lib/course-file-manager'

// POST /api/admin/upload/images - Upload image files
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('image') as File
      const type = formData.get('type') as string || 'general'
      const aspectRatio = formData.get('aspectRatio') as string || 'video'
      const courseId = formData.get('courseId') as string

      if (!file) {
        return APIResponse.error('No image file provided', 400)
      }

      if (!courseId) {
        return APIResponse.error('Course ID is required for organized file storage', 400)
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        return APIResponse.error(`File type ${file.type} is not allowed. Please use JPG, PNG, WebP, or GIF.`, 400)
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return APIResponse.error('Image file too large. Maximum size is 5MB', 400)
      }

      // Upload image using course file manager
      const uploadResult = await courseFileManager.uploadCourseFile(file, {
        courseId,
        type: 'image',
        category: type
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload image: ${uploadResult.error}`,
          500
        )
      }

      // Create image object with course context
      const timestamp = Date.now()
      const imageData = {
        id: `image-${timestamp}`,
        url: uploadResult.url!,
        filename: uploadResult.filename!,
        originalName: file.name,
        type: file.type,
        size: uploadResult.size!,
        aspectRatio: aspectRatio,
        uploadedAt: new Date().toISOString(),
        imageType: type,
        courseId: courseId,
        folder: uploadResult.folder!
      }

      return APIResponse.success({
        message: 'Image uploaded successfully to course-specific folder',
        ...imageData
      })

    } catch (error) {
      console.error('Error uploading image:', error)
      return APIResponse.error('Failed to upload image', 500)
    }
  }
)
