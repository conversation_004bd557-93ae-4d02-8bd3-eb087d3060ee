import Razorpay from 'razorpay'
import crypto from 'crypto'
import { prisma } from '@/lib/prisma'

export interface RazorpayConfig {
  keyId: string
  keySecret: string
  webhookSecret: string
}

export interface CreateOrderParams {
  amount: number // Amount in INR
  currency?: string
  courseId?: string // For course payments
  bundleId?: string // For bundle payments
  userId: string
  courseName: string // Can be course or bundle name
  userEmail: string
  userName: string
  paymentType?: 'course' | 'bundle'
}

export interface PaymentVerificationParams {
  razorpayOrderId: string
  razorpayPaymentId: string
  razorpaySignature: string
}

export interface PaymentStatus {
  success: boolean
  message: string
  paymentId?: string
  enrollmentId?: string
}

class RazorpayService {
  private razorpay: Razorpay
  private config: RazorpayConfig

  constructor() {
    this.config = {
      keyId: process.env.RAZORPAY_KEY_ID || '',
      keySecret: process.env.RAZORPAY_KEY_SECRET || '',
      webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET || ''
    }

    if (!this.config.keyId || !this.config.keySecret) {
      throw new Error('Razorpay configuration is incomplete. Please check environment variables.')
    }

    this.razorpay = new Razorpay({
      key_id: this.config.keyId,
      key_secret: this.config.keySecret
    })
  }

  /**
   * Public accessor for the configured Razorpay key id (for client checkout)
   */
  getPublicKeyId(): string {
    return this.config.keyId
  }

  /**
   * Create a new Razorpay order for course or bundle payment
   */
  async createOrder(params: CreateOrderParams): Promise<{
    orderId: string
    amount: number
    currency: string
    keyId: string
  }> {
    try {
      const {
        amount,
        currency = 'INR',
        courseId,
        bundleId,
        userId,
        courseName,
        userEmail,
        userName,
        paymentType = courseId ? 'course' : 'bundle'
      } = params

      // Convert amount to paise (Razorpay expects amount in smallest currency unit)
      const amountInPaise = Math.round(amount * 100)

      // Create Razorpay order
      const receiptPrefix = paymentType === 'course' ? 'c' : 'b'
      const itemId = courseId || bundleId

      // Create a compact receipt that stays within 40 characters
      // Format: {prefix}_{shortId}_{timestamp}
      const shortId = itemId ? itemId.substring(0, 8) : 'unknown'
      const timestamp = Date.now().toString().slice(-8) // Last 8 digits
      const receipt = `${receiptPrefix}_${shortId}_${timestamp}`

      const order = await this.razorpay.orders.create({
        amount: amountInPaise,
        currency,
        receipt: receipt,
        notes: {
          courseId: courseId || '',
          bundleId: bundleId || '',
          userId,
          courseName,
          userEmail,
          userName,
          paymentType
        }
      })

      // Save payment record in database
      await prisma.payment.create({
        data: {
          userId,
          courseId,
          bundleId,
          razorpayOrderId: order.id,
          amount,
          currency,
          status: 'created',
          paymentType,
          metadata: {
            courseName,
            userEmail,
            userName,
            orderReceipt: order.receipt,
            paymentType
          }
        }
      })

      // Create payment history entry
      await this.createPaymentHistory(order.id, 'created', 'Order created successfully')

      return {
        orderId: order.id,
        amount: amountInPaise,
        currency,
        keyId: this.config.keyId
      }
    } catch (error) {
      console.error('Error creating Razorpay order:', error)
      throw new Error('Failed to create payment order')
    }
  }

  /**
   * Verify payment signature and process successful payment
   */
  async verifyPayment(params: PaymentVerificationParams): Promise<PaymentStatus> {
    try {
      const { razorpayOrderId, razorpayPaymentId, razorpaySignature } = params

      // Verify signature
      const isSignatureValid = this.verifySignature(razorpayOrderId, razorpayPaymentId, razorpaySignature)

      if (!isSignatureValid) {
        await this.updatePaymentStatus(razorpayOrderId, 'failed', 'Invalid payment signature')
        return {
          success: false,
          message: 'Payment verification failed. Invalid signature.'
        }
      }

      // Get payment record
      const payment = await prisma.payment.findUnique({
        where: { razorpayOrderId },
        include: {
          user: { select: { id: true, name: true, email: true } },
          course: { select: { id: true, title: true, slug: true } }
        }
      })

      if (!payment) {
        return {
          success: false,
          message: 'Payment record not found'
        }
      }

      // Update payment status
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          razorpayPaymentId,
          razorpaySignature,
          status: 'paid',
          paidAt: new Date()
        }
      })

      // Create payment history entry
      await this.createPaymentHistory(payment.id, 'paid', 'Payment completed successfully')

      // Create enrollment or bundle purchase based on payment type
      let enrollmentId: string

      if (payment.paymentType === 'bundle' && payment.bundleId) {
        // Create bundle purchase - allow update during payment verification
        const { quizBundleService } = await import('@/lib/quiz-bundle-service')
        const bundlePurchase = await quizBundleService.purchaseBundle(
          payment.userId,
          payment.bundleId,
          payment.id,
          true // allowUpdate = true for payment verification
        )
        enrollmentId = bundlePurchase.id
      } else if (payment.courseId) {
        // Create course enrollment
        const enrollment = await this.createEnrollment(payment.userId, payment.courseId, payment.id)
        enrollmentId = enrollment.id
      } else {
        throw new Error('Invalid payment type or missing course/bundle ID')
      }

      return {
        success: true,
        message: 'Payment verified and enrollment completed successfully',
        paymentId: payment.id,
        enrollmentId: enrollmentId
      }
    } catch (error) {
      console.error('Error verifying payment:', error)
      return {
        success: false,
        message: 'Payment verification failed due to server error'
      }
    }
  }

  /**
   * Handle payment failure
   */
  async handlePaymentFailure(orderId: string, reason: string): Promise<void> {
    try {
      await this.updatePaymentStatus(orderId, 'failed', reason)
    } catch (error) {
      console.error('Error handling payment failure:', error)
    }
  }

  /**
   * Verify Razorpay signature
   */
  private verifySignature(orderId: string, paymentId: string, signature: string): boolean {
    try {
      const body = orderId + '|' + paymentId
      const expectedSignature = crypto
        .createHmac('sha256', this.config.keySecret)
        .update(body.toString())
        .digest('hex')

      return expectedSignature === signature
    } catch (error) {
      console.error('Error verifying signature:', error)
      return false
    }
  }

  /**
   * Update payment status in database
   */
  private async updatePaymentStatus(orderId: string, status: string, reason?: string): Promise<void> {
    try {
      await prisma.payment.update({
        where: { razorpayOrderId: orderId },
        data: {
          status,
          failureReason: reason
        }
      })

      await this.createPaymentHistory(orderId, status, reason || `Status updated to ${status}`)
    } catch (error) {
      console.error('Error updating payment status:', error)
    }
  }

  /**
   * Mark a payment as paid (idempotent) and fulfill access (enrollment or bundle purchase).
   * Designed for webhook-based reconciliation where no checkout signature is available.
   */
  async markPaymentPaidAndFulfill(razorpayOrderId: string, razorpayPaymentId: string): Promise<PaymentStatus> {
    try {
      const payment = await prisma.payment.findUnique({
        where: { razorpayOrderId },
      })

      if (!payment) {
        return { success: false, message: 'Payment record not found' }
      }

      // If already marked paid, treat as success (idempotent)
      if (payment.status === 'paid') {
        return { success: true, message: 'Payment already processed', paymentId: payment.id }
      }

      // Update payment as paid
      const updated = await prisma.payment.update({
        where: { id: payment.id },
        data: {
          razorpayPaymentId,
          status: 'paid',
          paidAt: new Date(),
        },
      })

      await this.createPaymentHistory(updated.id, 'paid', 'Payment captured via webhook')

      // Fulfill access
      let enrollmentId: string | undefined
      if (updated.paymentType === 'bundle' && updated.bundleId) {
        const { quizBundleService } = await import('@/lib/quiz-bundle-service')
        const purchase = await quizBundleService.purchaseBundle(
          updated.userId,
          updated.bundleId,
          updated.id,
          true
        )
        enrollmentId = purchase.id
      } else if (updated.courseId) {
        const enrollment = await this.createEnrollment(updated.userId, updated.courseId, updated.id)
        enrollmentId = enrollment.id
      }

      return {
        success: true,
        message: 'Payment processed via webhook',
        paymentId: updated.id,
        enrollmentId,
      }
    } catch (error) {
      console.error('Error fulfilling webhook payment:', error)
      return { success: false, message: 'Failed to process webhook payment' }
    }
  }

  /**
   * Mark a payment as failed (idempotent-safe)
   */
  async markPaymentFailed(orderId: string, reason?: string): Promise<void> {
    try {
      await this.updatePaymentStatus(orderId, 'failed', reason)
    } catch (error) {
      console.error('Error marking payment failed:', error)
    }
  }

  /**
   * Create payment history entry
   */
  private async createPaymentHistory(paymentId: string, status: string, reason?: string): Promise<void> {
    try {
      // Find payment by either ID or Razorpay order ID
      const payment = await prisma.payment.findFirst({
        where: {
          OR: [
            { id: paymentId },
            { razorpayOrderId: paymentId }
          ]
        }
      })

      if (payment) {
        await prisma.paymentHistory.create({
          data: {
            paymentId: payment.id,
            status,
            reason,
            metadata: {
              timestamp: new Date().toISOString()
            }
          }
        })
      }
    } catch (error) {
      console.error('Error creating payment history:', error)
    }
  }

  /**
   * Create course enrollment after successful payment
   */
  private async createEnrollment(userId: string, courseId: string, paymentId: string) {
    try {
      // Check if enrollment already exists
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId,
            courseId
          }
        }
      })

      if (existingEnrollment) {
        // Update existing enrollment with payment reference
        return await prisma.courseEnrollment.update({
          where: { id: existingEnrollment.id },
          data: {
            status: 'active',
            paymentId,
            enrolledAt: new Date()
          }
        })
      } else {
        // Create new enrollment
        return await prisma.courseEnrollment.create({
          data: {
            userId,
            courseId,
            paymentId,
            status: 'active',
            progress: 0
          }
        })
      }
    } catch (error) {
      console.error('Error creating enrollment:', error)
      throw error
    }
  }

  /**
   * Get payment details by order ID
   */
  async getPaymentByOrderId(orderId: string) {
    try {
      return await prisma.payment.findUnique({
        where: { razorpayOrderId: orderId },
        include: {
          user: { select: { id: true, name: true, email: true } },
          course: { select: { id: true, title: true, slug: true, price: true } },
          history: { orderBy: { createdAt: 'desc' } }
        }
      })
    } catch (error) {
      console.error('Error fetching payment:', error)
      return null
    }
  }

  /**
   * Get user's payment history
   */
  async getUserPayments(userId: string) {
    try {
      return await prisma.payment.findMany({
        where: { userId },
        include: {
          course: { select: { id: true, title: true, slug: true, thumbnailImage: true } },
          enrollments: { select: { id: true, status: true, progress: true } }
        },
        orderBy: { createdAt: 'desc' }
      })
    } catch (error) {
      console.error('Error fetching user payments:', error)
      return []
    }
  }
}

// Export singleton instance
export const razorpayService = new RazorpayService()
