/**
 * Course Service
 * 
 * Centralized service for handling course-related API calls with comprehensive
 * error handling, retry logic, and loading states.
 */

import { toast } from 'sonner'

export interface Course {
  id: string
  productId: string
  title: string
  description?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  duration?: string
  instructor?: string | {
    id: string
    name: string
    image?: string
  }
  rating?: number
  studentsCount?: number
  features: string[]
  tags: string[]
  isEnrolled?: boolean
  enrollmentCount?: number
}

export interface EnrolledCourse {
  enrollmentId: string
  enrolledAt: string
  progress: number
  status: 'active' | 'completed' | 'paused'
  lastAccessedAt?: string
  completedAt?: string
  course: Course
}

export interface CourseFilters {
  search?: string
  category?: string
  minPrice?: number
  maxPrice?: number
  page?: number
  limit?: number
  sync?: boolean
}

export interface ApiResponse<T> {
  data?: T
  message?: string
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export class CourseServiceError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string
  ) {
    super(message)
    this.name = 'CourseServiceError'
  }
}

class CourseService {
  private baseUrl = '/api/courses'
  private retryAttempts = 3
  private retryDelay = 1000

  private async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    attempt = 1
  ): Promise<Response> {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })

      if (!response.ok) {
        throw new CourseServiceError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status
        )
      }

      return response
    } catch (error) {
      if (attempt < this.retryAttempts && this.shouldRetry(error)) {
        await this.delay(this.retryDelay * attempt)
        return this.fetchWithRetry(url, options, attempt + 1)
      }
      throw error
    }
  }

  private shouldRetry(error: unknown): boolean {
    if (error instanceof CourseServiceError) {
      // Retry on server errors but not client errors
      return error.statusCode ? error.statusCode >= 500 : false
    }
    // Retry on network errors
    return error instanceof TypeError && error.message.includes('fetch')
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private handleError(error: unknown, context: string): never {
    console.error(`Course Service Error (${context}):`, error)
    
    if (error instanceof CourseServiceError) {
      throw error
    }
    
    if (error instanceof Error) {
      throw new CourseServiceError(error.message)
    }
    
    throw new CourseServiceError(`Unknown error in ${context}`)
  }

  async getCourses(filters: CourseFilters = {}): Promise<{
    courses: Course[]
    pagination?: any
  }> {
    try {
      const params = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value))
        }
      })

      const response = await this.fetchWithRetry(`${this.baseUrl}?${params}`)
      const data = await response.json()

      return {
        courses: data.courses || [],
        pagination: data.pagination
      }
    } catch (error) {
      this.handleError(error, 'getCourses')
    }
  }

  async getMyCourses(status: string = 'all', sync: boolean = false): Promise<{
    enrollments: EnrolledCourse[]
    summary: any
    pagination?: any
  }> {
    try {
      const params = new URLSearchParams({
        status,
        sync: String(sync),
        limit: '20'
      })

      const response = await this.fetchWithRetry(`${this.baseUrl}/my-courses?${params}`)
      const data = await response.json()

      return {
        enrollments: data.enrollments || [],
        summary: data.summary || {},
        pagination: data.pagination
      }
    } catch (error) {
      this.handleError(error, 'getMyCourses')
    }
  }

  async getCategories(): Promise<any[]> {
    try {
      const response = await this.fetchWithRetry(`${this.baseUrl}/categories`)
      const data = await response.json()
      return data.categories || []
    } catch (error) {
      this.handleError(error, 'getCategories')
    }
  }

  async enrollInCourse(courseId: string): Promise<{
    redirectUrl?: string
    message?: string
  }> {
    try {
      const response = await this.fetchWithRetry(`${this.baseUrl}/enroll`, {
        method: 'POST',
        body: JSON.stringify({ courseId })
      })

      const data = await response.json()
      
      if (data.redirectUrl) {
        toast.success('Redirecting to course enrollment...')
      }

      return data
    } catch (error) {
      this.handleError(error, 'enrollInCourse')
    }
  }

  async syncCourses(): Promise<void> {
    try {
      await this.fetchWithRetry(`${this.baseUrl}?sync=true&limit=1`)
      toast.success('Courses synced successfully')
    } catch (error) {
      toast.error('Failed to sync courses')
      this.handleError(error, 'syncCourses')
    }
  }

  async syncEnrollments(): Promise<void> {
    try {
      await this.fetchWithRetry(`${this.baseUrl}/my-courses?sync=true&limit=1`)
      toast.success('Enrollments synced successfully')
    } catch (error) {
      toast.error('Failed to sync enrollments')
      this.handleError(error, 'syncEnrollments')
    }
  }
}

// Singleton instance
export const courseService = new CourseService()

// React hooks for course data
export function useCourseErrorHandler() {
  const handleError = (error: unknown, showToast = true) => {
    let message = 'An unexpected error occurred'

    if (error instanceof CourseServiceError) {
      message = error.message
    } else if (error instanceof Error) {
      message = error.message
    }

    if (showToast) {
      toast.error(message)
    }

    return message
  }

  return { handleError }
}

// Utility functions
export function getCourseUrl(slug: string, isEnrolled: boolean = false): string {
  if (isEnrolled) {
    return `/student/courses/${slug}`
  }
  return `/courses/${slug}`
}

export function formatCoursePrice(price: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0
  }).format(price)
}

export function calculateDiscountPercentage(originalPrice: number, currentPrice: number): number {
  if (originalPrice <= currentPrice) return 0
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
    case 'paused':
      return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300'
    case 'active':
    default:
      return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
  }
}

export function formatEnrollmentDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
