import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler } from '@/lib/api-middleware'
import { notificationService } from '@/lib/notification-service'
import { NotificationType } from '@/lib/generated/prisma'

// GET /api/notifications - Get user notifications
export const GET = createAPIHandler({ requireAuth: true }, async (request: NextRequest, { session }) => {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const unreadOnly = searchParams.get('unreadOnly') === 'true'
    const category = searchParams.get('category') || undefined
    const type = searchParams.get('type') as NotificationType || undefined

    const result = await notificationService.getUserNotifications(
      session!.user.id,
      { page, limit, unreadOnly, category, type }
    )

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    )
  }
})

// POST /api/notifications - Send notification (Admin only)
export const POST = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN' }, async (request: NextRequest) => {
  try {
    const body = await request.json()
    const {
      recipients,
      type,
      title,
      message,
      data,
      actionUrl,
      imageUrl,
      priority,
      category,
      expiresAt,
      scheduledAt,
      sendToAll,
      sendToRole
    } = body

    if (!type || !title || !message) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, message' },
        { status: 400 }
      )
    }

    const notificationData = {
      type,
      title,
      message,
      data,
      actionUrl,
      imageUrl,
      priority,
      category,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
      scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined
    }

    let notificationId: string

    if (sendToAll) {
      notificationId = await notificationService.sendToAll(notificationData)
    } else if (sendToRole) {
      notificationId = await notificationService.sendToRole(sendToRole, notificationData)
    } else if (recipients && Array.isArray(recipients)) {
      notificationId = await notificationService.sendToUsers(recipients, notificationData)
    } else {
      return NextResponse.json(
        { error: 'Must specify recipients, sendToAll, or sendToRole' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      notificationId,
      message: 'Notification sent successfully'
    })
  } catch (error) {
    console.error('Error sending notification:', error)
    return NextResponse.json(
      { error: 'Failed to send notification' },
      { status: 500 }
    )
  }
})

// GET /api/notifications/unread-count - Get unread notification count
export const HEAD = createAPIHandler({ requireAuth: true }, async (request: NextRequest, { session }) => {
  try {
    const count = await notificationService.getUnreadCount(session!.user.id)
    return NextResponse.json({ count })
  } catch (error) {
    console.error('Error getting unread count:', error)
    return NextResponse.json(
      { error: 'Failed to get unread count' },
      { status: 500 }
    )
  }
})
