import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage } from '@/lib/bunny-storage'

// Allowed image types
const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
]

// POST /api/admin/quiz-bundles/upload/images - Upload quiz bundle images
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest) => {
    try {
      const formData = await request.formData()
      const file = formData.get('image') as File
      const bundleId = formData.get('bundleId') as string
      const imageType = formData.get('type') as string || 'thumbnail'

      if (!file) {
        return APIResponse.error('No image file provided', 400)
      }

      if (!bundleId) {
        return APIResponse.error('Bundle ID is required', 400)
      }

      // Validate file type
      if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
        return APIResponse.error(
          `Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`,
          400
        )
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        return APIResponse.error('Image file too large. Maximum size is 5MB', 400)
      }

      // Upload to Bunny CDN in quiz bundle specific folder
      const bunnyStorage = getBunnyStorage()
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `courses/${bundleId}/images`,
        filename: `${imageType}_${Date.now()}.${file.name.split('.').pop()}`,
        contentType: file.type,
        optimize: true,
        maxSize: maxSize
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload image: ${uploadResult.error}`,
          500
        )
      }

      return APIResponse.success({
        message: 'Image uploaded successfully',
        url: uploadResult.url,
        filename: uploadResult.filename,
        size: uploadResult.size,
        type: file.type,
        folder: `courses/${bundleId}/images`,
        imageType: imageType
      })

    } catch (error) {
      console.error('Error uploading quiz bundle image:', error)
      return APIResponse.error('Failed to upload image', 500)
    }
  }
)
