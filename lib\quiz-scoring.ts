/**
 * Centralized quiz scoring service
 * Handles evaluation logic for all question types
 */

export interface QuestionResult {
  questionId: string
  questionText: string
  questionType: string
  options?: string[]
  correctAnswer: string
  selectedAnswer: any
  isCorrect: boolean
  points: number
  maxPoints: number
  explanation?: string
  difficulty?: string
  tags?: string[]
  timeSpent?: number
}

export interface QuizScoringResult {
  totalQuestions: number
  correctAnswers: number
  incorrectAnswers: number
  unansweredQuestions: number
  totalPoints: number
  maxPossiblePoints: number
  percentage: number
  questionResults: QuestionResult[]
}

/**
 * Evaluates if an answer is correct based on question type
 */
export function evaluateAnswer(
  questionType: string,
  userAnswer: any,
  correctAnswer: string
): boolean {
  switch (questionType) {
    case 'MCQ':
      return userAnswer === correctAnswer

    case 'TRUE_FALSE':
      // Handle boolean comparison properly
      const correctBool = correctAnswer === 'true' || (correctAnswer as any) === true
      const userBool = userAnswer === true || userAnswer === 'true'
      return userBool === correctBool

    case 'SHORT_ANSWER':
      // Case-insensitive string comparison
      if (typeof userAnswer === 'string' && typeof correctAnswer === 'string') {
        return userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim()
      }
      return false

    case 'MATCHING':
      // For matching questions, user answer is array of selected right items
      // Correct answer is JSON string of pairs: [{"left":"A","right":"B"}]
      if (Array.isArray(userAnswer) && typeof correctAnswer === 'string') {
        try {
          const correctPairs = JSON.parse(correctAnswer)
          if (Array.isArray(correctPairs)) {
            // Extract the correct right items in order
            const correctRightItems = correctPairs.map((pair: any) => pair.right)
            // Compare the user's answers with correct answers (order matters)
            return JSON.stringify(userAnswer) === JSON.stringify(correctRightItems)
          }
        } catch (e) {
          console.error('Error parsing matching question correct answer:', e)
          return false
        }
      }
      return false

    case 'FILL_BLANK':
    case 'ESSAY':
      // For now, these require manual grading
      // Could implement keyword matching for fill-in-the-blank
      return false

    default:
      // Fallback to simple comparison
      return userAnswer === correctAnswer
  }
}

/**
 * Scores a complete quiz attempt
 */
export function scoreQuizAttempt(
  questions: Array<{
    id: string
    text: string
    type: string
    options?: string[]
    correctAnswer: string
    explanation?: string
    points: number
    difficulty?: string
    tags?: string[]
  }>,
  userAnswers: Record<string, any>,
  timeSpentPerQuestion?: Record<string, number>
): QuizScoringResult {
  const questionResults: QuestionResult[] = []
  let correctAnswers = 0
  let incorrectAnswers = 0
  let unansweredQuestions = 0
  let totalPoints = 0

  questions.forEach(question => {
    const userAnswer = userAnswers[question.id]
    const hasAnswer = userAnswer !== undefined && userAnswer !== null && userAnswer !== ''
    
    let isCorrect = false
    if (hasAnswer) {
      isCorrect = evaluateAnswer(question.type, userAnswer, question.correctAnswer)
      if (isCorrect) {
        correctAnswers++
        totalPoints += question.points
      } else {
        incorrectAnswers++
      }
    } else {
      unansweredQuestions++
    }

    questionResults.push({
      questionId: question.id,
      questionText: question.text,
      questionType: question.type,
      options: question.options,
      correctAnswer: question.correctAnswer,
      selectedAnswer: userAnswer,
      isCorrect,
      points: isCorrect ? question.points : 0,
      maxPoints: question.points,
      explanation: question.explanation,
      difficulty: question.difficulty,
      tags: question.tags,
      timeSpent: timeSpentPerQuestion?.[question.id]
    })
  })

  const maxPossiblePoints = questions.reduce((sum, q) => sum + q.points, 0)
  const percentage = maxPossiblePoints > 0 ? Math.round((totalPoints / maxPossiblePoints) * 100) : 0

  return {
    totalQuestions: questions.length,
    correctAnswers,
    incorrectAnswers,
    unansweredQuestions,
    totalPoints,
    maxPossiblePoints,
    percentage,
    questionResults
  }
}

/**
 * Helper function to format question results for database storage
 */
export function formatQuestionResultsForStorage(questionResults: QuestionResult[]) {
  return questionResults.map(result => ({
    questionId: result.questionId,
    selectedAnswer: result.selectedAnswer,
    isCorrect: result.isCorrect,
    pointsEarned: result.points,
    timeSpent: result.timeSpent || 0
  }))
}

/**
 * Helper function to parse stored question results
 */
export function parseStoredQuestionResults(
  storedResults: any[],
  questions: Array<{
    id: string
    text: string
    type: string
    options?: string[]
    correctAnswer: string
    explanation?: string
    points: number
    difficulty?: string
    tags?: string[]
  }>
): QuestionResult[] {
  return questions.map(question => {
    const stored = storedResults.find(r => r.questionId === question.id)
    
    return {
      questionId: question.id,
      questionText: question.text,
      questionType: question.type,
      options: question.options,
      correctAnswer: question.correctAnswer,
      selectedAnswer: stored?.selectedAnswer,
      isCorrect: stored?.isCorrect || false,
      points: stored?.pointsEarned || 0,
      maxPoints: question.points,
      explanation: question.explanation,
      difficulty: question.difficulty,
      tags: question.tags,
      timeSpent: stored?.timeSpent
    }
  })
}
