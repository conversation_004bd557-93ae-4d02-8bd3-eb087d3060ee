"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Play, 
  Clock, 
  Users, 
  Eye,
  Calendar,
  Zap,
  Target,
  Trophy,
  Timer,
  UserCheck,
  AlertCircle
} from "lucide-react"
import { motion } from "framer-motion"
import { formatDistanceToNow, format } from "date-fns"

interface LiveQuizSession {
  id: string
  title: string
  description?: string
  status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  maxParticipants?: number
  currentQuestion: number
  questionTimeLimit?: number
  autoAdvance: boolean
  showLeaderboard: boolean
  allowLateJoin: boolean
  startTime?: string
  scheduledStart?: string
  createdAt: string
  quiz: {
    id: string
    title: string
    description?: string
    difficulty: string
    timeLimit?: number
    questionCount: number
    category?: string
    tags: string[]
    thumbnail?: string
  }
  creator: {
    id: string
    name: string
  }
  participantCount: number
  isParticipating: boolean
  canJoin: boolean
  participants: Array<{
    id: string
    userName: string
  }>
}

interface LiveQuizCardProps {
  session: LiveQuizSession
  onJoin: (sessionId: string) => void
  onView: (sessionId: string) => void
}

export function LiveQuizCard({ session, onJoin, onView }: LiveQuizCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'PAUSED': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'CANCELLED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'HARD': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'WAITING': return <Clock className="h-3 w-3" />
      case 'ACTIVE': return <Zap className="h-3 w-3" />
      case 'PAUSED': return <AlertCircle className="h-3 w-3" />
      case 'COMPLETED': return <Trophy className="h-3 w-3" />
      case 'CANCELLED': return <AlertCircle className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  const getProgress = () => {
    if (session.quiz.questionCount === 0) return 0
    return Math.round(((session.currentQuestion + 1) / session.quiz.questionCount) * 100)
  }

  const getParticipantStatus = () => {
    if (session.isParticipating) {
      return { text: 'Participating', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200', icon: <UserCheck className="h-3 w-3" /> }
    }
    if (session.canJoin) {
      return { text: 'Can Join', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200', icon: <Play className="h-3 w-3" /> }
    }
    return { text: 'Full', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200', icon: <AlertCircle className="h-3 w-3" /> }
  }

  const participantStatus = getParticipantStatus()

  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full glass border-2 hover:border-primary/30 transition-all duration-300 overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate">
                {session.title}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {session.quiz.title}
              </CardDescription>
            </div>
            <div className="flex flex-col gap-1 ml-2">
              <Badge className={`${getStatusColor(session.status)} text-xs`}>
                {getStatusIcon(session.status)}
                <span className="ml-1">{session.status}</span>
              </Badge>
              <Badge className={`${participantStatus.color} text-xs`}>
                {participantStatus.icon}
                <span className="ml-1">{participantStatus.text}</span>
              </Badge>
            </div>
          </div>

          {/* Quiz Info */}
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Badge className={getDifficultyColor(session.quiz.difficulty)} variant="outline">
              <Target className="h-3 w-3 mr-1" />
              {session.quiz.difficulty}
            </Badge>
            {session.quiz.category && (
              <Badge variant="outline">
                {session.quiz.category}
              </Badge>
            )}
          </div>
        </div>

        <CardContent className="p-4 space-y-4">
          {/* Description */}
          {session.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {session.description}
            </p>
          )}

          {/* Session Stats */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>{session.participantCount}</span>
              {session.maxParticipants && (
                <span className="text-muted-foreground">/ {session.maxParticipants}</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span>{session.quiz.questionCount} questions</span>
            </div>
            {session.questionTimeLimit && (
              <div className="flex items-center gap-2">
                <Timer className="h-4 w-4 text-muted-foreground" />
                <span>{session.questionTimeLimit}s per question</span>
              </div>
            )}
            {session.quiz.timeLimit && (
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>{session.quiz.timeLimit}m total</span>
              </div>
            )}
          </div>

          {/* Progress for active sessions */}
          {session.status === 'ACTIVE' && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{session.currentQuestion + 1} / {session.quiz.questionCount}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgress()}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          )}

          {/* Timing Info */}
          <div className="text-xs text-muted-foreground space-y-1">
            {session.scheduledStart && !session.startTime && (
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3" />
                <span>Starts: {format(new Date(session.scheduledStart), 'MMM d, HH:mm')}</span>
              </div>
            )}
            {session.startTime && (
              <div className="flex items-center gap-2">
                <Play className="h-3 w-3" />
                <span>Started: {formatDistanceToNow(new Date(session.startTime), { addSuffix: true })}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Users className="h-3 w-3" />
              <span>Created by {session.creator.name}</span>
            </div>
          </div>

          {/* Participants Preview */}
          {session.participants.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">Recent participants:</div>
              <div className="flex items-center gap-1">
                {session.participants.slice(0, 3).map((participant, index) => (
                  <Avatar key={participant.id} className="h-6 w-6">
                    <AvatarFallback className="text-xs">
                      {participant.userName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {session.participants.length > 3 && (
                  <div className="text-xs text-muted-foreground ml-1">
                    +{session.participants.length - 3} more
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            {session.isParticipating ? (
              <Button
                onClick={() => onView(session.id)}
                className="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
              >
                <Play className="h-4 w-4 mr-2" />
                Continue
              </Button>
            ) : session.canJoin ? (
              <>
                <Button
                  onClick={() => onJoin(session.id)}
                  className="flex-1 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white"
                >
                  <Play className="h-4 w-4 mr-2" />
                  Join Now
                </Button>
                <Button
                  variant="outline"
                  onClick={() => onView(session.id)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                onClick={() => onView(session.id)}
                className="flex-1"
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </Button>
            )}
          </div>

          {/* Features */}
          <div className="flex flex-wrap gap-1 text-xs">
            {session.showLeaderboard && (
              <Badge variant="secondary" className="text-xs">
                <Trophy className="h-3 w-3 mr-1" />
                Leaderboard
              </Badge>
            )}
            {session.allowLateJoin && (
              <Badge variant="secondary" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                Late Join
              </Badge>
            )}
            {session.autoAdvance && (
              <Badge variant="secondary" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                Auto Advance
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
