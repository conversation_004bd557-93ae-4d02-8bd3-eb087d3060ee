'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import {
  Library,
  RefreshCw,
  AlertCircle,
  Play,
  CheckCircle,
  Clock,
  Pause,
  TrendingUp,
  BookOpen,
  Award,
  Star,
  MapIcon
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'

interface EnrolledCourse {
  enrollmentId: string
  enrolledAt: string
  progress: number
  status: 'active' | 'completed' | 'paused'
  lastAccessedAt?: string
  completedAt?: string
  course: {
    id: string
    productId: string
    title: string
    description?: string
    price: number
    originalPrice?: number
    slug: string
    thumbnailImage?: string
    category?: string
    duration?: string
    instructor?: string | {
      id: string
      name: string
      image?: string
    }
    rating?: number
    studentsCount?: number
    features: string[]
    tags: string[]
    hasRoadmap?: boolean
  }
}



export default function MyCoursesPage() {
  const router = useRouter()
  const [enrollments, setEnrollments] = useState<EnrolledCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSyncing, setIsSyncing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('all')
  const [summary, setSummary] = useState({
    totalEnrollments: 0,
    completedCourses: 0,
    activeCourses: 0,
    averageProgress: 0
  })

  const fetchMyCourses = useCallback(async (sync = false) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        status: activeTab === 'all' ? 'all' : activeTab,
        sync: sync.toString(),
        limit: '20'
      })

      const response = await fetch(`/api/courses/my-courses?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch your courses')
      }

      const result = await response.json()
      // Handle APIResponse format: { success: true, data: { enrollments: [...], summary: {...} } }
      const data = result.data || result

      setEnrollments(data.enrollments || [])
      setSummary(data.summary || {
        totalEnrollments: 0,
        completedCourses: 0,
        activeCourses: 0,
        averageProgress: 0
      })

    } catch (error) {
      console.error('Error fetching my courses:', error)
      setError('Failed to load your courses. Please try again.')
      toast.error('Failed to load your courses')
    } finally {
      setIsLoading(false)
      setIsSyncing(false)
    }
  }, [activeTab])

  const handleRefresh = async () => {
    setIsSyncing(true)
    try {
      // Refresh the course list from database
      await fetchMyCourses(true)
      toast.success('Course list refreshed successfully')
    } catch (error) {
      console.error('Refresh error:', error)
      toast.error('Failed to refresh course list')
    } finally {
      setIsSyncing(false)
    }
  }

  const handleContinueLearning = (course: EnrolledCourse) => {
    // Redirect to student course page for enrolled users
    const courseSlug = course.course.slug || course.course.id
    window.location.href = `/student/courses/${courseSlug}`
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />
      default:
        return <Play className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
      case 'paused':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300'
      default:
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
    }
  }

  useEffect(() => {
    fetchMyCourses()
  }, [fetchMyCourses])

  const filteredEnrollments = enrollments.filter(enrollment => {
    if (activeTab === 'all') return true
    return enrollment.status === activeTab
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg">
                  <Library className="h-8 w-8 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent">
                    My Courses
                  </h1>
                  <p className="text-muted-foreground text-lg">
                    Track your learning progress and continue your journey
                  </p>
                </div>
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isSyncing}
              className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              Refresh Courses
            </Button>
          </div>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Courses</p>
                  <p className="text-2xl font-bold">{summary.totalEnrollments}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Award className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold">{summary.completedCourses}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Play className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold">{summary.activeCourses}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Avg Progress</p>
                  <p className="text-2xl font-bold">{summary.averageProgress}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Error Alert */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}

        {/* Course Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
              <TabsTrigger value="all">All Courses</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="paused">Paused</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="space-y-6">
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <CourseCardSkeleton key={index} />
                  ))}
                </div>
              ) : filteredEnrollments.length === 0 ? (
                <EmptyState status={activeTab} />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredEnrollments.map((enrollment, index) => (
                    <motion.div
                      key={enrollment.enrollmentId}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                    >
                      <EnrolledCourseCard
                        enrollment={enrollment}
                        onContinue={() => handleContinueLearning(enrollment)}
                      />
                    </motion.div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  )
}

function EnrolledCourseCard({ 
  enrollment, 
  onContinue 
}: { 
  enrollment: EnrolledCourse
  onContinue: () => void 
}) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />
      default:
        return <Play className="h-4 w-4 text-blue-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
      case 'paused':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300'
      default:
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
    }
  }

  return (
    <Card className="h-full overflow-hidden bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
      {/* Course Image */}
      <div className="relative h-48 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 overflow-hidden">
        {enrollment.course.thumbnailImage ? (
          <img
            src={enrollment.course.thumbnailImage}
            alt={enrollment.course.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <BookOpen className="h-16 w-16 text-white/80" />
          </div>
        )}
        
        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300" />
        
        {/* Status Badge */}
        <div className="absolute top-4 left-4">
          <Badge className={cn("capitalize", getStatusColor(enrollment.status))}>
            {getStatusIcon(enrollment.status)}
            <span className="ml-1">{enrollment.status}</span>
          </Badge>
        </div>

        {/* Progress Badge */}
        <div className="absolute top-4 right-4">
          <Badge className="bg-white/90 text-gray-900">
            {enrollment.progress}% Complete
          </Badge>
        </div>
      </div>

      <CardContent className="p-6 space-y-4">
        <div>
          <h3 className="text-lg font-bold text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {enrollment.course.title}
          </h3>

          {enrollment.course.instructor && (
            <p className="text-sm text-blue-600 dark:text-blue-400 font-medium mt-1">
              by {typeof enrollment.course.instructor === 'string'
                ? enrollment.course.instructor
                : enrollment.course.instructor.name}
            </p>
          )}

          {enrollment.course.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-2">
              {enrollment.course.description}
            </p>
          )}
        </div>

        {/* Course Details */}
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          {enrollment.course.category && (
            <Badge variant="secondary" className="text-xs">
              {enrollment.course.category}
            </Badge>
          )}
          {enrollment.course.rating && (
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span>{enrollment.course.rating}</span>
            </div>
          )}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{enrollment.progress}%</span>
          </div>
          <Progress value={enrollment.progress} className="h-2" />
        </div>

        {/* Course Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>
                Enrolled {new Date(enrollment.enrolledAt).toLocaleDateString()}
              </span>
            </div>
            {enrollment.course.duration && (
              <span>{enrollment.course.duration}</span>
            )}
          </div>

          {enrollment.lastAccessedAt && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Last accessed: {new Date(enrollment.lastAccessedAt).toLocaleDateString()}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white"
            onClick={onContinue}
          >
            {enrollment.status === 'completed' ? (
              <>
                <Award className="h-4 w-4 mr-2" />
                Review Course
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Continue Learning
              </>
            )}
          </Button>

          {/* Roadmap Button - Show if course has roadmap */}
          {enrollment.course.hasRoadmap && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => window.location.href = `/student/courses/${enrollment.course.slug}/roadmap`}
            >
              <MapIcon className="h-4 w-4 mr-2" />
              Learning Roadmap
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function CourseCardSkeleton() {
  return (
    <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl border shadow-lg overflow-hidden">
      <Skeleton className="h-48 w-full" />
      <div className="p-6 space-y-4">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-2 w-full" />
        <Skeleton className="h-4 w-2/3" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
}

function EmptyState({ status }: { status: string }) {
  const getEmptyMessage = () => {
    switch (status) {
      case 'completed':
        return "You haven't completed any courses yet. Keep learning!"
      case 'paused':
        return "No paused courses. All your courses are active!"
      case 'active':
        return "No active courses. Browse our catalog to get started!"
      default:
        return "You haven't enrolled in any courses yet. Start your learning journey today!"
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col items-center justify-center py-16 text-center"
    >
      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-full flex items-center justify-center mb-6">
        <Library className="h-12 w-12 text-purple-500" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No courses found
      </h3>
      <p className="text-gray-600 dark:text-gray-400 max-w-md mb-6">
        {getEmptyMessage()}
      </p>
      <Button
        onClick={() => window.location.href = '/student/courses'}
        className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white"
      >
        Browse Courses
      </Button>
    </motion.div>
  )
}
