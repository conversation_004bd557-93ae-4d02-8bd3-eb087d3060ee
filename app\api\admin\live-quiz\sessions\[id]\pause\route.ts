import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const pauseResumeSchema = z.object({
  action: z.enum(['pause', 'resume'])
})

// POST /api/admin/live-quiz/sessions/[id]/pause - Pause or resume live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: pauseResumeSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string
    const { action } = validatedBody

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get current session
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              title: true
            }
          },
          participants: {
            where: { isActive: true },
            include: {
              user: {
                select: { id: true, name: true }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Validate action based on current status
      if (action === 'pause') {
        if (session.status !== 'ACTIVE') {
          return APIResponse.error('Can only pause active sessions', 400, 'SESSION_NOT_ACTIVE')
        }
      } else if (action === 'resume') {
        if (session.status !== 'PAUSED') {
          return APIResponse.error('Can only resume paused sessions', 400, 'SESSION_NOT_PAUSED')
        }
      }

      // Update session status
      const newStatus = action === 'pause' ? 'PAUSED' : 'ACTIVE'
      const updatedSession = await prisma.liveQuizSession.update({
        where: { id: sessionId },
        data: {
          status: newStatus
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              questions: {
                select: { id: true }
              }
            }
          },
          participants: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      })

      // Broadcast status change to participants
      try {
        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()

        const eventName = action === 'pause' ? 'live-quiz:session-paused' : 'live-quiz:session-resumed'

        if (socketManager) {
          const payload = {
            sessionId,
            session: {
              id: updatedSession.id,
              title: updatedSession.title,
              status: updatedSession.status,
              currentQuestion: updatedSession.currentQuestion
            }
          }
          socketManager.broadcastToRoom(`live-quiz:${sessionId}`, eventName, payload)
          socketManager.broadcastToAll(eventName, payload)
        }

        // Send notifications to active participants
        if (socketManager) {
          const notificationTitle = action === 'pause' ? 'Quiz Paused' : 'Quiz Resumed'
          const notificationMessage = action === 'pause'
            ? `${updatedSession.title} has been paused by the instructor.`
            : `${updatedSession.title} has been resumed. Continue answering!`

          for (const participant of session.participants) {
            socketManager.sendNotificationToUser(participant.userId, {
              type: 'info',
              title: notificationTitle,
              message: notificationMessage,
              data: {
                sessionId,
                action: `session-${action}d`
              }
            })
          }
        }
      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        session: {
          ...updatedSession,
          participantCount: updatedSession.participants.length,
          questionCount: updatedSession.quiz.questions.length
        }
      }, `Live quiz session ${action}d successfully`)

    } catch (error) {
      console.error(`Error ${action}ing live quiz session:`, error)
      return APIResponse.error(`Failed to ${action} live quiz session`, 500)
    }
  }
)
