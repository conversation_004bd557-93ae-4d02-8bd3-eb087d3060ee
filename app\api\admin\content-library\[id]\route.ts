import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateFileSchema = z.object({
  originalName: z.string().optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  folder: z.string().optional()
})

// GET /api/admin/content-library/[id] - Get single file with usage details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const fileId = resolvedParams?.id as string

      if (!fileId) {
        return APIResponse.error('File ID is required', 400)
      }

      const file = await prisma.contentLibraryFile.findUnique({
        where: { id: fileId },
        include: {
          usages: {
            include: {
              course: {
                select: {
                  id: true,
                  title: true,
                  thumbnailImage: true
                }
              },
              lesson: {
                select: {
                  id: true,
                  title: true,
                  chapter: {
                    select: {
                      title: true,
                      section: {
                        select: {
                          title: true,
                          course: {
                            select: {
                              id: true,
                              title: true
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          uploadedByUser: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      if (!file) {
        return APIResponse.error('File not found', 404)
      }

      return APIResponse.success({
        file: {
          ...file,
          usageCount: file.usages.length
        }
      })
    } catch (error) {
      console.error('Error fetching file:', error)
      return APIResponse.error('Failed to fetch file', 500)
    }
  }
)

// PUT /api/admin/content-library/[id] - Update file metadata
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateFileSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const fileId = resolvedParams?.id as string

      if (!fileId) {
        return APIResponse.error('File ID is required', 400)
      }

      const file = await prisma.contentLibraryFile.update({
        where: { id: fileId },
        data: {
          ...validatedBody,
          updatedAt: new Date()
        },
        include: {
          _count: {
            select: {
              usages: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'File updated successfully',
        file: {
          ...file,
          usageCount: file._count.usages
        }
      })
    } catch (error) {
      console.error('Error updating file:', error)
      return APIResponse.error('Failed to update file', 500)
    }
  }
)

// DELETE /api/admin/content-library/[id] - Delete file
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const fileId = resolvedParams?.id as string

      if (!fileId) {
        return APIResponse.error('File ID is required', 400)
      }

      // Check if file is in use
      const file = await prisma.contentLibraryFile.findUnique({
        where: { id: fileId },
        include: {
          _count: {
            select: {
              usages: true
            }
          }
        }
      })

      if (!file) {
        return APIResponse.error('File not found', 404)
      }

      // Prevent deletion if file is in use (optional - you might want to allow force deletion)
      const forceDelete = new URL(request.url).searchParams.get('force') === 'true'
      
      if (file._count.usages > 0 && !forceDelete) {
        return APIResponse.error(
          `Cannot delete file: it is currently used in ${file._count.usages} location(s). Use force=true to delete anyway.`,
          400
        )
      }

      // Delete from Bunny CDN
      const bunnyStorage = getBunnyStorage()
      try {
        await bunnyStorage.deleteFile(file.filename)
      } catch (cdnError) {
        console.warn('Failed to delete file from CDN:', cdnError)
        // Continue with database deletion even if CDN deletion fails
      }

      // Delete from database
      await prisma.contentLibraryFile.delete({
        where: { id: fileId }
      })

      return APIResponse.success({
        message: 'File deleted successfully'
      })
    } catch (error) {
      console.error('Error deleting file:', error)
      return APIResponse.error('Failed to delete file', 500)
    }
  }
)
