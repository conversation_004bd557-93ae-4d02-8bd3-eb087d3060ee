import { notificationService, NotificationTemplates } from '@/lib/notification-service'
import { emailService } from '@/lib/email-service'
import { EmailTemplates } from '@/lib/email-templates'
import { prisma } from '@/lib/prisma'

/**
 * Notification event handlers for automatic notifications
 */
export class NotificationEvents {
  /**
   * Send welcome notification to new user
   */
  static async onUserRegistered(userId: string, userName: string, userRole: string = 'STUDENT') {
    try {
      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.WELCOME(userName)
      )

      // Send welcome email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { email: true }
      })

      if (user?.email) {
        const emailTemplate = EmailTemplates.welcome(userName, userRole)
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending welcome notification:', error)
    }
  }

  /**
   * Notify student when they enroll in a course
   */
  static async onCourseEnrolled(userId: string, courseTitle: string, courseId: string) {
    try {
      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.COURSE_ENROLLED(courseTitle, courseId)
      )

      // Send enrollment email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true }
      })

      if (user?.email) {
        const emailTemplate = EmailTemplates.courseEnrolled(user.name || 'Student', courseTitle, courseId)
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending course enrollment notification:', error)
    }
  }

  /**
   * Notify student when they purchase a quiz bundle
   */
  static async onBundlePurchased(userId: string, bundleTitle: string, bundleId: string) {
    try {
      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.BUNDLE_PURCHASED(bundleTitle, bundleId)
      )

      // Send purchase confirmation email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true }
      })

      if (user?.email) {
        const emailTemplate = EmailTemplates.bundlePurchased(user.name || 'Student', bundleTitle, bundleId)
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending bundle purchase notification:', error)
    }
  }

  /**
   * Notify student when they enroll in a quiz
   */
  static async onQuizEnrolled(userId: string, quizTitle: string, quizId: string) {
    try {
      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.QUIZ_ENROLLED(quizTitle, quizId)
      )

      // Send enrollment email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true }
      })

      if (user?.email) {
        const emailTemplate = EmailTemplates.quizEnrolled(user.name || 'Student', quizTitle, quizId)
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending quiz enrollment notification:', error)
    }
  }

  /**
   * Notify student when they complete a quiz
   */
  static async onQuizCompleted(userId: string, quizTitle: string, quizId: string, percentage: number) {
    try {
      // Get quiz attempt details
      const attempt = await prisma.quizAttempt.findFirst({
        where: {
          userId,
          quizId,
          completedAt: { not: null }
        },
        orderBy: { completedAt: 'desc' },
        select: { score: true }
      })

      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.QUIZ_COMPLETED(quizTitle, percentage, quizId)
      )

      // Send completion email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true }
      })

      if (user?.email && attempt) {
        const emailTemplate = EmailTemplates.quizCompleted(
          user.name || 'Student',
          quizTitle,
          attempt.score || 0,
          percentage
        )
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending quiz completion notification:', error)
    }
  }

  /**
   * Notify student when they unlock an achievement
   */
  static async onAchievementUnlocked(userId: string, achievementTitle: string, achievementDescription: string) {
    try {
      // Send in-app notification
      await notificationService.sendToUsers(
        [{ userId }],
        NotificationTemplates.ACHIEVEMENT_UNLOCKED(achievementTitle, achievementDescription)
      )

      // Send achievement email
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, email: true }
      })

      if (user?.email) {
        const emailTemplate = EmailTemplates.achievementUnlocked(
          user.name || 'Student',
          achievementTitle,
          achievementDescription
        )
        await emailService.sendEmail({
          to: user.email,
          subject: emailTemplate.subject,
          html: emailTemplate.html,
          text: emailTemplate.text
        })
      }
    } catch (error) {
      console.error('Error sending achievement notification:', error)
    }
  }

  /**
   * Notify students when a new quiz is published
   */
  static async onQuizPublished(quizId: string, quizTitle: string) {
    try {
      // Get all students
      const students = await prisma.user.findMany({
        where: { role: 'STUDENT' },
        select: { id: true }
      })

      if (students.length > 0) {
        await notificationService.sendToUsers(
          students.map(student => ({ userId: student.id })),
          NotificationTemplates.QUIZ_AVAILABLE(quizTitle, quizId)
        )
      }
    } catch (error) {
      console.error('Error sending quiz published notification:', error)
    }
  }

  /**
   * Notify student when quiz results are available
   */
  static async onQuizResultPublished(attemptId: string) {
    try {
      const attempt = await prisma.quizAttempt.findUnique({
        where: { id: attemptId },
        include: {
          quiz: { select: { title: true } },
          user: { select: { id: true } }
        }
      })

      if (attempt && attempt.isCompleted) {
        await notificationService.sendToUsers(
          [{ userId: attempt.user.id }],
          NotificationTemplates.RESULT_PUBLISHED(
            attempt.quiz.title,
            attempt.percentage,
            attemptId
          )
        )
      }
    } catch (error) {
      console.error('Error sending result published notification:', error)
    }
  }

  /**
   * Send quiz reminder notifications
   */
  static async sendQuizReminders() {
    try {
      // Find quizzes ending in the next 24 hours
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)

      const endingSoonQuizzes = await prisma.quiz.findMany({
        where: {
          isPublished: true,
          endTime: {
            gte: new Date(),
            lte: tomorrow
          }
        },
        include: {
          enrollments: {
            include: {
              user: { select: { id: true } }
            }
          }
        }
      })

      for (const quiz of endingSoonQuizzes) {
        const timeLeft = this.getTimeUntil(quiz.endTime!)
        
        // Send reminder to enrolled students who haven't completed
        // Note: We'll send to all enrolled users and let them decide if they've completed
        const enrolledUsers = quiz.enrollments
          .map(enrollment => ({ userId: enrollment.user.id }))

        if (enrolledUsers.length > 0) {
          await notificationService.sendToUsers(
            enrolledUsers,
            NotificationTemplates.QUIZ_REMINDER(quiz.title, quiz.id, timeLeft)
          )
        }
      }
    } catch (error) {
      console.error('Error sending quiz reminders:', error)
    }
  }



  /**
   * Send system maintenance notifications
   */
  static async sendMaintenanceNotification(
    title: string,
    message: string,
    scheduledAt?: Date
  ) {
    try {
      await notificationService.sendToAll({
        type: 'MAINTENANCE',
        title,
        message,
        priority: 'high',
        category: 'system',
        scheduledAt
      })
    } catch (error) {
      console.error('Error sending maintenance notification:', error)
    }
  }

  /**
   * Send system update notifications
   */
  static async sendSystemUpdateNotification(
    title: string,
    message: string,
    actionUrl?: string
  ) {
    try {
      await notificationService.sendToAll({
        type: 'SYSTEM_UPDATE',
        title,
        message,
        actionUrl,
        priority: 'normal',
        category: 'system'
      })
    } catch (error) {
      console.error('Error sending system update notification:', error)
    }
  }

  /**
   * Notify when user achieves a streak milestone
   */
  static async onStreakMilestone(userId: string, streakCount: number) {
    try {
      await notificationService.sendToUsers(
        [{ userId }],
        {
          type: 'STREAK_MILESTONE',
          title: `🔥 ${streakCount} Day Streak!`,
          message: `Amazing! You've maintained a ${streakCount}-day learning streak. Keep it up!`,
          actionUrl: '/student/achievements',
          priority: 'high',
          category: 'achievement'
        }
      )
    } catch (error) {
      console.error('Error sending streak milestone notification:', error)
    }
  }

  /**
   * Notify when user's rank changes significantly
   */
  static async onRankChanged(userId: string, newRank: number, previousRank: number) {
    try {
      const isImprovement = newRank < previousRank
      const rankDifference = Math.abs(newRank - previousRank)

      // Only notify for significant rank changes (5+ positions)
      if (rankDifference >= 5) {
        await notificationService.sendToUsers(
          [{ userId }],
          {
            type: 'RANK_CHANGED',
            title: isImprovement ? '📈 Rank Improved!' : '📉 Rank Update',
            message: isImprovement 
              ? `Congratulations! You've moved up ${rankDifference} positions to rank #${newRank}!`
              : `Your rank has changed to #${newRank}. Keep practicing to improve!`,
            actionUrl: '/student/leaderboard',
            priority: isImprovement ? 'high' : 'normal',
            category: 'achievement'
          }
        )
      }
    } catch (error) {
      console.error('Error sending rank change notification:', error)
    }
  }

  /**
   * Send deadline approaching notifications
   */
  static async sendDeadlineReminders() {
    try {
      // Find quizzes with deadlines in the next 3 days
      const threeDaysFromNow = new Date()
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3)

      const upcomingDeadlines = await prisma.quiz.findMany({
        where: {
          isPublished: true,
          endTime: {
            gte: new Date(),
            lte: threeDaysFromNow
          }
        },
        include: {
          enrollments: {
            include: {
              user: { select: { id: true } }
            }
          }
        }
      })

      for (const quiz of upcomingDeadlines) {
        const daysLeft = Math.ceil(
          (quiz.endTime!.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        )

        const enrolledUsers = quiz.enrollments.map(enrollment => ({ 
          userId: enrollment.user.id 
        }))

        if (enrolledUsers.length > 0) {
          await notificationService.sendToUsers(
            enrolledUsers,
            {
              type: 'DEADLINE_APPROACHING',
              title: '⏰ Deadline Approaching',
              message: `"${quiz.title}" deadline is in ${daysLeft} day${daysLeft !== 1 ? 's' : ''}. Don't miss out!`,
              actionUrl: `/student/quiz/${quiz.id}`,
              priority: daysLeft <= 1 ? 'urgent' : 'high',
              category: 'reminder'
            }
          )
        }
      }
    } catch (error) {
      console.error('Error sending deadline reminders:', error)
    }
  }

  /**
   * Request feedback from users after quiz completion
   */
  static async requestFeedback(userId: string, quizTitle: string, quizId: string) {
    try {
      await notificationService.sendToUsers(
        [{ userId }],
        {
          type: 'FEEDBACK_REQUEST',
          title: 'How was your experience?',
          message: `We'd love to hear your feedback on "${quizTitle}". Help us improve!`,
          actionUrl: `/student/quiz/${quizId}/feedback`,
          priority: 'low',
          category: 'feedback',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Expires in 7 days
        }
      )
    } catch (error) {
      console.error('Error sending feedback request:', error)
    }
  }

  /**
   * Helper function to calculate time until a date
   */
  private static getTimeUntil(date: Date): string {
    const now = new Date()
    const diff = date.getTime() - now.getTime()
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) return `${days} day${days !== 1 ? 's' : ''}`
    if (hours > 0) return `${hours} hour${hours !== 1 ? 's' : ''}`
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`
  }
}

// Schedule automatic notifications
export function scheduleNotificationJobs() {
  // Send quiz reminders every hour
  setInterval(() => {
    NotificationEvents.sendQuizReminders()
  }, 60 * 60 * 1000) // 1 hour

  // Send deadline reminders twice daily
  setInterval(() => {
    NotificationEvents.sendDeadlineReminders()
  }, 12 * 60 * 60 * 1000) // 12 hours

  // Clean up expired notifications daily
  setInterval(() => {
    notificationService.cleanupExpiredNotifications()
  }, 24 * 60 * 60 * 1000) // 24 hours
}
