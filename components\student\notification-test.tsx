"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  XCircle,
  Wifi,
  WifiOff,
  TestTube
} from "lucide-react"
import { getSocketClient } from "@/lib/socket-client"
import { toast } from "@/lib/toast-utils"

export function NotificationTest() {
  const { data: session } = useSession()
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting')
  const [receivedNotifications, setReceivedNotifications] = useState<any[]>([])

  useEffect(() => {
    if (!session?.user) return

    // Get existing socket client (already initialized in layout)
    const socketClient = getSocketClient()

    // Listen for connection events
    socketClient.on('connection:established', () => {
      setConnectionStatus('connected')
           })

    socketClient.on('connection:lost', () => {
      setConnectionStatus('disconnected')
           })

    socketClient.on('authenticated', (data: any) => {
             setConnectionStatus('connected')
    })

    // Listen for notifications
    socketClient.on('notification:received', (notification: any) => {
             setReceivedNotifications(prev => [notification, ...prev])
      
      // Show toast notification
      switch (notification.type) {
        case 'success':
          toast.success(notification.title, { description: notification.message })
          break
        case 'error':
          toast.error(notification.title, { description: notification.message })
          break
        case 'warning':
          toast.warning(notification.title, { description: notification.message })
          break
        default:
          toast.info(notification.title, { description: notification.message })
      }
    })

    return () => {
      // Cleanup listeners
      socketClient.off('connection:established')
      socketClient.off('connection:lost')
      socketClient.off('authenticated')
      socketClient.off('notification:received')
    }
  }, [session])

  const sendTestNotification = () => {
    // This would normally be done by an admin, but for testing purposes
    // we can simulate receiving a notification
    const testNotification = {
      id: `test_${Date.now()}`,
      type: 'info',
      title: 'Test Notification',
      message: 'This is a test notification for the student!',
      createdAt: new Date().toISOString(),
      senderId: 'system',
      senderName: 'System'
    }

    setReceivedNotifications(prev => [testNotification, ...prev])
    toast.info(testNotification.title, { description: testNotification.message })
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default: return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Real-time Notifications Test
            </span>
            <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
              {connectionStatus === 'connected' ? (
                <>
                  <Wifi className="h-4 w-4 mr-1" />
                  Connected
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 mr-1" />
                  {connectionStatus}
                </>
              )}
            </Badge>
          </CardTitle>
          <CardDescription>
            Test real-time notifications for students. Connection status and received notifications will appear here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button 
              onClick={sendTestNotification}
              variant="outline"
              size="sm"
            >
              <TestTube className="h-4 w-4 mr-2" />
              Send Test Notification
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Received Notifications */}
      {receivedNotifications.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Received Notifications ({receivedNotifications.length})</CardTitle>
            <CardDescription>
              Real-time notifications received by this student
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {receivedNotifications.map((notification, index) => (
                <div key={notification.id || index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                  {getNotificationIcon(notification.type)}
                  <div className="flex-1">
                    <div className="font-medium">{notification.title}</div>
                    <div className="text-sm text-muted-foreground">{notification.message}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {new Date(notification.createdAt).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium">1</div>
            <div>Make sure the socket server is running (npm run dev:full)</div>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium">2</div>
            <div>Check that the connection status shows &quot;Connected&quot;</div>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium">3</div>
            <div>Go to /admin/realtime and send a notification to test real-time delivery</div>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-xs font-medium">4</div>
            <div>Or click &quot;Send Test Notification&quot; above to simulate receiving one</div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
