'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { toast } from 'react-hot-toast'
import ContentModal from '../content-modal'

interface SectionFormData {
  title: string
  description: string
  isPublished: boolean
}

interface SectionModalProps {
  isOpen: boolean
  onClose: () => void
  courseId: string
  onSuccess: () => void
  editingSection?: {
    id: string
    title: string
    description: string
    isPublished: boolean
  }
}

export default function SectionModal({
  isOpen,
  onClose,
  courseId,
  onSuccess,
  editingSection
}: SectionModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<SectionFormData>({
    title: editingSection?.title || '',
    description: editingSection?.description || '',
    isPublished: editingSection?.isPublished || false
  })

  // Update form data when editingSection changes
  useEffect(() => {
    if (editingSection) {
      setFormData({
        title: editingSection.title || '',
        description: editingSection.description || '',
        isPublished: editingSection.isPublished || false
      })
    } else {
      setFormData({
        title: '',
        description: '',
        isPublished: false
      })
    }
  }, [editingSection])

  const handleInputChange = (field: keyof SectionFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Section title is required')
      return
    }

    try {
      setLoading(true)
      
      const url = editingSection 
        ? `/api/admin/courses/${courseId}/sections/${editingSection.id}`
        : `/api/admin/courses/${courseId}/sections`
      
      const method = editingSection ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || `Failed to ${editingSection ? 'update' : 'create'} section`)
      }

      const result = await response.json()
      const data = result.data || result
      
      toast.success(data.message || `Section ${editingSection ? 'updated' : 'created'} successfully!`)
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        isPublished: false
      })
      
      onSuccess()
      onClose()

    } catch (error: any) {
      console.error(`Error ${editingSection ? 'updating' : 'creating'} section:`, error)
      toast.error(error.message || `Failed to ${editingSection ? 'update' : 'create'} section`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!editingSection) {
      setFormData({
        title: '',
        description: '',
        isPublished: false
      })
    }
    onClose()
  }

  return (
    <ContentModal
      isOpen={isOpen}
      onClose={handleClose}
      title={editingSection ? 'Edit Section' : 'Add New Section'}
      description={editingSection ? 'Update section information and settings' : 'Create a new section to organize your course content'}
      size="md"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Section Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Section Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
            placeholder="Enter section title"
            required
          />
        </div>

        {/* Section Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className="w-full px-4 py-3 bg-white/50 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
            placeholder="Describe what this section covers"
          />
        </div>



        {/* Published Status */}
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="isPublished"
            checked={formData.isPublished}
            onChange={(e) => handleInputChange('isPublished', e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          />
          <label htmlFor="isPublished" className="text-sm font-medium text-gray-700">
            {editingSection ? 'Section is published' : 'Publish section immediately'}
          </label>
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <motion.button
            type="button"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleClose}
            className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors duration-200"
          >
            Cancel
          </motion.button>
          <motion.button
            type="submit"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            disabled={loading}
            className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
          >
            {loading ? (editingSection ? 'Updating...' : 'Creating...') : (editingSection ? 'Update Section' : 'Create Section')}
          </motion.button>
        </div>
      </form>
    </ContentModal>
  )
}
