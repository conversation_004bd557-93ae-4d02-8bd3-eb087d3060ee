import { NextRequest, NextResponse } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON> } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateRoleSchema = z.object({
  role: z.enum(['STUDENT', 'ADMIN'])
})

export const POST = createAPIHandler({ requireAuth: true }, async (request: NextRequest, { session }) => {
  try {
    const body = await request.json()
    const { role } = updateRoleSchema.parse(body)

    const isRequesterAdmin = session!.user.role === 'ADMIN'
    const isAllowlistedEmail = Boolean(
      session!.user?.email &&
      (process.env.ADMIN_EMAIL_ALLOWLIST || '')
        .split(',')
        .map((e) => e.trim().toLowerCase())
        .filter(Boolean)
        .includes(session!.user.email!.toLowerCase())
    )

    if (role === 'ADMIN' && !(isRequesterAdmin || isAllowlistedEmail)) {
      return NextResponse.json(
        { error: 'Forbidden: cannot escalate privileges' },
        { status: 403 }
      )
    }

    const updated = await prisma.user.update({
      where: { id: session!.user.id },
      data: { role }
    })

    return NextResponse.json({ success: true, role: updated.role })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
})
