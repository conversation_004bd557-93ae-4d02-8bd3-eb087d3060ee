import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { getBunnyStorage } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const bulkOperationSchema = z.object({
  operation: z.enum(['delete', 'move', 'tag', 'untag']),
  fileIds: z.array(z.string()).min(1, 'At least one file ID is required'),
  data: z.object({
    folder: z.string().optional(),
    tags: z.array(z.string()).optional(),
    force: z.boolean().optional()
  }).optional()
})

// POST /api/admin/content-library/bulk - Perform bulk operations
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: bulkOperationSchema
  },
  async (request: NextRequest, { validatedBody }) => {
    try {
      const { operation, fileIds, data = {} } = validatedBody
      const results = {
        success: 0,
        failed: 0,
        errors: [] as string[]
      }

      switch (operation) {
        case 'delete':
          await handleBulkDelete(fileIds, data.force || false, results)
          break
        
        case 'move':
          if (!data.folder) {
            return APIResponse.error('Folder is required for move operation', 400)
          }
          await handleBulkMove(fileIds, data.folder, results)
          break
        
        case 'tag':
          if (!data.tags || data.tags.length === 0) {
            return APIResponse.error('Tags are required for tag operation', 400)
          }
          await handleBulkTag(fileIds, data.tags, results)
          break
        
        case 'untag':
          if (!data.tags || data.tags.length === 0) {
            return APIResponse.error('Tags are required for untag operation', 400)
          }
          await handleBulkUntag(fileIds, data.tags, results)
          break
        
        default:
          return APIResponse.error('Invalid operation', 400)
      }

      return APIResponse.success({
        message: `Bulk ${operation} completed`,
        results
      })
    } catch (error) {
      console.error('Error performing bulk operation:', error)
      return APIResponse.error('Failed to perform bulk operation', 500)
    }
  }
)

async function handleBulkDelete(
  fileIds: string[], 
  force: boolean, 
  results: { success: number; failed: number; errors: string[] }
) {
  const bunnyStorage = getBunnyStorage()

  for (const fileId of fileIds) {
    try {
      // Check if file exists and get usage count
      const file = await prisma.contentLibraryFile.findUnique({
        where: { id: fileId },
        include: {
          _count: {
            select: { usages: true }
          }
        }
      })

      if (!file) {
        results.failed++
        results.errors.push(`File ${fileId} not found`)
        continue
      }

      // Check if file is in use
      if (file._count.usages > 0 && !force) {
        results.failed++
        results.errors.push(`File ${file.originalName} is in use (${file._count.usages} locations)`)
        continue
      }

      // Delete from CDN
      try {
        await bunnyStorage.deleteFile(file.filename)
      } catch (cdnError) {
        console.warn(`Failed to delete ${file.filename} from CDN:`, cdnError)
      }

      // Delete from database
      await prisma.contentLibraryFile.delete({
        where: { id: fileId }
      })

      results.success++
    } catch (error) {
      results.failed++
      results.errors.push(`Failed to delete file ${fileId}: ${error}`)
    }
  }
}

async function handleBulkMove(
  fileIds: string[], 
  folder: string, 
  results: { success: number; failed: number; errors: string[] }
) {
  for (const fileId of fileIds) {
    try {
      await prisma.contentLibraryFile.update({
        where: { id: fileId },
        data: { 
          folder,
          updatedAt: new Date()
        }
      })
      results.success++
    } catch (error) {
      results.failed++
      results.errors.push(`Failed to move file ${fileId}: ${error}`)
    }
  }
}

async function handleBulkTag(
  fileIds: string[], 
  tags: string[], 
  results: { success: number; failed: number; errors: string[] }
) {
  for (const fileId of fileIds) {
    try {
      // Get current tags
      const file = await prisma.contentLibraryFile.findUnique({
        where: { id: fileId },
        select: { tags: true }
      })

      if (!file) {
        results.failed++
        results.errors.push(`File ${fileId} not found`)
        continue
      }

      // Merge with new tags (avoid duplicates)
      const currentTags = file.tags || []
      const newTags = [...new Set([...currentTags, ...tags])]

      await prisma.contentLibraryFile.update({
        where: { id: fileId },
        data: { 
          tags: newTags,
          updatedAt: new Date()
        }
      })
      results.success++
    } catch (error) {
      results.failed++
      results.errors.push(`Failed to tag file ${fileId}: ${error}`)
    }
  }
}

async function handleBulkUntag(
  fileIds: string[], 
  tags: string[], 
  results: { success: number; failed: number; errors: string[] }
) {
  for (const fileId of fileIds) {
    try {
      // Get current tags
      const file = await prisma.contentLibraryFile.findUnique({
        where: { id: fileId },
        select: { tags: true }
      })

      if (!file) {
        results.failed++
        results.errors.push(`File ${fileId} not found`)
        continue
      }

      // Remove specified tags
      const currentTags = file.tags || []
      const newTags = currentTags.filter(tag => !tags.includes(tag))

      await prisma.contentLibraryFile.update({
        where: { id: fileId },
        data: { 
          tags: newTags,
          updatedAt: new Date()
        }
      })
      results.success++
    } catch (error) {
      results.failed++
      results.errors.push(`Failed to untag file ${fileId}: ${error}`)
    }
  }
}
