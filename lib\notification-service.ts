import { prisma } from '@/lib/prisma'
import { getSocketManager } from '@/lib/socket-server'
import { NotificationType } from '@/lib/generated/prisma'


export interface NotificationData {
  type: NotificationType
  title: string
  message: string
  data?: any
  actionUrl?: string
  imageUrl?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
  category?: string
  expiresAt?: Date
  scheduledAt?: Date
}

export interface NotificationRecipient {
  userId: string
  customData?: any // User-specific data for the notification
}

export class NotificationService {
  private static instance: NotificationService
  private socketServer: any

  private constructor() {
    this.socketServer = getSocketManager()
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance
  }

  /**
   * Send notification to specific users
   */
  async sendToUsers(
    recipients: NotificationRecipient[],
    notificationData: NotificationData
  ): Promise<string> {
    try {
      // Create the notification record
      const notification = await prisma.notification.create({
        data: {
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          data: notificationData.data || {},
          actionUrl: notificationData.actionUrl,
          imageUrl: notificationData.imageUrl,
          priority: notificationData.priority || 'normal',
          category: notificationData.category,
          expiresAt: notificationData.expiresAt,
          scheduledAt: notificationData.scheduledAt,
          sentAt: new Date()
        }
      })

      // Create user notification records
      const userNotifications = await Promise.all(
        recipients.map(recipient =>
          prisma.userNotification.create({
            data: {
              userId: recipient.userId,
              notificationId: notification.id,
              deliveredAt: new Date()
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true
                }
              },
              notification: true
            }
          })
        )
      )

      // Send real-time notifications via WebSocket
      if (this.socketServer) {
        for (const userNotification of userNotifications) {
          this.socketServer.sendNotificationToUser(
            userNotification.userId,
            {
              id: userNotification.id,
              type: notification.type,
              title: notification.title,
              message: notification.message,
              data: notification.data,
              actionUrl: notification.actionUrl,
              imageUrl: notification.imageUrl,
              priority: notification.priority,
              category: notification.category,
              createdAt: notification.createdAt,
              isRead: userNotification.isRead
            }
          )
        }
      }

      return notification.id
    } catch (error) {
      console.error('Error sending notification:', error)
      throw new Error('Failed to send notification')
    }
  }

  /**
   * Send notification to all users with a specific role
   */
  async sendToRole(
    role: 'ADMIN' | 'STUDENT',
    notificationData: NotificationData
  ): Promise<string> {
    try {
      // Get all users with the specified role
      const users = await prisma.user.findMany({
        where: { role },
        select: { id: true }
      })

      const recipients = users.map(user => ({ userId: user.id }))
      return await this.sendToUsers(recipients, notificationData)
    } catch (error) {
      console.error('Error sending notification to role:', error)
      throw new Error('Failed to send notification to role')
    }
  }

  /**
   * Send notification to all users
   */
  async sendToAll(notificationData: NotificationData): Promise<string> {
    try {
      // Get all users
      const users = await prisma.user.findMany({
        select: { id: true }
      })

      const recipients = users.map(user => ({ userId: user.id }))
      return await this.sendToUsers(recipients, notificationData)
    } catch (error) {
      console.error('Error sending notification to all users:', error)
      throw new Error('Failed to send notification to all users')
    }
  }

  /**
   * Schedule a notification for later delivery
   */
  async scheduleNotification(
    recipients: NotificationRecipient[],
    notificationData: NotificationData,
    scheduledAt: Date
  ): Promise<string> {
    return await this.sendToUsers(recipients, {
      ...notificationData,
      scheduledAt
    })
  }

  /**
   * Mark notification as read
   */
  async markAsRead(userNotificationId: string, userId: string): Promise<void> {
    try {
      await prisma.userNotification.updateMany({
        where: {
          id: userNotificationId,
          userId: userId
        },
        data: {
          isRead: true,
          readAt: new Date()
        }
      })

      // Emit real-time update
      if (this.socketServer) {
        this.socketServer.sendNotificationUpdate(userId, {
          id: userNotificationId,
          isRead: true,
          readAt: new Date()
        })
      }
    } catch (error) {
      console.error('Error marking notification as read:', error)
      throw new Error('Failed to mark notification as read')
    }
  }

  /**
   * Mark notification as clicked
   */
  async markAsClicked(userNotificationId: string, userId: string): Promise<void> {
    try {
      await prisma.userNotification.updateMany({
        where: {
          id: userNotificationId,
          userId: userId
        },
        data: {
          isClicked: true,
          clickedAt: new Date(),
          isRead: true,
          readAt: new Date()
        }
      })

      // Emit real-time update
      if (this.socketServer) {
        this.socketServer.sendNotificationUpdate(userId, {
          id: userNotificationId,
          isClicked: true,
          clickedAt: new Date(),
          isRead: true,
          readAt: new Date()
        })
      }
    } catch (error) {
      console.error('Error marking notification as clicked:', error)
      throw new Error('Failed to mark notification as clicked')
    }
  }

  /**
   * Dismiss notification
   */
  async dismissNotification(userNotificationId: string, userId: string): Promise<void> {
    try {
      await prisma.userNotification.updateMany({
        where: {
          id: userNotificationId,
          userId: userId
        },
        data: {
          isDismissed: true,
          dismissedAt: new Date()
        }
      })

      // Emit real-time update
      if (this.socketServer) {
        this.socketServer.sendNotificationUpdate(userId, {
          id: userNotificationId,
          isDismissed: true,
          dismissedAt: new Date()
        })
      }
    } catch (error) {
      console.error('Error dismissing notification:', error)
      throw new Error('Failed to dismiss notification')
    }
  }

  /**
   * Get user notifications with pagination
   */
  async getUserNotifications(
    userId: string,
    options: {
      page?: number
      limit?: number
      unreadOnly?: boolean
      category?: string
      type?: NotificationType
    } = {}
  ) {
    try {
      const {
        page = 1,
        limit = 20,
        unreadOnly = false,
        category,
        type
      } = options

      const where: any = {
        userId,
        isDismissed: false
      }

      if (unreadOnly) {
        where.isRead = false
      }

      if (category || type) {
        where.notification = {}
        if (category) where.notification.category = category
        if (type) where.notification.type = type
      }

      const [notifications, total] = await Promise.all([
        prisma.userNotification.findMany({
          where,
          include: {
            notification: true
          },
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit
        }),
        prisma.userNotification.count({ where })
      ])

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Error getting user notifications:', error)
      throw new Error('Failed to get user notifications')
    }
  }

  /**
   * Get unread notification count for user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      return await prisma.userNotification.count({
        where: {
          userId,
          isRead: false,
          isDismissed: false
        }
      })
    } catch (error) {
      console.error('Error getting unread count:', error)
      return 0
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications(): Promise<void> {
    try {
      const now = new Date()
      
      // Delete expired notifications
      await prisma.notification.deleteMany({
        where: {
          expiresAt: {
            lt: now
          }
        }
      })

           } catch (error) {
      console.error('Error cleaning up expired notifications:', error)
    }
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance()

// Predefined notification templates
export const NotificationTemplates = {
  QUIZ_AVAILABLE: (quizTitle: string, quizId: string) => ({
    type: 'QUIZ_AVAILABLE' as NotificationType,
    title: 'New Quiz Available',
    message: `"${quizTitle}" is now available for you to take!`,
    actionUrl: `/student/browse/${quizId}`,
    priority: 'normal' as const,
    category: 'quiz'
  }),

  QUIZ_REMINDER: (quizTitle: string, quizId: string, timeLeft: string) => ({
    type: 'QUIZ_REMINDER' as NotificationType,
    title: 'Quiz Reminder',
    message: `Don't forget! "${quizTitle}" ends in ${timeLeft}`,
    actionUrl: `/student/quiz/${quizId}`,
    priority: 'high' as const,
    category: 'reminder'
  }),

  RESULT_PUBLISHED: (quizTitle: string, score: number, attemptId: string) => ({
    type: 'RESULT_PUBLISHED' as NotificationType,
    title: 'Quiz Results Available',
    message: `Your results for "${quizTitle}" are ready! You scored ${score}%`,
    actionUrl: `/student/quiz/result/${attemptId}`,
    priority: 'normal' as const,
    category: 'results'
  }),

  ACHIEVEMENT_UNLOCKED: (achievementName: string, description: string) => ({
    type: 'ACHIEVEMENT_UNLOCKED' as NotificationType,
    title: 'Achievement Unlocked! 🏆',
    message: `Congratulations! You've unlocked "${achievementName}": ${description}`,
    actionUrl: '/student/achievements',
    priority: 'high' as const,
    category: 'achievement'
  }),

  WELCOME: (userName: string) => ({
    type: 'WELCOME' as NotificationType,
    title: `Welcome to PrepLocus, ${userName}! 🎉`,
    message: 'Get started by exploring our quiz library and taking your first quiz.',
    actionUrl: '/student/browse',
    priority: 'normal' as const,
    category: 'welcome'
  }),

  COURSE_ENROLLED: (courseTitle: string, courseId: string) => ({
    type: 'COURSE_ENROLLED' as NotificationType,
    title: 'Course Enrollment Successful! 🎓',
    message: `You've successfully enrolled in "${courseTitle}". Start learning now!`,
    actionUrl: `/student/courses/${courseId}`,
    priority: 'high' as const,
    category: 'enrollment'
  }),

  BUNDLE_PURCHASED: (bundleTitle: string, bundleId: string) => ({
    type: 'BUNDLE_PURCHASED' as NotificationType,
    title: 'Quiz Bundle Purchased! 📚',
    message: `You've successfully purchased "${bundleTitle}". Start practicing now!`,
    actionUrl: `/student/quiz-bundles/${bundleId}`,
    priority: 'high' as const,
    category: 'purchase'
  }),

  QUIZ_ENROLLED: (quizTitle: string, quizId: string) => ({
    type: 'QUIZ_ENROLLED' as NotificationType,
    title: 'Successfully Enrolled! 📚',
    message: `You've been enrolled in "${quizTitle}". You can now start taking the quiz.`,
    actionUrl: `/student/quiz/${quizId}`,
    priority: 'normal' as const,
    category: 'enrollment'
  }),

  QUIZ_COMPLETED: (quizTitle: string, percentage: number, quizId: string) => ({
    type: 'QUIZ_COMPLETED' as NotificationType,
    title: 'Quiz Completed! ✅',
    message: `You've completed "${quizTitle}" with a score of ${percentage}%. Great job!`,
    actionUrl: `/student/quiz/${quizId}/result`,
    priority: 'normal' as const,
    category: 'completion'
  }),

  RANK_CHANGED: (newRank: number, oldRank: number) => ({
    type: 'RANK_CHANGED' as NotificationType,
    title: newRank < oldRank ? 'Rank Improved! 📈' : 'Rank Update 📊',
    message: newRank < oldRank
      ? `Great job! You've moved up from rank #${oldRank} to #${newRank}!`
      : `Your rank has been updated to #${newRank}.`,
    actionUrl: '/student/leaderboard',
    priority: newRank < oldRank ? 'high' as const : 'normal' as const,
    category: 'ranking'
  }),

  STREAK_MILESTONE: (streakDays: number) => ({
    type: 'STREAK_MILESTONE' as NotificationType,
    title: `${streakDays} Day Streak! 🔥`,
    message: `Amazing! You've maintained a ${streakDays}-day learning streak. Keep it up!`,
    actionUrl: '/student/analytics',
    priority: 'high' as const,
    category: 'milestone'
  }),

  QUIZ_REMINDER_TIME: (quizTitle: string, quizId: string, timeLeft: string) => ({
    type: 'QUIZ_REMINDER' as NotificationType,
    title: 'Quiz Reminder ⏰',
    message: `Don't forget! "${quizTitle}" ends in ${timeLeft}. Complete it now!`,
    actionUrl: `/student/quiz/${quizId}`,
    priority: 'high' as const,
    category: 'reminder'
  })
}
