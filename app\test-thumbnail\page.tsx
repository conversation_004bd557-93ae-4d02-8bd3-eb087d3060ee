"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FileUpload } from "@/components/ui/file-upload"
import { toast } from "@/lib/toast-utils"
import { X } from "lucide-react"

export default function TestThumbnailUpload() {
  const [thumbnail, setThumbnail] = useState<string>("")
  const [uploading, setUploading] = useState(false)

  const handleThumbnailUpload = async (uploadedFiles: any[]) => {
    if (uploadedFiles.length > 0) {
      const file = uploadedFiles[0]
      setThumbnail(file.url)
      toast.success('Thumbnail uploaded successfully!')
           }
  }

  const removeThumbnail = () => {
    setThumbnail("")
    toast.info('Thumbnail removed')
  }

  const handleUploadError = (error: string) => {
    toast.error(`Upload failed: ${error}`)
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Thumbnail Upload Test</CardTitle>
          <CardDescription>
            Test the thumbnail upload functionality for quizzes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Upload Thumbnail</h3>
            
            {thumbnail ? (
              <div className="space-y-4">
                <div className="relative inline-block">
                  <img
                    src={thumbnail}
                    alt="Uploaded thumbnail"
                    className="w-64 h-48 object-cover rounded-lg border shadow-sm"
                  />
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2"
                    onClick={removeThumbnail}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium">Thumbnail URL:</p>
                  <code className="block p-2 bg-muted rounded text-xs break-all">
                    {thumbnail}
                  </code>
                </div>
                
                <Button onClick={removeThumbnail} variant="outline">
                  Remove Thumbnail
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <FileUpload
                  onUpload={handleThumbnailUpload}
                  onError={handleUploadError}
                  acceptedTypes={['image/*']}
                  maxFiles={1}
                  maxSize={2 * 1024 * 1024} // 2MB
                  uploadType="image"
                  folder="test-thumbnails"
                  multiple={false}
                />
                
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>• Accepted formats: JPG, PNG, GIF, WebP</p>
                  <p>• Maximum file size: 2MB</p>
                  <p>• Recommended dimensions: 400x300 pixels</p>
                </div>
              </div>
            )}
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Test Results</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-muted rounded">
                <span className="font-medium">Upload Status:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  thumbnail 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {thumbnail ? 'Success' : 'No file uploaded'}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-muted rounded">
                <span className="font-medium">File URL:</span>
                <span className="text-xs text-muted-foreground">
                  {thumbnail ? 'Generated' : 'Not available'}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-muted rounded">
                <span className="font-medium">Storage:</span>
                <span className="text-xs text-muted-foreground">
                  Bunny CDN
                </span>
              </div>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Integration Test</h3>
            <p className="text-sm text-muted-foreground mb-4">
              This tests the same upload functionality used in quiz creation.
            </p>
            
            <div className="space-y-2">
              <Button 
                onClick={() => window.location.href = '/admin/quizzes/create'}
                variant="outline"
                className="w-full"
              >
                Test in Quiz Creation
              </Button>
              
              <Button 
                onClick={() => window.location.href = '/admin/files'}
                variant="outline"
                className="w-full"
              >
                View File Manager
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
