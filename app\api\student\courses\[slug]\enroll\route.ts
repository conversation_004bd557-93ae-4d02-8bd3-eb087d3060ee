import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { NotificationEvents } from '@/lib/notification-events'

// POST /api/student/courses/[slug]/enroll - Enroll student in course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    try {
      const resolvedParams = await params
      const courseSlug = resolvedParams?.slug as string

      if (!courseSlug) {
        return APIResponse.error('Course slug is required', 400)
      }

      // Find course by slug
      const course = await prisma.course.findUnique({
        where: { 
          slug: courseSlug,
          isActive: true,
          isPublished: true
        },
        select: {
          id: true,
          title: true,
          price: true
        }
      })

      if (!course) {
        return APIResponse.error('Course not found or not available', 404)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      if (existingEnrollment) {
        return APIResponse.error('You are already enrolled in this course', 400)
      }

      // For now, we'll create a free enrollment
      // In a real app, you'd integrate with payment processing here
      const enrollment = await prisma.courseEnrollment.create({
        data: {
          userId: user.id,
          courseId: course.id,
          status: 'active',
          progress: 0
        }
      })

      // Send enrollment notification
      try {
        await NotificationEvents.onCourseEnrolled(user.id, course.title, course.id)
      } catch (notificationError) {
        console.error('Failed to send enrollment notification:', notificationError)
        // Don't fail the enrollment if notification fails
      }

      return APIResponse.success({
        message: 'Successfully enrolled in course',
        enrollment: {
          id: enrollment.id,
          enrolledAt: enrollment.enrolledAt,
          status: enrollment.status,
          progress: enrollment.progress
        },
        course: {
          id: course.id,
          title: course.title
        }
      })

    } catch (error) {
      console.error('Error enrolling in course:', error)
      return APIResponse.error('Failed to enroll in course', 500)
    }
  }
)
