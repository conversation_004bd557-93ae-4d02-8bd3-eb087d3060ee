import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
// Use server-side socket manager, not client
import { liveQuizDelivery } from '@/lib/live-quiz-delivery'
import { z } from 'zod'

const submitAnswerSchema = z.object({
  questionId: z.string().min(1, "Question ID is required"),
  answer: z.union([z.string(), z.number(), z.array(z.string())]),
  timeSpent: z.number().min(0).optional() // Time spent on this question in seconds
})

// POST /api/student/live-quiz/sessions/[id]/answer - Submit answer for current question
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: submitAnswerSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string
    const { questionId, answer, timeSpent = 0 } = validatedBody

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session and verify it's active
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            include: {
              questions: {
                where: { id: questionId },
                select: {
                  id: true,
                  type: true,
                  correctAnswer: true,
                  points: true,
                  order: true
                }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      if (session.status !== 'ACTIVE') {
        return APIResponse.error('Session is not active', 400, 'SESSION_NOT_ACTIVE')
      }

      // Get user's participation
      const participation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId,
            userId: user.id
          }
        }
      })

      if (!participation) {
        return APIResponse.error('Not participating in this session', 400, 'NOT_PARTICIPATING')
      }

      if (!participation.isActive) {
        return APIResponse.error('Participation is not active', 400, 'PARTICIPATION_INACTIVE')
      }

      // Verify question exists and belongs to the quiz
      const question = session.quiz.questions[0]
      if (!question) {
        return APIResponse.error('Question not found in this quiz', 404, 'QUESTION_NOT_FOUND')
      }

      // Check if this is the current question or if late answers are allowed
      const currentQuestionOrder = session.currentQuestion
      if (question.order !== currentQuestionOrder) {
        // Allow answering previous questions but not future ones
        if (question.order > currentQuestionOrder) {
          return APIResponse.error('Cannot answer future questions', 400, 'FUTURE_QUESTION')
        }
      }

      // Get current answers
      const currentAnswers = participation.answers as Record<string, any>
      const hasAnsweredBefore = questionId in currentAnswers

      // Validate answer format based on question type
      let isCorrect = false
      let normalizedAnswer = answer

      switch (question.type) {
        case 'MCQ':
          if (typeof answer !== 'string') {
            return APIResponse.error('MCQ answer must be a string', 400, 'INVALID_ANSWER_FORMAT')
          }
          isCorrect = answer === question.correctAnswer
          break

        case 'TRUE_FALSE':
          if (typeof answer !== 'string' || !['true', 'false'].includes(answer.toLowerCase())) {
            return APIResponse.error('True/False answer must be "true" or "false"', 400, 'INVALID_ANSWER_FORMAT')
          }
          normalizedAnswer = answer.toLowerCase()
          isCorrect = normalizedAnswer === question.correctAnswer?.toLowerCase()
          break

        case 'SHORT_ANSWER':
          if (typeof answer !== 'string') {
            return APIResponse.error('Short answer must be a string', 400, 'INVALID_ANSWER_FORMAT')
          }
          // Case-insensitive comparison for short answers
          isCorrect = answer.toLowerCase().trim() === question.correctAnswer?.toLowerCase().trim()
          break

        case 'MATCHING':
          if (!Array.isArray(answer)) {
            return APIResponse.error('Matching answer must be an array', 400, 'INVALID_ANSWER_FORMAT')
          }
          // For matching questions, compare arrays
          const correctAnswerArray = Array.isArray(question.correctAnswer) 
            ? question.correctAnswer 
            : JSON.parse(question.correctAnswer || '[]')
          isCorrect = JSON.stringify(answer.sort()) === JSON.stringify(correctAnswerArray.sort())
          break

        default:
          return APIResponse.error('Unsupported question type', 400, 'UNSUPPORTED_QUESTION_TYPE')
      }

      // Calculate score changes
      let scoreChange = 0
      let correctAnswerChange = 0
      let totalAnsweredChange = 0

      if (!hasAnsweredBefore) {
        // First time answering this question
        totalAnsweredChange = 1
        if (isCorrect) {
          scoreChange = question.points
          correctAnswerChange = 1
        }
      } else {
        // Re-answering question - adjust score
        const previousAnswer = currentAnswers[questionId]
        const wasCorrectBefore = previousAnswer === question.correctAnswer // Simplified check
        
        if (isCorrect && !wasCorrectBefore) {
          scoreChange = question.points
          correctAnswerChange = 1
        } else if (!isCorrect && wasCorrectBefore) {
          scoreChange = -question.points
          correctAnswerChange = -1
        }
      }

      // Update participation with new answer
      const updatedAnswers = {
        ...currentAnswers,
        [questionId]: normalizedAnswer
      }

      const updatedParticipation = await prisma.liveQuizParticipant.update({
        where: { id: participation.id },
        data: {
          answers: updatedAnswers,
          score: participation.score + scoreChange,
          correctAnswers: participation.correctAnswers + correctAnswerChange,
          totalAnswered: participation.totalAnswered + totalAnsweredChange,
          timeSpent: participation.timeSpent + timeSpent,
          currentQuestion: Math.max(participation.currentQuestion, question.order)
        },
        include: {
          user: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      // Update rankings for all participants
      await updateSessionRankings(sessionId)

      // Get updated ranking for this participant
      const updatedParticipationWithRank = await prisma.liveQuizParticipant.findUnique({
        where: { id: participation.id },
        select: { rank: true }
      })

      // Process answer submission with delivery service and broadcast
      try {
        // Use delivery service for real-time processing
        await liveQuizDelivery.processAnswerSubmission(
          sessionId,
          user.id,
          questionId,
          normalizedAnswer,
          timeSpent
        )

        const { getSocketManager } = await import('@/lib/socket-server')
        const socketManager = getSocketManager()
        // Notify session room about answer submission
        socketManager?.broadcastToRoom(`live-quiz:${sessionId}`, 'live-quiz:answer-submitted', {
          sessionId,
          participant: {
            userId: updatedParticipation.userId,
            userName: updatedParticipation.user.name || 'Unknown',
            questionId,
            isCorrect,
            score: updatedParticipation.score,
            rank: updatedParticipationWithRank?.rank || undefined,
            timeSpent
          },
          questionOrder: question.order
        })

        // Send feedback back to the specific user via their user room
        socketManager?.broadcastToRoom(`user:${user.id}`, 'live-quiz:answer-feedback', {
          userId: user.id,
          sessionId,
          questionId,
          isCorrect,
          correctAnswer: question.correctAnswer,
          pointsEarned: isCorrect ? question.points : 0,
          newScore: updatedParticipation.score,
          newRank: updatedParticipationWithRank?.rank || undefined
        })

      } catch (socketError) {
        console.warn('Failed to send socket notifications:', socketError)
        // Continue execution even if socket notifications fail
      }

      return APIResponse.success({
        submission: {
          questionId,
          answer: normalizedAnswer,
          isCorrect,
          pointsEarned: isCorrect ? question.points : 0,
          timeSpent
        },
        participation: {
          score: updatedParticipation.score,
          correctAnswers: updatedParticipation.correctAnswers,
          totalAnswered: updatedParticipation.totalAnswered,
          currentQuestion: updatedParticipation.currentQuestion,
          rank: updatedParticipationWithRank?.rank,
          totalTimeSpent: updatedParticipation.timeSpent
        },
        feedback: {
          isCorrect,
          correctAnswer: question.correctAnswer,
          explanation: null // Don't show explanation during live quiz
        }
      }, 'Answer submitted successfully')

    } catch (error) {
      console.error('Error submitting answer:', error)
      return APIResponse.error('Failed to submit answer', 500)
    }
  }
)

// Helper function to update rankings for all participants in a session
async function updateSessionRankings(sessionId: string) {
  try {
    const participants = await prisma.liveQuizParticipant.findMany({
      where: { 
        sessionId,
        isActive: true
      },
      orderBy: [
        { score: 'desc' },
        { totalAnswered: 'desc' },
        { timeSpent: 'asc' }
      ]
    })

    // Update rankings
    for (let i = 0; i < participants.length; i++) {
      await prisma.liveQuizParticipant.update({
        where: { id: participants[i].id },
        data: { rank: i + 1 }
      })
    }
  } catch (error) {
    console.error('Error updating session rankings:', error)
  }
}
