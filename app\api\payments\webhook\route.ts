import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { razorpayService } from '@/lib/razorpay-service'
import crypto from 'crypto'

// Razorpay sends webhooks without user context; do not require auth.
export const POST = createAPIHandler(
  {
    requireAuth: false,
  },
  async (request: NextRequest) => {
    try {
      const signature = request.headers.get('x-razorpay-signature') || request.headers.get('X-Razorpay-Signature')
      if (!signature) {
        return APIResponse.error('Missing signature', 400)
      }

      const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET || ''
      if (!webhookSecret) {
        return APIResponse.error('Webhook not configured', 500)
      }

      const payload = await request.text()

      const expected = crypto
        .createHmac('sha256', webhookSecret)
        .update(payload)
        .digest('hex')

      if (expected !== signature) {
        return APIResponse.error('Invalid signature', 400)
      }

      const event = JSON.parse(payload)
      const eventType: string = event?.event

      // Handle key payment/order events
      if (eventType === 'payment.captured') {
        const paymentId = event?.payload?.payment?.entity?.id as string
        const orderId = event?.payload?.payment?.entity?.order_id as string
        if (orderId && paymentId) {
          await razorpayService.markPaymentPaidAndFulfill(orderId, paymentId)
        }
      } else if (eventType === 'order.paid') {
        const orderId = event?.payload?.order?.entity?.id as string
        const paymentId = event?.payload?.order?.entity?.payments?.[0]?.id as string | undefined
        if (orderId) {
          await razorpayService.markPaymentPaidAndFulfill(orderId, paymentId || '')
        }
      } else if (eventType === 'payment.failed') {
        const orderId = event?.payload?.payment?.entity?.order_id as string
        const reason = event?.payload?.payment?.entity?.error_reason as string | undefined
        if (orderId) {
          await razorpayService.markPaymentFailed(orderId, reason || 'payment.failed webhook')
        }
      }

      return APIResponse.success({ received: true })
    } catch (error) {
      console.error('Razorpay webhook error:', error)
      return APIResponse.error('Webhook processing failed', 500)
    }
  }
)


