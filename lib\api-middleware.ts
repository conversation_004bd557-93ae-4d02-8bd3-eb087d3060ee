import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { z } from 'zod'

export interface APIMiddlewareConfig {
  requireAuth?: boolean
  requireRole?: 'ADMIN' | 'STUDENT'
  validateBody?: z.ZodSchema
  validateQuery?: z.ZodSchema
}

export class APIError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class APIResponse {
  static success(data: any, message?: string, statusCode: number = 200) {
    return NextResponse.json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    }, { status: statusCode })
  }

  static error(
    message: string, 
    statusCode: number = 500, 
    code?: string, 
    details?: any
  ) {
    return NextResponse.json({
      success: false,
      error: {
        message,
        code,
        details,
        timestamp: new Date().toISOString()
      }
    }, { status: statusCode })
  }

  static paginated(
    data: any[],
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    },
    message?: string
  ) {
    return NextResponse.json({
      success: true,
      data,
      pagination,
      message,
      timestamp: new Date().toISOString()
    })
  }

  static unauthorized(message: string = 'Unauthorized') {
    return this.error(message, 401, 'UNAUTHORIZED')
  }

  static forbidden(message: string = 'Forbidden') {
    return this.error(message, 403, 'FORBIDDEN')
  }

  static notFound(message: string = 'Not found') {
    return this.error(message, 404, 'NOT_FOUND')
  }

  static badRequest(message: string = 'Bad request') {
    return this.error(message, 400, 'BAD_REQUEST')
  }

  static internalServerError(message: string = 'Internal server error') {
    return this.error(message, 500, 'INTERNAL_SERVER_ERROR')
  }
}

export function createAPIHandler(
  config: APIMiddlewareConfig,
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // CSRF Origin check for state-changing methods in production
      if (process.env.NODE_ENV === 'production' && ['POST','PUT','PATCH','DELETE'].includes(request.method)) {
        const origin = request.headers.get('origin') || ''
        const allowedOrigins = [
          process.env.NEXTAUTH_URL || '',
          (process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : ''),
          process.env.AUTH_URL || '',
        ].filter(Boolean)
        if (origin && !allowedOrigins.includes(origin)) {
          return APIResponse.error('Invalid origin', 403, 'FORBIDDEN')
        }
      }
      // Authentication: always attach session if present; enforce only when required
      const session = await auth()
      if ((config.requireAuth || config.requireRole) && !session?.user?.id) {
        return APIResponse.error('Authentication required', 401, 'UNAUTHORIZED')
      }

      // Role-based access control
      if (config.requireRole && session?.user?.role !== config.requireRole) {
        return APIResponse.error(
          `${config.requireRole} role required`,
          403,
          'FORBIDDEN'
        )
      }

      // Request body validation
      let validatedBody = null
      if (config.validateBody && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          validatedBody = config.validateBody.parse(body)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.error(
              'Invalid request body',
              400,
              'VALIDATION_ERROR',
              error.errors
            )
          }
          return APIResponse.error('Invalid JSON in request body', 400, 'INVALID_JSON')
        }
      }

      // Query parameters validation
      let validatedQuery = null
      if (config.validateQuery) {
        const { searchParams } = new URL(request.url)
        const queryObject = Object.fromEntries(searchParams.entries())
        try {
          validatedQuery = config.validateQuery.parse(queryObject)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.error(
              'Invalid query parameters',
              400,
              'VALIDATION_ERROR',
              error.errors
            )
          }
        }
      }

      // Create enhanced context
      const enhancedContext = {
        ...context,
        session,
        validatedBody,
        validatedQuery,
        user: session?.user
      }

      // Call the actual handler
      const response = await handler(request, enhancedContext)

      return response

    } catch (error) {
      console.error('API Handler Error:', error)

      if (error instanceof APIError) {
        return APIResponse.error(error.message, error.statusCode, error.code)
      }

      if (error instanceof z.ZodError) {
        return APIResponse.error(
          'Validation error',
          400,
          'VALIDATION_ERROR',
          error.errors
        )
      }

      // Generic error
      return APIResponse.error(
        'Internal server error',
        500,
        'INTERNAL_ERROR'
      )
    }
  }
}



// Common validation schemas
export const commonSchemas = {
  pagination: z.object({
    page: z.string().optional().transform(val => parseInt(val || '1') || 1),
    limit: z.string().optional().transform(val => Math.min(parseInt(val || '20') || 20, 100)),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional()
  }),

  search: z.object({
    q: z.string().optional(),
    category: z.string().optional(),
    tags: z.string().optional(),
    status: z.string().optional()
  }),

  idParam: z.object({
    id: z.string().min(1, 'ID is required')
  })
}



// API versioning helper
export function getAPIVersion(request: NextRequest): string {
  const version = request.headers.get('api-version') || 
                 request.nextUrl.searchParams.get('version') ||
                 'v1'
  return version
}

// CORS helper
export function setCORSHeaders(response: NextResponse, origin?: string): NextResponse {
  if (origin) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, API-Version')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}

// Request logging (only in development)
export function logAPIRequest(request: NextRequest, response?: NextResponse) {
  if (process.env.NODE_ENV === 'development') {
    const timestamp = new Date().toISOString()
    const method = request.method
    const url = request.url
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    const status = response?.status || 'pending'

       }
}

// Health check endpoint helper
export function createHealthCheck() {
  return APIResponse.success({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  })
}
