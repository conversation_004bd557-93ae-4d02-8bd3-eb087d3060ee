import { Check, X } from 'lucide-react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Question } from '@/lib/schemas'


interface QuizReviewProps {
  questions: Question[]
  userAnswers: string[]
}

export default function QuizReview({ questions, userAnswers }: QuizReviewProps) {
  const answerLabels: ("A" | "B" | "C" | "D")[] = ["A", "B", "C", "D"]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Quiz Review</CardTitle>
      </CardHeader>
      <CardContent>
          {questions.map((question, questionIndex) => (
            <div key={questionIndex} className="mb-8 last:mb-0">
              <h3 className="text-lg font-semibold mb-4">{question.question}</h3>
              <div className="space-y-2">
                {question.type === 'MCQ' && question.options?.map((option, optionIndex) => {
                  const currentLabel = answerLabels[optionIndex]
                  const isCorrect = currentLabel === question.answer
                  const isSelected = currentLabel === userAnswers[questionIndex]
                  const isIncorrectSelection = isSelected && !isCorrect

                  return (
                    <div
                      key={optionIndex}
                      className={`flex items-center p-4 rounded-lg ${
                        isCorrect
                          ? 'bg-green-100 dark:bg-green-700/50'
                          : isIncorrectSelection
                          ? 'bg-red-100 dark:bg-red-700/50'
                          : 'border border-border'
                      }`}
                    >
                      <span className="text-lg font-medium mr-4 w-6">{currentLabel}</span>
                      <span className="flex-grow">{option}</span>
                      {isCorrect && (
                        <Check className="ml-2 text-green-600 dark:text-green-400" size={20} />
                      )}
                      {isIncorrectSelection && (
                        <X className="ml-2 text-red-600 dark:text-red-400" size={20} />
                      )}
                    </div>
                  )
                })}

                {question.type === 'TRUE_FALSE' && (
                  <div className="space-y-2">
                    <div className={`flex items-center p-4 rounded-lg ${
                      question.answer === true ? 'bg-green-100 dark:bg-green-700/50' : 'border border-border'
                    }`}>
                      <span className="flex-grow">True</span>
                      {question.answer === true && <Check className="ml-2 text-green-600 dark:text-green-400" size={20} />}
                    </div>
                    <div className={`flex items-center p-4 rounded-lg ${
                      question.answer === false ? 'bg-green-100 dark:bg-green-700/50' : 'border border-border'
                    }`}>
                      <span className="flex-grow">False</span>
                      {question.answer === false && <Check className="ml-2 text-green-600 dark:text-green-400" size={20} />}
                    </div>
                  </div>
                )}

                {question.type === 'SHORT_ANSWER' && (
                  <div className="p-4 rounded-lg bg-green-100 dark:bg-green-700/50">
                    <span className="font-medium">Correct Answer: </span>
                    <span>{question.answer}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
      </CardContent>
    </Card>
  )
}

