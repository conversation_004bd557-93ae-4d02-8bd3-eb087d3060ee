import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['WAITING', 'ACTIVE', 'PAUSED', 'COMPLETED', 'CANCELLED']).optional(),
  search: z.string().optional(),
  quizId: z.string().optional()
})

const createLiveQuizSessionSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  maxParticipants: z.number().min(1).optional(),
  questionTimeLimit: z.number().min(10).max(300).optional(), // 10 seconds to 5 minutes
  autoAdvance: z.boolean().default(true),
  showLeaderboard: z.boolean().default(true),
  allowLateJoin: z.boolean().default(false),
  scheduledStart: z.string().datetime().optional()
})

// GET /api/admin/live-quiz/sessions - Get live quiz sessions
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    const { page, limit, status, search, quizId } = validatedQuery

    try {
      // Build where clause
      const where: any = {}
      
      if (status) {
        where.status = status
      }
      
      if (quizId) {
        where.quizId = quizId
      }
      
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { quiz: { title: { contains: search, mode: 'insensitive' } } }
        ]
      }

      // Get total count
      const totalCount = await prisma.liveQuizSession.count({ where })

      // Get sessions with pagination
      const sessions = await prisma.liveQuizSession.findMany({
        where,
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true,
              timeLimit: true,
              questions: {
                select: { id: true }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          participants: {
            select: {
              id: true,
              userId: true,
              joinedAt: true,
              isActive: true,
              score: true,
              rank: true,
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          },
          _count: {
            select: {
              participants: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      })

      const totalPages = Math.ceil(totalCount / limit)

      return APIResponse.success({
        sessions: sessions.map(session => ({
          ...session,
          participantCount: session._count.participants,
          questionCount: session.quiz.questions.length
        })),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }, 'Live quiz sessions retrieved successfully')

    } catch (error) {
      console.error('Error fetching live quiz sessions:', error)
      return APIResponse.error('Failed to fetch live quiz sessions', 500)
    }
  }
)

// POST /api/admin/live-quiz/sessions - Create live quiz session
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createLiveQuizSessionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    const {
      quizId,
      title,
      description,
      maxParticipants,
      questionTimeLimit,
      autoAdvance,
      showLeaderboard,
      allowLateJoin,
      scheduledStart
    } = validatedBody

    try {
      // Verify quiz exists and is published
      const quiz = await prisma.quiz.findUnique({
        where: { id: quizId },
        include: {
          questions: {
            select: { id: true }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404, 'QUIZ_NOT_FOUND')
      }

      if (!quiz.isPublished) {
        return APIResponse.error('Quiz must be published to create live session', 400, 'QUIZ_NOT_PUBLISHED')
      }

      if (quiz.questions.length === 0) {
        return APIResponse.error('Quiz must have at least one question', 400, 'NO_QUESTIONS')
      }

      // Validate scheduled start time
      if (scheduledStart) {
        const scheduledDate = new Date(scheduledStart)
        if (scheduledDate <= new Date()) {
          return APIResponse.error('Scheduled start time must be in the future', 400, 'INVALID_SCHEDULED_TIME')
        }
      }

      // Create live quiz session
      const session = await prisma.liveQuizSession.create({
        data: {
          quizId,
          title,
          description,
          maxParticipants,
          questionTimeLimit,
          autoAdvance,
          showLeaderboard,
          allowLateJoin,
          scheduledStart: scheduledStart ? new Date(scheduledStart) : null,
          createdBy: user.id
        },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true,
              timeLimit: true,
              questions: {
                select: { id: true }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              participants: true
            }
          }
        }
      })

      return APIResponse.success({
        ...session,
        participantCount: session._count.participants,
        questionCount: session.quiz.questions.length
      }, 'Live quiz session created successfully')

    } catch (error) {
      console.error('Error creating live quiz session:', error)
      return APIResponse.error('Failed to create live quiz session', 500)
    }
  }
)
