# Socket Client Methods Documentation

This document lists all available public methods in the SocketClient class.

## Connection Methods

### `isConnected(): boolean`
Check if the socket is currently connected.

### `disconnect(): void`
Disconnect from the socket server.

### `authenticate(userData: object): void`
Authenticate the user with the socket server.
```typescript
socketClient.authenticate({
  userId: 'user-123',
  name: '<PERSON>',
  email: '<EMAIL>',
  role: 'STUDENT'
})
```

### `on(event: string, callback: Function): void`
Add an event listener.

### `off(event: string, callback?: Function): void`
Remove an event listener.

## Quiz Methods

### `joinQuiz(quizId: string): void`
Join a quiz session.

### `leaveQuiz(quizId: string): void`
Leave a quiz session.

### `updateQuizProgress(data: object): void`
Update quiz progress.
```typescript
socketClient.updateQuizProgress({
  quizId: 'quiz-123',
  questionIndex: 1,
  timeRemaining: 30,
  answered: true
})
```

### `sendQuizProgress(data: object): void`
Send quiz progress update.

### `completeQuiz(data: object): void`
Mark quiz as completed.

## Live Quiz Methods

### Session Management
- `joinLiveQuizSession(sessionId: string): void`
- `leaveLiveQuizSession(sessionId: string): void`
- `startQuizSession(data: object): void`
- `endQuizSession(data: object): void`

### Participant Management
- `joinQuizSession(data: object): void`
- `leaveQuizSession(data: object): void`

### Answer Submission
- `submitLiveQuizAnswer(data: object): void`
- `submitQuizAnswer(data: object): void`

### Progress Synchronization
- `syncLiveQuizProgress(data: object): void`
- `syncQuizProgress(data: object): void`

### Statistics
- `updateSessionStatistics(data: object): void`
- `updateQuestionStatistics(data: object): void`

### Broadcasting
- `broadcastLiveQuizEvent(eventName: string, data: any): void`

### Utility
- `requestCurrentQuestion(sessionId: string): void`

## Mission Methods

### `updateMissionProgress(data: object): void`
Update mission progress.
```typescript
socketClient.updateMissionProgress({
  missionId: 'mission-123',
  contentId: 'content-456',
  contentType: 'LESSON',
  isCompleted: true,
  courseId: 'course-789'
})
```

## Notification Methods

### `sendNotification(data: object): void`
Send notification to specific user.
```typescript
socketClient.sendNotification({
  targetUserId: 'user-123',
  type: 'success',
  title: 'Quiz Completed',
  message: 'You scored 95%!'
})
```

### `broadcastNotification(data: object): void`
Broadcast notification to all users.
```typescript
socketClient.broadcastNotification({
  type: 'info',
  title: 'System Update',
  message: 'System will be updated in 10 minutes'
})
```

## Chat Methods

### `joinChatRoom(roomId: string): void`
Join a chat room.

### `leaveChatRoom(roomId: string): void`
Leave a chat room.

### `sendChatMessage(data: object): void`
Send a chat message.
```typescript
socketClient.sendChatMessage({
  roomId: 'room-123',
  message: 'Hello everyone!',
  type: 'text'
})
```

### `setTyping(roomId: string, isTyping: boolean): void`
Set typing indicator.

## Document Collaboration Methods

### `joinDocument(documentId: string): void`
Join a document for collaboration.

### `sendDocumentChanges(data: object): void`
Send document changes.

### `updateCursor(data: object): void`
Update cursor position.

## Course Methods

### `joinCourse(courseId: string): void`
Join a course room.

### `leaveCourse(courseId: string): void`
Leave a course room.

### `sendProgressUpdate(data: object): void`
Send course progress update.

## Room Management Methods

### `joinRoom(roomId: string, userData?: any): void`
Join a generic room.

### `leaveRoom(roomId: string): void`
Leave a generic room.

## Utility Methods

### `sendEvent(eventName: string, data: any): void`
Send a custom event.

### `requestMetrics(): void`
Request server metrics.

### `setUserData(userData: object): void`
Set user data.

## Usage Examples

### Basic Setup
```typescript
import { getSocketClient } from '@/lib/socket-client'

const socketClient = getSocketClient()

// Authenticate user
socketClient.authenticate({
  userId: 'user-123',
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'STUDENT'
})
```

### Live Quiz Session
```typescript
// Join a live quiz session
socketClient.joinLiveQuizSession('session-123')

// Listen for events
socketClient.on('live-quiz:question-delivered', (data) => {
  console.log('New question:', data)
})

// Submit an answer
socketClient.submitLiveQuizAnswer({
  sessionId: 'session-123',
  questionId: 'q1',
  answer: 'A',
  timeSpent: 15
})

// Sync progress
socketClient.syncLiveQuizProgress({
  sessionId: 'session-123',
  currentQuestion: 2,
  score: 80,
  timeSpent: 120
})
```

### Mission Progress
```typescript
// Update mission progress
socketClient.updateMissionProgress({
  missionId: 'mission-123',
  contentId: 'lesson-456',
  contentType: 'LESSON',
  isCompleted: true,
  courseId: 'course-789'
})
```

### Notifications
```typescript
// Send to specific user
socketClient.sendNotification({
  targetUserId: 'user-123',
  type: 'success',
  title: 'Achievement Unlocked!',
  message: 'You completed your first mission!'
})

// Broadcast to all users
socketClient.broadcastNotification({
  type: 'info',
  title: 'Maintenance Notice',
  message: 'System maintenance scheduled for tonight'
})
```

## Error Handling

All methods include built-in error handling:
- Methods check if socket is connected before emitting
- Invalid data is handled gracefully
- Connection failures are managed automatically

## Testing

Use the test suite to verify all methods work correctly:
```typescript
import { runSocketClientTests } from '@/lib/socket-client-test'

runSocketClientTests()
```
