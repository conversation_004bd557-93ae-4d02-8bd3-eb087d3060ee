import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateQuizSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  type: z.enum(['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE']).optional(),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']).optional(),
  tags: z.array(z.string()).optional(),
  timeLimit: z.number().min(1).optional(),
  maxAttempts: z.number().min(1).optional(),
  passingScore: z.number().min(0).max(100).optional(),
  instructions: z.string().optional(),
  thumbnail: z.string().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
  isPublished: z.boolean().optional(),
  // Category fields
  subjectId: z.string().optional(),
  chapterId: z.string().optional(),
  topicId: z.string().optional(),
  questions: z.array(z.object({
    id: z.string().optional(), // For existing questions
    type: z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING']),
    text: z.string().min(1),
    options: z.array(z.string()),
    correctAnswer: z.string(),
    explanation: z.string().optional(),
    points: z.number().min(1),
    image: z.string().optional(),
    order: z.number()
  })).optional(),
})

export const GET = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN' }, async (
  request: NextRequest,
  { params }:   { params: Promise<{ id: string } > }
) => {
  try {

   const {id} = await params
    const quiz = await prisma.quiz.findUnique({
      where: { id: id },
      include: {
        creator: {
          select: { name: true, email: true }
        },
        questions: {
          orderBy: { order: 'asc' }
        },
        attempts: {
          include: {
            user: {
              select: { name: true, email: true }
            }
          },
          orderBy: { startedAt: 'desc' }
        }
      }
    })

 

    if (!quiz) {
  
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Calculate statistics with safe field access
    const stats = {
      totalAttempts: quiz.attempts.length,
      averageScore: quiz.attempts.length > 0
        ? Math.round(quiz.attempts.reduce((sum, attempt) => sum + (attempt.score || 0), 0) / quiz.attempts.length)
        : 0,
      completionRate: quiz.attempts.length > 0
        ? Math.round((quiz.attempts.filter(attempt => {
            // Check if attempt is completed (either isCompleted field or has completedAt)
            const isCompleted = (attempt as any).isCompleted === true || attempt.completedAt !== null
            return isCompleted
          }).length / quiz.attempts.length) * 100)
        : 0
    }

    // Transform the response to ensure all expected fields are present
    const response = {
      id: quiz.id,
      title: quiz.title,
      description: quiz.description,
      type: quiz.type,
      difficulty: quiz.difficulty,
      thumbnail: quiz.thumbnail,
      tags: quiz.tags || [],
      timeLimit: quiz.timeLimit,
      startTime: quiz.startTime?.toISOString(),
      endTime: quiz.endTime?.toISOString(),
      maxAttempts: quiz.maxAttempts,
      passingScore: quiz.passingScore,
      instructions: quiz.instructions,
      isPublished: quiz.isPublished,
      createdAt: quiz.createdAt.toISOString(),
      updatedAt: quiz.updatedAt.toISOString(),
      creator: quiz.creator,
      questions: quiz.questions.map(q => ({
        id: q.id,
        type: q.type,
        text: q.text,
        options: q.options,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        points: q.points,
        order: q.order
      })),
      attempts: quiz.attempts.map(a => ({
        id: a.id,
        user: a.user,
        score: a.score || 0,
        percentage: a.percentage || 0,
        timeSpent: a.timeSpent,
        isCompleted: (a as any).isCompleted === true || a.completedAt !== null,
        startedAt: a.startedAt.toISOString(),
        completedAt: a.completedAt?.toISOString()
      })),
      stats
    }

    
    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching quiz:', error)

    // Provide more specific error information
    let errorMessage = 'Failed to fetch quiz'
    if (error instanceof Error) {
      errorMessage = error.message
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: error instanceof Error ? error.stack : String(error)
      },
      { status: 500 }
    )
  }
})

export const PUT = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN', validateBody: updateQuizSchema }, async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) => {
  try {
    const validatedData = (request as any).validatedBody
    const { id } = await params

    // Check if quiz exists and user has permission
    const existingQuiz = await prisma.quiz.findUnique({
      where: { id: id}
    })

    if (!existingQuiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Extract questions from validated data
    const { questions, ...quizData } = validatedData

    // Update quiz and questions in a transaction
    const updatedQuiz = await prisma.$transaction(async (tx) => {
      // Update quiz metadata
      const quiz = await tx.quiz.update({
        where: { id: id },
        data: {
          ...quizData,
          startTime: quizData.startTime ? new Date(quizData.startTime) : undefined,
          endTime: quizData.endTime ? new Date(quizData.endTime) : undefined,
        }
      })

      // Handle questions if provided
      if (questions && questions.length > 0) {
        // Delete existing questions
        await tx.question.deleteMany({
          where: { quizId: id }
        })

        // Create new questions
        await tx.question.createMany({
          data: questions.map((question: any, index: number) => ({
            quizId: id,
            type: question.type,
            text: question.text,
            options: question.options,
            correctAnswer: question.correctAnswer,
            explanation: question.explanation,
            points: question.points,
            image: question.image,
            order: question.order || index + 1,
          }))
        })
      }

      return quiz
    })

    return NextResponse.json({ success: true, quiz: updatedQuiz })
  } catch (error) {
    console.error('Error updating quiz:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update quiz' },
      { status: 500 }
    )
  }
})

export const DELETE = createAPIHandler({ requireAuth: true, requireRole: 'ADMIN' }, async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string } > }
) => {
  try {
    const { id } = await params
    // Check if quiz exists
    const existingQuiz = await prisma.quiz.findUnique({
      where: { id: id },
      include: {
        attempts: { select: { id: true } }
      }
    })

    if (!existingQuiz) {
      return NextResponse.json(
        { error: 'Quiz not found' },
        { status: 404 }
      )
    }

    // Check if quiz has attempts (might want to prevent deletion)
    if (existingQuiz.attempts.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete quiz with existing attempts. Consider archiving instead.' },
        { status: 400 }
      )
    }

    // Delete quiz (questions will be deleted due to cascade)
    await prisma.quiz.delete({
      where: { id: id }
    })

    return NextResponse.json({ success: true, message: 'Quiz deleted successfully' })
  } catch (error) {
    console.error('Error deleting quiz:', error)
    return NextResponse.json(
      { error: 'Failed to delete quiz' },
      { status: 500 }
    )
  }
})
