import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/debug/quiz-bundle-flow - Debug quiz bundle data flow
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      const { searchParams } = new URL(request.url)
      const testUserId = searchParams.get('userId') || user.id
      const bundleId = searchParams.get('bundleId')

      const debugInfo: any = {
        timestamp: new Date().toISOString(),
        testUserId,
        bundleId
      }

      // 1. Test Quiz Bundle Creation
      debugInfo.bundleCreation = {
        totalBundles: await prisma.quizBundle.count(),
        recentBundles: await prisma.quizBundle.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            title: true,
            slug: true,
            price: true,
            isActive: true,
            isPublished: true,
            createdAt: true
          }
        })
      }

      // 2. Test Quiz Loading for Bundle Creation
      debugInfo.quizLoading = {
        totalQuizzes: await prisma.quiz.count(),
        publishedQuizzes: await prisma.quiz.count({
          where: { isPublished: true }
        }),
        sampleQuizzes: await prisma.quiz.findMany({
          where: { isPublished: true },
          take: 5,
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            isPublished: true
          }
        })
      }

      // 3. Test Bundle Purchases
      if (bundleId) {
        debugInfo.bundlePurchases = {
          bundleExists: await prisma.quizBundle.findUnique({
            where: { id: bundleId },
            select: {
              id: true,
              title: true,
              price: true,
              isActive: true,
              isPublished: true
            }
          }),
          userPurchase: await prisma.quizBundlePurchase.findUnique({
            where: {
              userId_bundleId: {
                userId: testUserId,
                bundleId: bundleId
              }
            },
            select: {
              id: true,
              status: true,
              purchasedAt: true,
              progress: true
            }
          }),
          allPurchasesForBundle: await prisma.quizBundlePurchase.count({
            where: { bundleId: bundleId }
          })
        }
      }

      // 4. Test Student Bundle Access
      debugInfo.studentAccess = {
        userPurchases: await prisma.quizBundlePurchase.findMany({
          where: { userId: testUserId },
          include: {
            bundle: {
              select: {
                id: true,
                title: true,
                slug: true,
                price: true
              }
            }
          }
        }),
        totalUserPurchases: await prisma.quizBundlePurchase.count({
          where: { userId: testUserId }
        })
      }

      // 5. Test Course Categories
      debugInfo.courseCategories = {
        categoriesTableExists: true,
        totalCategories: 0,
        sampleCategories: []
      }

      try {
        debugInfo.courseCategories.totalCategories = await prisma.courseCategory.count()
        debugInfo.courseCategories.sampleCategories = await prisma.courseCategory.findMany({
          take: 5,
          select: {
            id: true,
            name: true,
            description: true,
            isActive: true
          }
        })
      } catch (error) {
        debugInfo.courseCategories.categoriesTableExists = false
        debugInfo.courseCategories.error = error instanceof Error ? error.message : 'Unknown error'
        
        // Fallback to course grouping
        try {
          const courseCategories = await prisma.course.groupBy({
            by: ['category'],
            where: {
              category: { not: null },
              isActive: true
            },
            _count: { category: true }
          })
          debugInfo.courseCategories.fallbackCategories = courseCategories
        } catch (fallbackError) {
          debugInfo.courseCategories.fallbackError = fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error'
        }
      }

      // 6. Test API Response Formats
      debugInfo.apiFormats = {
        quizBundlesAPI: {
          endpoint: '/api/quiz-bundles',
          expectedFormat: '{ data: { bundles: [...] } }',
          note: 'Used by public and student pages'
        },
        adminQuizzesAPI: {
          endpoint: '/api/admin/quizzes',
          expectedFormat: '{ quizzes: [...], success: true }',
          note: 'Used by quiz selector and bundle form'
        },
        studentBundlesAPI: {
          endpoint: '/api/student/quiz-bundles',
          expectedFormat: '{ data: { bundles: [...] } }',
          note: 'Used by student dashboard'
        },
        courseCategoriesAPI: {
          endpoint: '/api/admin/courses/categories',
          expectedFormat: '{ categories: [...] }',
          note: 'Used by course forms'
        }
      }

      // 7. Common Issues and Solutions
      debugInfo.commonIssues = [
        {
          issue: 'Bundle not appearing after creation',
          possibleCauses: [
            'API response format mismatch (data.data.bundles vs data.bundles)',
            'Bundle not published (isPublished: false)',
            'Bundle not active (isActive: false)',
            'Frontend not refreshing after creation'
          ],
          solutions: [
            'Check API response structure in browser console',
            'Verify bundle isPublished and isActive flags',
            'Ensure fetchBundles() is called after creation'
          ]
        },
        {
          issue: 'Quiz selector not loading quizzes',
          possibleCauses: [
            'No published quizzes in database',
            'API response format mismatch',
            'Network or authentication issues'
          ],
          solutions: [
            'Create and publish some quizzes first',
            'Check browser console for API errors',
            'Verify admin authentication'
          ]
        },
        {
          issue: 'Course categories not loading in edit mode',
          possibleCauses: [
            'CourseCategory table not created',
            'No categories in database',
            'API response format issues'
          ],
          solutions: [
            'Run: npx prisma db push',
            'Create some categories via admin interface',
            'Check API response in browser console'
          ]
        }
      ]

      return APIResponse.success({
        message: 'Quiz bundle flow debug information',
        debug: debugInfo
      })

    } catch (error) {
      console.error('Debug API error:', error)
      return APIResponse.error('Debug failed', 500)
    }
  }
)
