import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateLessonSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  description: z.string().optional(),
  type: z.enum(['VIDEO', 'TEXT', 'QUIZ', 'ASSIGNMENT', 'DOCUMENT']).optional(),
   
  duration: z.number().min(0).optional(),
  isPublished: z.boolean().optional(),
  isFree: z.boolean().optional(),
  content: z.string().optional(),
  videoUrl: z.string().optional(),
  attachments: z.array(z.any()).optional(),
  hasQuiz: z.boolean().optional(),
  quizId: z.string().optional()
})

// GET /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId]/lessons/[lessonId] - Get lesson
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string
      const lessonId = resolvedParams?.lessonId as string

      if (!courseId || !sectionId || !chapterId || !lessonId) {
        return APIResponse.error('All IDs are required', 400)
      }

      const lesson = await prisma.courseLesson.findFirst({
        where: {
          id: lessonId,
          chapterId: chapterId,
          chapter: {
            sectionId: sectionId,
            section: {
              courseId: courseId
            }
          }
        },
        include: {
          video: true
        }
      })

      if (!lesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      return APIResponse.success({ lesson })
    } catch (error) {
      console.error('Error fetching lesson:', error)
      return APIResponse.error('Failed to fetch lesson', 500)
    }
  }
)

// PUT /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId]/lessons/[lessonId] - Update lesson
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: updateLessonSchema
  },
  async (request: NextRequest, { params, validatedBody }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string
      const lessonId = resolvedParams?.lessonId as string

      if (!courseId || !sectionId || !chapterId || !lessonId) {
        return APIResponse.error('All IDs are required', 400)
      }

      // Verify lesson exists
      const existingLesson = await prisma.courseLesson.findFirst({
        where: {
          id: lessonId,
          chapterId: chapterId,
          chapter: {
            sectionId: sectionId,
            section: {
              courseId: courseId
            }
          }
        },
        include: {
          video: true
        }
      })

      if (!existingLesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      // Prepare update data (exclude fields that don't belong in the lesson table)
      const { hasQuiz, quizId, videoUrl, ...updateFields } = validatedBody
      const updateData: any = {
        ...updateFields,
        ...(validatedBody.attachments !== undefined && { attachments: validatedBody.attachments })
      }

      // Handle video updates
      if (validatedBody.type === 'VIDEO' && videoUrl) {
        if (existingLesson.video) {
          // Update existing video
          updateData.video = {
            update: {
              url: videoUrl,
              duration: validatedBody.duration || existingLesson.video.duration
            }
          }
        } else {
          // Create new video
          updateData.video = {
            create: {
              url: videoUrl,
              duration: validatedBody.duration || 0
            }
          }
        }
      } else if (validatedBody.type !== 'VIDEO' && existingLesson.video) {
        // Delete video if changing from VIDEO to another type
        updateData.video = {
          delete: true
        }
      }

      // Remove videoUrl from update data as it's handled via video relation
      delete updateData.videoUrl

      // Update lesson
      const lesson = await prisma.courseLesson.update({
        where: { id: lessonId },
        data: updateData,
        include: {
          video: true
        }
      })

      return APIResponse.success({
        message: 'Lesson updated successfully',
        lesson
      })
    } catch (error) {
      console.error('Error updating lesson:', error)
      return APIResponse.error('Failed to update lesson', 500)
    }
  }
)

// DELETE /api/admin/courses/[id]/sections/[sectionId]/chapters/[chapterId]/lessons/[lessonId] - Delete lesson
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string
      const chapterId = resolvedParams?.chapterId as string
      const lessonId = resolvedParams?.lessonId as string

      if (!courseId || !sectionId || !chapterId || !lessonId) {
        return APIResponse.error('All IDs are required', 400)
      }

      // Verify lesson exists
      const existingLesson = await prisma.courseLesson.findFirst({
        where: {
          id: lessonId,
          chapterId: chapterId,
          chapter: {
            sectionId: sectionId,
            section: {
              courseId: courseId
            }
          }
        }
      })

      if (!existingLesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      // Delete lesson (this will cascade delete video)
      await prisma.courseLesson.delete({
        where: { id: lessonId }
      })

      return APIResponse.success({
        message: 'Lesson deleted successfully'
      })
    } catch (error) {
      console.error('Error deleting lesson:', error)
      return APIResponse.error('Failed to delete lesson', 500)
    }
  }
)
