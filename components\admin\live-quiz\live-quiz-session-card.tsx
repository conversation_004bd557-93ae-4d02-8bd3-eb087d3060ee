"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { 
  Play, 
  Pause, 
  Square, 
  Users, 
  Clock, 
  Calendar,
  Monitor,
  Edit,
  Trash2,
  MoreVertical,
  Eye,
  Settings
} from "lucide-react"
import { motion } from "framer-motion"
import { formatDistanceToNow, format } from "date-fns"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface LiveQuizSession {
  id: string
  title: string
  description?: string
  status: 'WAITING' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED'
  maxParticipants?: number
  currentQuestion: number
  questionTimeLimit?: number
  autoAdvance: boolean
  showLeaderboard: boolean
  allowLateJoin: boolean
  startTime?: string
  endTime?: string
  scheduledStart?: string
  createdAt: string
  quiz: {
    id: string
    title: string
    description?: string
    difficulty: string
    timeLimit?: number
    questionCount: number
  }
  creator: {
    id: string
    name: string
  }
  participantCount: number
}

interface LiveQuizSessionCardProps {
  session: LiveQuizSession
  onAction: (sessionId: string, action: 'start' | 'stop' | 'pause' | 'resume') => void
  onDelete: (sessionId: string) => void
  onMonitor: (session: LiveQuizSession) => void
  onEdit?: (session: LiveQuizSession) => void
}

export function LiveQuizSessionCard({ 
  session, 
  onAction, 
  onDelete, 
  onMonitor,
  onEdit 
}: LiveQuizSessionCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'WAITING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'ACTIVE': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'PAUSED': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'CANCELLED': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'WAITING': return <Clock className="h-3 w-3" />
      case 'ACTIVE': return <Play className="h-3 w-3" />
      case 'PAUSED': return <Pause className="h-3 w-3" />
      case 'COMPLETED': return <Square className="h-3 w-3" />
      case 'CANCELLED': return <Square className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  const canStart = session.status === 'WAITING'
  const canPause = session.status === 'ACTIVE'
  const canResume = session.status === 'PAUSED'
  const canStop = session.status === 'ACTIVE' || session.status === 'PAUSED'
  const canDelete = session.status !== 'ACTIVE'
  const canEdit = session.status === 'WAITING'

  const getProgress = () => {
    if (session.quiz.questionCount === 0) return 0
    return Math.round(((session.currentQuestion + 1) / session.quiz.questionCount) * 100)
  }

  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full glass border-2 hover:border-primary/20 transition-all duration-200">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate">
                {session.title}
              </CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {session.quiz.title}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2 ml-2">
              <Badge className={`${getStatusColor(session.status)} text-xs`}>
                {getStatusIcon(session.status)}
                <span className="ml-1">{session.status}</span>
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onMonitor(session)}>
                    <Monitor className="h-4 w-4 mr-2" />
                    Monitor
                  </DropdownMenuItem>
                  {onEdit && canEdit && (
                    <DropdownMenuItem onClick={() => onEdit(session)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  {canDelete && (
                    <ConfirmationDialog
                      trigger={
                        <DropdownMenuItem 
                          className="text-destructive focus:text-destructive"
                          onSelect={(e) => e.preventDefault()}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      }
                      title="Delete Live Quiz Session"
                      description="Are you sure you want to delete this live quiz session? This action cannot be undone."
                      confirmText="Delete"
                      variant="destructive"
                      onConfirm={() => onDelete(session.id)}
                    />
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Session Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>{session.participantCount}</span>
              {session.maxParticipants && (
                <span className="text-muted-foreground">/ {session.maxParticipants}</span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <span>{session.quiz.questionCount} questions</span>
            </div>
          </div>

          {/* Progress */}
          {session.status === 'ACTIVE' && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{session.currentQuestion + 1} / {session.quiz.questionCount}</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <motion.div
                  className="bg-primary h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgress()}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          )}

          {/* Timing Info */}
          <div className="text-xs text-muted-foreground space-y-1">
            {session.scheduledStart && (
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3" />
                <span>Scheduled: {format(new Date(session.scheduledStart), 'MMM d, HH:mm')}</span>
              </div>
            )}
            {session.startTime && (
              <div className="flex items-center gap-2">
                <Play className="h-3 w-3" />
                <span>Started: {formatDistanceToNow(new Date(session.startTime), { addSuffix: true })}</span>
              </div>
            )}
            {session.endTime && (
              <div className="flex items-center gap-2">
                <Square className="h-3 w-3" />
                <span>Ended: {formatDistanceToNow(new Date(session.endTime), { addSuffix: true })}</span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            {canStart && (
              <Button
                size="sm"
                onClick={() => onAction(session.id, 'start')}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-2" />
                Start
              </Button>
            )}
            
            {canPause && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onAction(session.id, 'pause')}
                className="flex-1"
              >
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
            )}
            
            {canResume && (
              <Button
                size="sm"
                onClick={() => onAction(session.id, 'resume')}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-2" />
                Resume
              </Button>
            )}
            
            {canStop && (
              <ConfirmationDialog
                trigger={
                  <Button
                    size="sm"
                    variant="destructive"
                    className="flex-1"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Stop
                  </Button>
                }
                title="Stop Live Quiz Session"
                description="Are you sure you want to stop this live quiz session? This will end the quiz for all participants."
                confirmText="Stop Session"
                variant="destructive"
                onConfirm={() => onAction(session.id, 'stop')}
              />
            )}
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => onMonitor(session)}
            >
              <Monitor className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
