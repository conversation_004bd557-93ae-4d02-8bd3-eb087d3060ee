import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { MissionNotificationService } from '@/lib/mission-notifications'
import { z } from 'zod'

const markReadSchema = z.object({
  notificationIds: z.array(z.string())
})

// GET /api/student/notifications - Get user's notifications
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get unread notifications
      const unreadNotifications = await MissionNotificationService.getUnreadNotifications(user.id)
      
      return APIResponse.success({
        notifications: unreadNotifications,
        unreadCount: unreadNotifications.length
      }, 'Notifications retrieved successfully')

    } catch (error) {
      console.error('Error fetching notifications:', error)
      return APIResponse.internalServerError('Failed to fetch notifications')
    }
  }
)

// POST /api/student/notifications/mark-read - Mark notifications as read
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: markReadSchema
  },
  async (request: NextRequest, { user, validatedBody }) => {
    try {
      const { notificationIds } = validatedBody
      
      // Mark notifications as read
      await MissionNotificationService.markNotificationsAsRead(user.id, notificationIds)
      
      return APIResponse.success({
        markedCount: notificationIds.length
      }, `${notificationIds.length} notification(s) marked as read`)

    } catch (error) {
      console.error('Error marking notifications as read:', error)
      return APIResponse.internalServerError('Failed to mark notifications as read')
    }
  }
)
