"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { FloatingThemeToggle } from "@/components/ui/theme-toggle"
import { LayoutDashboard } from "lucide-react"

export function LandingHeader() {
  const { data: session } = useSession()
  const router = useRouter()

  const handleDashboardClick = () => {
    if (session?.user.role === 'ADMIN') {
      router.push('/admin')
    } else {
      router.push('/student')
    }
  }

  if (!session) {
    return (
      <div
        className="fixed top-3 inset-x-0 px-3 sm:top-5 sm:inset-auto sm:right-5 md:top-6 md:right-8 z-50 flex justify-end"
        style={{ paddingRight: 'env(safe-area-inset-right)', paddingTop: 'env(safe-area-inset-top)' }}
      >
        <FloatingThemeToggle />
      </div>
    )
  }

  return (
    <div
      className="fixed top-3 inset-x-0 px-3 sm:top-5 sm:inset-auto sm:right-5 md:top-6 md:right-16 z-50 flex items-center justify-end gap-2 sm:gap-3"
      style={{ paddingRight: 'env(safe-area-inset-right)', paddingTop: 'env(safe-area-inset-top)' }}
    >
      <Button
        onClick={handleDashboardClick}
        variant="outline"
        size="sm"
        className="glass bg-white/10 dark:bg-gray-900/10 backdrop-blur-sm border-white/20 dark:border-gray-800/20 hover:bg-white/20 dark:hover:bg-gray-800/20 transition-all duration-300 shadow-lg"
      >
        <LayoutDashboard className="w-4 h-4 mr-2 hidden xs:inline sm:inline" />
        <span className="hidden sm:inline">Dashboard</span>
        <span className="sm:hidden">Dash</span>
      </Button>
      <FloatingThemeToggle />
    </div>
  )
}
