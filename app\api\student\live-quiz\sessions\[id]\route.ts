import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/live-quiz/sessions/[id] - Get specific live quiz session details for student
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session details
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              description: true,
              difficulty: true,
              timeLimit: true,
              category: true,
              thumbnail: true,
              tags: true,
              instructions: true,
              isPublished: true,
              questions: {
                select: { id: true, order: true },
                orderBy: { order: 'asc' }
              }
            }
          },
          creator: {
            select: {
              id: true,
              name: true
            }
          },
          participants: {
            where: { isActive: true },
            select: {
              id: true,
              userId: true,
              score: true,
              rank: true,
              user: {
                select: {
                  name: true
                }
              }
            },
            orderBy: [
              { rank: 'asc' },
              { score: 'desc' }
            ]
          },
          _count: {
            select: {
              participants: {
                where: { isActive: true }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Check if quiz is published
      if (!session.quiz.isPublished) {
        return APIResponse.error('Quiz is not available', 403, 'QUIZ_NOT_PUBLISHED')
      }

      // Check if user is participating
      const userParticipation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId,
            userId: user.id
          }
        },
        select: {
          id: true,
          joinedAt: true,
          leftAt: true,
          currentQuestion: true,
          score: true,
          correctAnswers: true,
          totalAnswered: true,
          isActive: true,
          rank: true,
          answers: true,
          timeSpent: true
        }
      })

      const isParticipating = userParticipation?.isActive || false
      const participantCount = session._count.participants

      // Determine if user can join
      const canJoin = !isParticipating && 
                     (session.status === 'WAITING' || 
                      (session.status === 'ACTIVE' && session.allowLateJoin)) &&
                     (!session.maxParticipants || participantCount < session.maxParticipants)

      // Get current question if session is active and user is participating
      let currentQuestionData = null
      if (session.status === 'ACTIVE' && isParticipating) {
        const currentQuestion = await prisma.question.findFirst({
          where: {
            quizId: session.quizId,
            order: session.currentQuestion
          },
          select: {
            id: true,
            type: true,
            text: true,
            options: true,
            points: true,
            order: true,
            image: true
            // Note: Don't include correctAnswer or explanation for students during active quiz
          }
        })
        currentQuestionData = currentQuestion
      }

      // Calculate leaderboard (top 10)
      const leaderboard = session.participants
        .filter(p => p.rank !== null)
        .slice(0, 10)
        .map(p => ({
          userId: p.userId,
          userName: p.user.name,
          score: p.score,
          rank: p.rank
        }))

      // Calculate session statistics
      const stats = {
        totalParticipants: participantCount,
        averageScore: participantCount > 0 
          ? Math.round(session.participants.reduce((sum, p) => sum + p.score, 0) / participantCount)
          : 0,
        questionCount: session.quiz.questions.length,
        progress: session.quiz.questions.length > 0 
          ? Math.round(((session.currentQuestion + 1) / session.quiz.questions.length) * 100)
          : 0
      }

      return APIResponse.success({
        session: {
          id: session.id,
          title: session.title,
          description: session.description,
          status: session.status,
          maxParticipants: session.maxParticipants,
          currentQuestion: session.currentQuestion,
          questionTimeLimit: session.questionTimeLimit,
          autoAdvance: session.autoAdvance,
          showLeaderboard: session.showLeaderboard,
          allowLateJoin: session.allowLateJoin,
          startTime: session.startTime,
          endTime: session.endTime,
          scheduledStart: session.scheduledStart,
          createdAt: session.createdAt
        },
        quiz: {
          ...session.quiz,
          questionCount: session.quiz.questions.length
        },
        creator: session.creator,
        userParticipation,
        isParticipating,
        canJoin,
        participantCount,
        currentQuestionData,
        leaderboard: session.showLeaderboard ? leaderboard : [],
        stats
      }, 'Live quiz session details retrieved successfully')

    } catch (error) {
      console.error('Error fetching live quiz session details:', error)
      return APIResponse.error('Failed to fetch live quiz session details', 500)
    }
  }
)
