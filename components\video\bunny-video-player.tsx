'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  Cog6ToothIcon,
  ForwardIcon,
  BackwardIcon
} from '@heroicons/react/24/outline'

interface VideoQuality {
  label: string
  height: number
  bitrate: number
  url: string
}

interface BunnyVideoPlayerProps {
  videoUrl: string
  posterUrl?: string
  title?: string
  qualities?: VideoQuality[]
  onTimeUpdate?: (currentTime: number, duration: number) => void
  onProgress?: (watchTime: number, percentage: number) => void
  onComplete?: () => void
  startTime?: number
  autoplay?: boolean
  className?: string
}

export default function BunnyVideoPlayer({
  videoUrl,
  posterUrl,
  title,
  qualities = [],
  onTimeUpdate,
  onProgress,
  onComplete,
  startTime = 0,
  autoplay = false,
  className = ''
}: BunnyVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const progressRef = useRef<HTMLDivElement>(null)
  
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  const [currentQuality, setCurrentQuality] = useState<VideoQuality | null>(null)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [isBuffering, setIsBuffering] = useState(false)
  const [watchTime, setWatchTime] = useState(0)

  const playbackSpeeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2]

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    // Set initial quality if available
    if (qualities.length > 0 && !currentQuality) {
      // Default to 720p or highest available quality
      const defaultQuality = qualities.find(q => q.height === 720) || qualities[qualities.length - 1]
      setCurrentQuality(defaultQuality)
    }

    // Set start time
    if (startTime > 0) {
      video.currentTime = startTime
    }

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      if (startTime > 0) {
        video.currentTime = startTime
      }
    }

    const handleTimeUpdate = () => {
      const current = video.currentTime
      setCurrentTime(current)
      
      // Track watch time
      setWatchTime(prev => prev + 0.1)
      
      // Call callbacks
      onTimeUpdate?.(current, video.duration)
      
      if (video.duration > 0) {
        const percentage = (current / video.duration) * 100
        onProgress?.(Math.floor(current), percentage)
        
        // Mark as complete when 90% watched
        if (percentage >= 90) {
          onComplete?.()
        }
      }
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleWaiting = () => setIsBuffering(true)
    const handleCanPlay = () => setIsBuffering(false)

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('waiting', handleWaiting)
    video.addEventListener('canplay', handleCanPlay)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('waiting', handleWaiting)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [startTime, onTimeUpdate, onProgress, onComplete, qualities, currentQuality])

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current
    const progressBar = progressRef.current
    if (!video || !progressBar) return

    const rect = progressBar.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * duration
    
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const skip = (seconds: number) => {
    const video = videoRef.current
    if (!video) return

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    const video = videoRef.current
    if (!video) return

    setVolume(newVolume)
    video.volume = newVolume
    setIsMuted(newVolume === 0)
  }

  const toggleFullscreen = () => {
    const container = containerRef.current
    if (!container) return

    if (!isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
  }

  const changeQuality = (quality: VideoQuality) => {
    const video = videoRef.current
    if (!video) return

    const currentTime = video.currentTime
    const wasPlaying = !video.paused

    setCurrentQuality(quality)
    video.src = quality.url
    video.currentTime = currentTime

    if (wasPlaying) {
      video.play()
    }
  }

  const changePlaybackSpeed = (speed: number) => {
    const video = videoRef.current
    if (!video) return

    setPlaybackSpeed(speed)
    video.playbackRate = speed
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // Auto-hide controls
  useEffect(() => {
    let timeout: NodeJS.Timeout

    const resetTimeout = () => {
      clearTimeout(timeout)
      setShowControls(true)
      timeout = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false)
        }
      }, 3000)
    }

    const container = containerRef.current
    if (container) {
      container.addEventListener('mousemove', resetTimeout)
      container.addEventListener('mouseenter', resetTimeout)
      container.addEventListener('mouseleave', () => {
        if (isPlaying) {
          setShowControls(false)
        }
      })
    }

    return () => {
      clearTimeout(timeout)
      if (container) {
        container.removeEventListener('mousemove', resetTimeout)
        container.removeEventListener('mouseenter', resetTimeout)
        container.removeEventListener('mouseleave', () => {})
      }
    }
  }, [isPlaying])

  return (
    <div
      ref={containerRef}
      className={`relative bg-black rounded-xl overflow-hidden group ${className}`}
      style={{ aspectRatio: '16/9' }}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        src={currentQuality?.url || videoUrl}
        poster={posterUrl}
        className="w-full h-full object-contain"
        autoPlay={autoplay}
        playsInline
        preload="metadata"
      />

      {/* Loading Spinner */}
      <AnimatePresence>
        {isBuffering && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-black/20"
          >
            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Play Button Overlay */}
      <AnimatePresence>
        {!isPlaying && !isBuffering && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            onClick={togglePlay}
            className="absolute inset-0 flex items-center justify-center bg-black/20 hover:bg-black/30 transition-colors"
          >
            <div className="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center shadow-2xl">
              <PlayIcon className="w-8 h-8 text-black ml-1" />
            </div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Video Controls */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4"
          >
            {/* Progress Bar */}
            <div className="mb-4">
              <div
                ref={progressRef}
                onClick={handleSeek}
                className="w-full h-1 bg-white/30 rounded-full cursor-pointer hover:h-2 transition-all duration-200"
              >
                <div
                  className="h-full bg-red-500 rounded-full relative"
                  style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                >
                  <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Play/Pause */}
                <button
                  onClick={togglePlay}
                  className="text-white hover:text-red-400 transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="w-6 h-6" />
                  ) : (
                    <PlayIcon className="w-6 h-6" />
                  )}
                </button>

                {/* Skip Backward */}
                <button
                  onClick={() => skip(-10)}
                  className="text-white hover:text-red-400 transition-colors"
                >
                  <BackwardIcon className="w-5 h-5" />
                </button>

                {/* Skip Forward */}
                <button
                  onClick={() => skip(10)}
                  className="text-white hover:text-red-400 transition-colors"
                >
                  <ForwardIcon className="w-5 h-5" />
                </button>

                {/* Volume */}
                <div className="flex items-center space-x-2 group/volume">
                  <button
                    onClick={toggleMute}
                    className="text-white hover:text-red-400 transition-colors"
                  >
                    {isMuted || volume === 0 ? (
                      <SpeakerXMarkIcon className="w-5 h-5" />
                    ) : (
                      <SpeakerWaveIcon className="w-5 h-5" />
                    )}
                  </button>
                  <div className="w-0 group-hover/volume:w-20 overflow-hidden transition-all duration-300">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                      className="w-20 h-1 bg-white/30 rounded-full appearance-none cursor-pointer"
                    />
                  </div>
                </div>

                {/* Time Display */}
                <div className="text-white text-sm font-medium">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Settings */}
                <div className="relative">
                  <button
                    onClick={() => setShowSettings(!showSettings)}
                    className="text-white hover:text-red-400 transition-colors"
                  >
                    <Cog6ToothIcon className="w-5 h-5" />
                  </button>

                  {/* Settings Menu */}
                  <AnimatePresence>
                    {showSettings && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute bottom-full right-0 mb-2 bg-black/90 rounded-lg p-4 min-w-48"
                      >
                        {/* Quality Selection */}
                        {qualities.length > 0 && (
                          <div className="mb-4">
                            <div className="text-white text-sm font-medium mb-2">Quality</div>
                            <div className="space-y-1">
                              {qualities.map((quality) => (
                                <button
                                  key={quality.height}
                                  onClick={() => changeQuality(quality)}
                                  className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                                    currentQuality?.height === quality.height
                                      ? 'bg-red-500 text-white'
                                      : 'text-white/80 hover:bg-white/10'
                                  }`}
                                >
                                  {quality.label}
                                </button>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Playback Speed */}
                        <div>
                          <div className="text-white text-sm font-medium mb-2">Speed</div>
                          <div className="space-y-1">
                            {playbackSpeeds.map((speed) => (
                              <button
                                key={speed}
                                onClick={() => changePlaybackSpeed(speed)}
                                className={`block w-full text-left px-2 py-1 text-sm rounded transition-colors ${
                                  playbackSpeed === speed
                                    ? 'bg-red-500 text-white'
                                    : 'text-white/80 hover:bg-white/10'
                                }`}
                              >
                                {speed}x
                              </button>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Fullscreen */}
                <button
                  onClick={toggleFullscreen}
                  className="text-white hover:text-red-400 transition-colors"
                >
                  {isFullscreen ? (
                    <ArrowsPointingInIcon className="w-5 h-5" />
                  ) : (
                    <ArrowsPointingOutIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
