'use client'

import { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface PaymentStatusProps {
  status: 'created' | 'paid' | 'failed' | 'cancelled'
  className?: string
  showIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const statusConfig = {
  created: {
    label: 'Pending',
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    iconColor: 'text-yellow-600'
  },
  paid: {
    label: 'Paid',
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800 border-green-200',
    iconColor: 'text-green-600'
  },
  failed: {
    label: 'Failed',
    icon: XCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    iconColor: 'text-red-600'
  },
  cancelled: {
    label: 'Cancelled',
    icon: AlertCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    iconColor: 'text-gray-600'
  }
}

export function PaymentStatus({ status, className, showIcon = true, size = 'md' }: PaymentStatusProps) {
  const config = statusConfig[status]
  const Icon = config.icon

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-2.5 py-1.5',
    lg: 'text-base px-3 py-2'
  }

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  }

  return (
    <Badge
      variant="outline"
      className={cn(
        config.color,
        sizeClasses[size],
        'font-medium border',
        className
      )}
    >
      <div className="flex items-center gap-1.5">
        {showIcon && (
          <Icon className={cn(iconSizes[size], config.iconColor)} />
        )}
        {config.label}
      </div>
    </Badge>
  )
}
