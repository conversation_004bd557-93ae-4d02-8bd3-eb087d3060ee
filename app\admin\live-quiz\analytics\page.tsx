"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  Target,
  Trophy,
  Activity,
  Calendar,
  RefreshCw,
  Download,
  Filter
} from "lucide-react"
import { motion } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { MinimalLayout } from "@/components/layouts/minimal-layout"
import { LiveQuizAnalyticsCharts } from "@/components/admin/live-quiz/analytics-charts"
import { LiveQuizAnalyticsTable } from "@/components/admin/live-quiz/analytics-table"

interface AnalyticsData {
  overview: {
    totalSessions: number
    totalParticipants: number
    totalActiveParticipants: number
    averageParticipantsPerSession: number
    averageScore: number
    averageAccuracy: number
    averageTimeSpent: number
    averageQuestionsPerSession: number
  }
  sessionsByStatus: Record<string, number>
  topSessions: Array<{
    id: string
    title: string
    quiz: {
      title: string
      difficulty: string
    }
    creator: {
      name: string
    }
    participantCount: number
    averageScore: number
    completionRate: number
    createdAt: string
  }>
  dailyStats: Array<{
    date: string
    sessions: number
    completed_sessions: number
  }>
  difficultyDistribution: Record<string, number>
  averageScoresByDifficulty: Record<string, number>
  topCreators: Array<{
    creator: {
      name: string
      email: string
    }
    sessionCount: number
  }>
  timeframe: string
  generatedAt: string
}

export default function LiveQuizAnalytics() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState('week')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchAnalytics()
  }, [timeframe])

  const fetchAnalytics = async () => {
    setLoading(true)
    setRefreshing(true)

    try {
      const response = await fetch(`/api/admin/live-quiz/analytics?timeframe=${timeframe}`)
      const data = await response.json()

      if (data.success) {
        setAnalyticsData(data.data)
      } else {
        throw new Error(data.message || 'Failed to fetch analytics')
      }
    } catch (error) {
      console.error('Error fetching analytics:', error)
      toast.error('Failed to fetch analytics data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const exportAnalytics = async () => {
    try {
      const response = await fetch(`/api/admin/live-quiz/analytics?timeframe=${timeframe}&export=true`)
      const blob = await response.blob()
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `live-quiz-analytics-${timeframe}-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success('Analytics exported successfully')
    } catch (error) {
      console.error('Error exporting analytics:', error)
      toast.error('Failed to export analytics')
    }
  }

  if (loading && !analyticsData) {
    return (
      <MinimalLayout title="Live Quiz Analytics" userRole="ADMIN">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading analytics...</p>
          </div>
        </div>
      </MinimalLayout>
    )
  }

  return (
    <MinimalLayout title="Live Quiz Analytics" userRole="ADMIN">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Live Quiz Analytics</h1>
            <p className="text-muted-foreground">Comprehensive insights into live quiz performance</p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-[140px]">
                <Calendar className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAnalytics}
              disabled={refreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={exportAnalytics}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {analyticsData && (
          <>
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="glass">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{analyticsData.overview.totalSessions}</div>
                        <p className="text-sm text-muted-foreground">Total Sessions</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="glass">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                        <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{analyticsData.overview.totalParticipants}</div>
                        <p className="text-sm text-muted-foreground">Total Participants</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="glass">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                        <Target className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{analyticsData.overview.averageScore}</div>
                        <p className="text-sm text-muted-foreground">Average Score</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="glass">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                        <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <div>
                        <div className="text-2xl font-bold">{analyticsData.overview.averageAccuracy}%</div>
                        <p className="text-sm text-muted-foreground">Average Accuracy</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="glass">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <Activity className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="text-lg font-semibold">{analyticsData.overview.averageParticipantsPerSession}</div>
                      <p className="text-sm text-muted-foreground">Avg Participants/Session</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="text-lg font-semibold">{Math.round(analyticsData.overview.averageTimeSpent / 60)}m</div>
                      <p className="text-sm text-muted-foreground">Avg Time Spent</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <Trophy className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="text-lg font-semibold">{analyticsData.overview.averageQuestionsPerSession}</div>
                      <p className="text-sm text-muted-foreground">Avg Questions/Session</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts and Tables */}
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="sessions">Sessions</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="creators">Creators</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <LiveQuizAnalyticsCharts
                  dailyStats={analyticsData.dailyStats}
                  sessionsByStatus={analyticsData.sessionsByStatus}
                  difficultyDistribution={analyticsData.difficultyDistribution}
                  averageScoresByDifficulty={analyticsData.averageScoresByDifficulty}
                />
              </TabsContent>

              <TabsContent value="sessions" className="space-y-6">
                <LiveQuizAnalyticsTable
                  title="Top Performing Sessions"
                  data={analyticsData.topSessions}
                  type="sessions"
                />
              </TabsContent>

              <TabsContent value="performance" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="glass">
                    <CardHeader>
                      <CardTitle className="text-base">Performance by Difficulty</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(analyticsData.averageScoresByDifficulty).map(([difficulty, score]) => (
                          <div key={difficulty} className="flex items-center justify-between">
                            <Badge variant="outline">{difficulty}</Badge>
                            <span className="font-medium">{score} avg score</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="glass">
                    <CardHeader>
                      <CardTitle className="text-base">Session Status Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(analyticsData.sessionsByStatus).map(([status, count]) => (
                          <div key={status} className="flex items-center justify-between">
                            <Badge variant="outline">{status}</Badge>
                            <span className="font-medium">{count} sessions</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="creators" className="space-y-6">
                <LiveQuizAnalyticsTable
                  title="Most Active Creators"
                  data={analyticsData.topCreators}
                  type="creators"
                />
              </TabsContent>
            </Tabs>

            {/* Footer */}
            <div className="text-center text-sm text-muted-foreground">
              Last updated: {new Date(analyticsData.generatedAt).toLocaleString()}
            </div>
          </>
        )}
      </div>
    </MinimalLayout>
  )
}
