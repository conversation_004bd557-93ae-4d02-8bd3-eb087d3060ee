import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/debug/quiz-bundles-test - Test quiz bundles API
export const GET = createAPIHandler(
  {
    requireAuth: false
  },
  async (request: NextRequest, { user }) => {
    try {
      const { searchParams } = new URL(request.url)
      
      const debugInfo: any = {
        timestamp: new Date().toISOString(),
        user: user ? { id: user.id, role: user.role, email: user.email } : null,
        searchParams: Object.fromEntries(searchParams.entries())
      }

      // Test database connection
      try {
        debugInfo.database = {
          connected: true,
          totalBundles: await prisma.quizBundle.count(),
          publishedBundles: await prisma.quizBundle.count({ where: { isPublished: true } }),
          activeBundles: await prisma.quizBundle.count({ where: { isActive: true } })
        }
      } catch (dbError) {
        debugInfo.database = {
          connected: false,
          error: dbError instanceof Error ? dbError.message : 'Unknown database error'
        }
      }

      // Test quiz bundles query
      try {
        const sampleBundles = await prisma.quizBundle.findMany({
          take: 3,
          include: {
            items: {
              include: {
                quiz: {
                  select: {
                    id: true,
                    title: true,
                    type: true,
                    difficulty: true
                  }
                }
              }
            },
            _count: {
              select: {
                purchases: {
                  where: { status: 'active' }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        })

        debugInfo.sampleBundles = sampleBundles.map(bundle => ({
          id: bundle.id,
          title: bundle.title,
          slug: bundle.slug,
          price: bundle.price,
          isActive: bundle.isActive,
          isPublished: bundle.isPublished,
          quizCount: bundle.items?.length || 0,
          purchaseCount: bundle._count?.purchases || 0,
          createdAt: bundle.createdAt
        }))
      } catch (queryError) {
        debugInfo.sampleBundles = {
          error: queryError instanceof Error ? queryError.message : 'Unknown query error'
        }
      }

      // Test the actual API call
      try {
        const testParams = new URLSearchParams({
          published: 'false',
          limit: '10'
        })
        
        const apiUrl = `${request.nextUrl.origin}/api/quiz-bundles?${testParams}`
        debugInfo.apiTest = {
          url: apiUrl,
          params: Object.fromEntries(testParams.entries())
        }

        // Don't actually make the API call to avoid infinite loop
        // Just show what would be called
        debugInfo.apiTest.note = 'API call not executed to avoid infinite loop'
      } catch (apiError) {
        debugInfo.apiTest = {
          error: apiError instanceof Error ? apiError.message : 'Unknown API error'
        }
      }

      // Test parameter parsing
      const publishedParam = searchParams.get('published')
      debugInfo.parameterParsing = {
        publishedParam,
        publishedType: typeof publishedParam,
        publishedBoolean: publishedParam === 'true' || publishedParam === '1',
        publishedFalse: publishedParam === 'false' || publishedParam === '0'
      }

      return APIResponse.success({
        message: 'Quiz bundles debug test completed',
        debug: debugInfo
      })

    } catch (error) {
      console.error('Debug test error:', error)
      return APIResponse.error('Debug test failed', 500)
    }
  }
)
