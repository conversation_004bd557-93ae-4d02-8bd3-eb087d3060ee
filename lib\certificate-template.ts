interface CertificateData {
  certificateId: string
  studentName: string
  courseName: string
  instructorName: string
  completionDate: Date
  issuedDate: Date
  finalScore: number | null
}

export function generateModernCertificateHTML(data: CertificateData): string {
  const completionDateFormatted = data.completionDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const issuedDateFormatted = data.issuedDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700;900&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }

        .certificate-container {
            background: white;
            width: 1200px;
            height: 850px;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 80px 60px;
        }

        .certificate-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .certificate-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .decorative-border {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 30px;
            bottom: 30px;
            border: 2px solid #e5e7eb;
            border-radius: 16px;
            pointer-events: none;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .logo-section {
            margin-bottom: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .logo-text {
            color: white;
            font-size: 32px;
            font-weight: 900;
            font-family: 'Playfair Display', serif;
        }

        .institution-name {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .institution-subtitle {
            font-size: 16px;
            color: #6b7280;
            font-weight: 400;
        }

        .certificate-title {
            font-family: 'Playfair Display', serif;
            font-size: 56px;
            font-weight: 900;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .certificate-subtitle {
            font-size: 20px;
            color: #6b7280;
            font-weight: 400;
            margin-bottom: 50px;
        }

        .recipient-section {
            text-align: center;
            margin-bottom: 50px;
        }

        .recipient-intro {
            font-size: 22px;
            color: #374151;
            margin-bottom: 30px;
            font-weight: 400;
        }

        .recipient-name {
            font-family: 'Playfair Display', serif;
            font-size: 48px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 30px;
            position: relative;
            display: inline-block;
        }

        .recipient-name::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .course-section {
            text-align: center;
            margin-bottom: 60px;
        }

        .course-intro {
            font-size: 20px;
            color: #374151;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .course-name {
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
            margin-bottom: 40px;
            font-style: italic;
        }

        .details-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 800px;
            margin-bottom: 60px;
        }

        .detail-item {
            text-align: center;
            flex: 1;
        }

        .detail-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .detail-value {
            font-size: 18px;
            color: #1f2937;
            font-weight: 600;
        }

        .signature-section {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: auto;
        }

        .signature-item {
            text-align: center;
            margin: 0 40px;
        }

        .signature-line {
            width: 200px;
            height: 2px;
            background: #d1d5db;
            margin-bottom: 12px;
        }

        .signature-name {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .signature-title {
            font-size: 14px;
            color: #6b7280;
            font-weight: 400;
        }

        .certificate-id {
            position: absolute;
            bottom: 30px;
            right: 30px;
            font-size: 12px;
            color: #9ca3af;
            font-weight: 400;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            color: rgba(102, 126, 234, 0.03);
            font-weight: 900;
            font-family: 'Playfair Display', serif;
            pointer-events: none;
            z-index: 0;
        }

        .content {
            position: relative;
            z-index: 1;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .certificate-container {
                box-shadow: none;
                border: 1px solid #e5e7eb;
            }
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <div class="decorative-border"></div>
        <div class="watermark">CERTIFIED</div>
        
        <div class="content">
            <div class="header">
                <div class="logo-section">
                    <div class="logo">
                        <div class="logo-text">P</div>
                    </div>
                    <div class="institution-name">PrepLocus</div>
                    <div class="institution-subtitle">India's #1 Online Exam Coaching Platform</div>
                </div>
                
                <h1 class="certificate-title">Certificate of Completion</h1>
                <p class="certificate-subtitle">This is to certify that</p>
            </div>

            <div class="recipient-section">
                <div class="recipient-name">${data.studentName}</div>
                <p class="course-intro">has successfully completed the course</p>
                <h2 class="course-name">"${data.courseName}"</h2>
            </div>

            <div class="details-section">
                <div class="detail-item">
                    <div class="detail-label">Completion Date</div>
                    <div class="detail-value">${completionDateFormatted}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Instructor</div>
                    <div class="detail-value">${data.instructorName}</div>
                </div>
                ${data.finalScore ? `
                <div class="detail-item">
                    <div class="detail-label">Final Score</div>
                    <div class="detail-value">${data.finalScore}%</div>
                </div>
                ` : ''}
            </div>

            <div class="signature-section">
                <div class="signature-item">
                    <div class="signature-line"></div>
                    <div class="signature-name">${data.instructorName}</div>
                    <div class="signature-title">Course Instructor</div>
                </div>
            </div>
        </div>

        <div class="certificate-id">
            Certificate ID: ${data.certificateId} | Issued: ${issuedDateFormatted}
        </div>
    </div>
</body>
</html>
  `
}
