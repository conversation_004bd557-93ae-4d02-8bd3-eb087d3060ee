import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createChapterSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  isPublished: z.boolean().default(false)
})

// POST /api/admin/courses/[id]/sections/[sectionId]/chapters - Create chapter
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createChapterSchema
  },
  async (request: NextRequest, { params, validatedBody, user }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Verify course and section exist
      const section = await prisma.courseSection.findFirst({
        where: {
          id: sectionId,
          courseId: courseId
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Create chapter
      const chapter = await prisma.courseChapter.create({
        data: {
          ...validatedBody,
          sectionId: sectionId
        },
        include: {
          lessons: {
             orderBy: { updatedAt: 'asc' },
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              isPublished: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Chapter created successfully',
        chapter
      })
    } catch (error) {
      console.error('Error creating chapter:', error)
      return APIResponse.error('Failed to create chapter', 500)
    }
  }
)

// GET /api/admin/courses/[id]/sections/[sectionId]/chapters - Get chapters
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { params }) => {
    try {
      const resolvedParams = await params
      const courseId = resolvedParams?.id as string
      const sectionId = resolvedParams?.sectionId as string

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      const chapters = await prisma.courseChapter.findMany({
        where: {
          sectionId: sectionId,
          section: {
            courseId: courseId
          }
        },
        orderBy: { createdAt: 'asc' },
        include: {
          lessons: {
            orderBy: { createdAt: 'asc' },
            select: {
              id: true,
              title: true,
              type: true,
              duration: true,
              isPublished: true
            }
          }
        }
      })

      return APIResponse.success({ chapters })
    } catch (error) {
      console.error('Error fetching chapters:', error)
      return APIResponse.error('Failed to fetch chapters', 500)
    }
  }
)
