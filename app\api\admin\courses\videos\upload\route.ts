import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { courseFileManager } from '@/lib/course-file-manager'
import { getBunnyStorage } from '@/lib/bunny-storage'
import { prisma } from '@/lib/prisma'

// Allowed video formats
const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/ogg',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/mkv'
]

// Maximum video file size (500MB for better quality)
const MAX_VIDEO_SIZE = 500 * 1024 * 1024

// Video quality configurations for Bunny.net
const VIDEO_QUALITIES = [
  { label: '360p', height: 360, bitrate: 800 },
  { label: '480p', height: 480, bitrate: 1200 },
  { label: '720p', height: 720, bitrate: 2500 },
  { label: '1080p', height: 1080, bitrate: 5000 }
]



// POST /api/admin/courses/videos/upload - Upload video for course lesson
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Parse form data
      const formData = await request.formData()
      const file = formData.get('video') as File
      const lessonId = formData.get('lessonId') as string
      const generateThumbnail = formData.get('generateThumbnail') === 'true'

      if (!file) {
        return APIResponse.error('No video file provided', 400)
      }

      if (!lessonId) {
        return APIResponse.error('Lesson ID is required', 400)
      }

      // Validate file type
      if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {
        return APIResponse.error(
          `Invalid file type. Allowed types: ${ALLOWED_VIDEO_TYPES.join(', ')}`,
          400
        )
      }

      // Validate file size
      if (file.size > MAX_VIDEO_SIZE) {
        return APIResponse.error(
          `File size exceeds maximum allowed size of ${MAX_VIDEO_SIZE / (1024 * 1024)}MB`,
          400
        )
      }

      // Verify lesson exists and belongs to a course the user can edit
      const lesson = await prisma.courseLesson.findUnique({
        where: { id: lessonId },
        include: {
          chapter: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      title: true,
                      instructorId: true
                    }
                  }
                }
              }
            }
          },
          video: true // Check if video already exists
        }
      })

      if (!lesson) {
        return APIResponse.error('Lesson not found', 404)
      }

      // Check if user has permission to edit this course
      if (lesson.chapter.section.course.instructorId !== user.id) {
        return APIResponse.error('You do not have permission to edit this course', 403)
      }

      // Upload video to Bunny CDN
      const courseId = lesson.chapter.section.course.id
      const bunnyStorage = getBunnyStorage()

      // Upload original video to Bunny CDN
      const uploadResult = await bunnyStorage.uploadFile(file, {
        folder: `courses/${courseId}/videos`,
        filename: `lesson_${lessonId}_${Date.now()}.${file.name.split('.').pop()}`,
        contentType: file.type,
        optimize: true
      })

      if (!uploadResult.success) {
        return APIResponse.error(
          `Failed to upload video: ${uploadResult.error}`,
          500
        )
      }

      // Get video duration from client or set to null
      const durationParam = formData.get('duration')
      const videoDuration = durationParam ? parseInt(durationParam as string) : null

      // Generate thumbnail URL (Bunny CDN can auto-generate thumbnails)
      const thumbnailUrl = uploadResult.url ? `${uploadResult.url}?thumbnail=1` : null

      // Create video qualities array (for future adaptive streaming)
      const videoQualities = VIDEO_QUALITIES.map(quality => ({
        label: quality.label,
        height: quality.height,
        bitrate: quality.bitrate,
        url: uploadResult.url // For now, use same URL. In production, you'd generate different quality versions
      }))

      // Create or update video record
      let videoRecord
      if (lesson.video) {
        // Update existing video
        videoRecord = await prisma.courseVideo.update({
          where: { id: lesson.video.id },
          data: {
            filename: uploadResult.filename!,
            originalName: file.name,
            url: uploadResult.url!,
            duration: videoDuration,
            size: uploadResult.size!,
            mimeType: file.type,
            thumbnailUrl,
            uploadedAt: new Date()
          }
        })
      } else {
        // Create new video record
        videoRecord = await prisma.courseVideo.create({
          data: {
            lessonId,
            filename: uploadResult.filename!,
            originalName: file.name,
            url: uploadResult.url!,
            duration: videoDuration,
            size: uploadResult.size!,
            mimeType: file.type,
            thumbnailUrl
          }
        })
      }

      // Update lesson duration if video duration is available
      if (videoDuration) {
        await prisma.courseLesson.update({
          where: { id: lessonId },
          data: { duration: videoDuration }
        })
      }

      return APIResponse.success({
        message: 'Video uploaded successfully to course-specific folder',
        video: {
          id: videoRecord.id,
          url: videoRecord.url,
          filename: videoRecord.filename,
          originalName: videoRecord.originalName,
          duration: videoRecord.duration,
          size: videoRecord.size,
          mimeType: videoRecord.mimeType,
          uploadedAt: videoRecord.uploadedAt,
          folder: `courses/${courseId}/videos`
        },
        lesson: {
          id: lesson.id,
          title: lesson.title,
          course: {
            id: lesson.chapter.section.course.id,
            title: lesson.chapter.section.course.title
          }
        }
      })
    } catch (error) {
      console.error('Error uploading video:', error)
      return APIResponse.error('Failed to upload video', 500)
    }
  }
)

// GET /api/admin/courses/videos/upload - Get upload progress (for future implementation)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN'
  },
  async (request: NextRequest, { user }) => {
    // This endpoint could be used to check upload progress
    // For now, return a simple response
    return APIResponse.success({
      message: 'Video upload endpoint is ready',
      maxFileSize: MAX_VIDEO_SIZE,
      allowedTypes: ALLOWED_VIDEO_TYPES
    })
  }
)
