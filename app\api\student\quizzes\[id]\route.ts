import { NextRequest } from 'next/server'
 import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/quizzes/[id] - Get quiz details for students
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
     
  },
  async (request: NextRequest, { user, params }) => {
    const paramsResolved = await params
    const quizId = paramsResolved?.id as string

    if (!quizId) {
      return APIResponse.error('Quiz ID is required', 400)
    }

    try {
      // Get quiz details with student-specific information
      const quiz = await prisma.quiz.findUnique({
        where: { 
          id: quizId,
          isPublished: true,
          // Only show quizzes that are currently available
          OR: [
            { startTime: null },
            { startTime: { lte: new Date() } }
          ],
          AND: [
            {
              OR: [
                { endTime: null },
                { endTime: { gte: new Date() } }
              ]
            }
          ]
        },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          questions: {
            select: {
              id: true,
              type: true,
              text: true,
              tags: true,
              difficulty: true
            }
          },
          _count: {
            select: {
              questions: true,
              attempts: true,
              enrollments: true
            }
          },
          enrollments: {
            where: { userId: user.id },
            select: { 
              id: true,
              enrolledAt: true
            }
          },
          attempts: {
            where: { userId: user.id },
            select: {
              id: true,
              score: true,
              percentage: true,
              completedAt: true,
              startedAt: true,
              isCompleted: true
            },
            orderBy: { startedAt: 'desc' },
            take: 5
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found or not available', 404)
      }

      // Calculate average score from all attempts (not just this user)
      const allAttempts = await prisma.quizAttempt.findMany({
        where: { 
          quizId: quiz.id,
          completedAt: { not: null }
        },
        select: { percentage: true }
      })

      const averageScore = allAttempts.length > 0 
        ? Math.round(allAttempts.reduce((sum, attempt) => sum + (attempt.percentage || 0), 0) / allAttempts.length)
        : 0

      // Get recent reviews/ratings (if implemented)
      // For now, we'll use mock data or skip this

      // Generate objectives based on quiz content
      const objectives = [
        `Master ${quiz.title.toLowerCase()} concepts and principles`,
        `Apply knowledge through ${quiz._count.questions} practice questions`,
        `Achieve proficiency in ${quiz.difficulty.toLowerCase()} level topics`,
        `Understand key concepts in ${quiz.tags.join(', ') || 'the subject area'}`
      ]

      // Generate syllabus based on question tags and types
      const topicCounts = quiz.questions.reduce((acc: Record<string, number>, question) => {
        question.tags.forEach(tag => {
          acc[tag] = (acc[tag] || 0) + 1
        })
        return acc
      }, {})

      const syllabus = Object.entries(topicCounts).map(([topic, count]) => ({
        topic: topic.charAt(0).toUpperCase() + topic.slice(1),
        description: `Comprehensive coverage of ${topic} concepts and applications`,
        questionCount: count
      }))

      // If no tags, create generic syllabus
      if (syllabus.length === 0) {
        syllabus.push({
          topic: quiz.title,
          description: quiz.description || `Core concepts and principles of ${quiz.title}`,
          questionCount: quiz._count.questions
        })
      }

      // Generate instructor info from creator with real data
      const [totalQuizzes, totalStudents] = await Promise.all([
        prisma.quiz.count({ where: { creator: { id: quiz.creator.id } } }),
        prisma.quizEnrollment.count({
          where: {
            quiz: { creator: { id: quiz.creator.id } }
          }
        })
      ])

      const instructor = {
        name: quiz.creator.name,
        email: quiz.creator.email,
        bio: `Experienced educator specializing in ${quiz.tags.join(', ') || 'various subjects'}. Committed to helping students achieve their learning goals through well-designed assessments.`,
        totalQuizzes,
        totalStudents,
        avatar: quiz.creator.image
      }

      // Get real reviews from database
      const reviews = await prisma.quizReview.findMany({
        where: { quizId },
        include: {
          user: {
            select: {
              name: true,
              image: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10 // Limit to recent 10 reviews
      })

      const formattedReviews = reviews.map(review => ({
        id: review.id,
        user: {
          name: review.user.name || 'Anonymous',
          avatar: review.user.image
        },
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt.toISOString()
      }))

      // Transform data to match frontend QuizDetails interface exactly
      const transformedQuiz = {
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        instructions: quiz.instructions,
        type: quiz.type,
        difficulty: quiz.difficulty,
        tags: quiz.tags,
        thumbnail: quiz.thumbnail,
        timeLimit: quiz.timeLimit,
        duration: quiz.timeLimit, // Add duration property
        passingScore: quiz.passingScore,
        maxAttempts: quiz.maxAttempts,
        questionCount: quiz._count.questions,
        totalAttempts: quiz._count.attempts,
        enrollmentCount: quiz._count.enrollments,
        averageScore,
        isEnrolled: quiz.enrollments.length > 0,
        enrolledAt: quiz.enrollments[0]?.enrolledAt?.toISOString() || null,
        userAttempts: quiz.attempts.map(attempt => ({
          id: attempt.id,
          score: attempt.score,
          percentage: attempt.percentage,
          completedAt: attempt.completedAt?.toISOString() || '',
          startedAt: attempt.startedAt.toISOString(),
          isCompleted: attempt.isCompleted
        })),
        bestAttempt: quiz.attempts.length > 0
          ? (() => {
              const best = quiz.attempts.reduce((best, current) =>
                (current.percentage || 0) > (best.percentage || 0) ? current : best
              )
              return {
                id: best.id,
                score: best.score,
                percentage: best.percentage,
                completedAt: best.completedAt?.toISOString() || '',
                startedAt: best.startedAt.toISOString(),
                isCompleted: best.isCompleted
              }
            })()
          : null,
        instructor,
        objectives,
        syllabus,
        reviews: formattedReviews,
        rating: formattedReviews.length > 0
          ? formattedReviews.reduce((sum, review) => sum + review.rating, 0) / formattedReviews.length
          : 4.5,
        reviewCount: formattedReviews.length,
        prerequisites: [
          'Basic understanding of the subject area',
          'Completion of prerequisite courses (if any)',
          'Access to required materials'
        ],
        isFavorite: false, // Would come from user preferences
        schedule: quiz.startTime || quiz.endTime ? {
          startTime: quiz.startTime?.toISOString() || new Date().toISOString(),
          endTime: quiz.endTime?.toISOString() || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: (!quiz.startTime || quiz.startTime <= new Date()) &&
                   (!quiz.endTime || quiz.endTime >= new Date())
        } : undefined,
        createdAt: quiz.createdAt.toISOString(),
        updatedAt: quiz.updatedAt.toISOString(),
        startTime: quiz.startTime?.toISOString() || null,
        endTime: quiz.endTime?.toISOString() || null,
        // Additional metadata
        canAttempt: quiz.enrollments.length > 0 &&
                   (quiz.maxAttempts === null || quiz.attempts.length < quiz.maxAttempts),
        hasActiveAttempt: quiz.attempts.some(attempt => !attempt.isCompleted),
        hasCompleted: quiz.attempts.some(attempt => attempt.isCompleted),
        isAvailable: (!quiz.startTime || quiz.startTime <= new Date()) &&
                    (!quiz.endTime || quiz.endTime >= new Date())
      }

      return APIResponse.success(
        transformedQuiz,
        'Quiz details retrieved successfully'
      )

    } catch (error) {
      console.error('Error fetching quiz details:', error)
      return APIResponse.error('Failed to fetch quiz details', 500)
    }
  }
)
