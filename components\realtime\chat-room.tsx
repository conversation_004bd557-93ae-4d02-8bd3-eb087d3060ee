"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Send,
  MessageCircle,
  Smile,
  Paperclip,
  MoreHorizontal,
  UserPlus,
  UserMinus
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { getSocketClient, ChatMessage } from "@/lib/socket-client"
import { useSession } from "next-auth/react"
import { toast } from "@/lib/toast-utils"

interface ChatRoomProps {
  roomId: string
  roomName: string
  roomType?: 'public' | 'private' | 'quiz'
  className?: string
}

interface TypingUser {
  userId: string
  userName: string
}

export function ChatRoom({
  roomId,
  roomName,
  roomType = 'public',
  className = ""
}: ChatRoomProps) {
  const { data: session } = useSession()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [isConnected, setIsConnected] = useState(false)
  const [onlineUsers, setOnlineUsers] = useState<string[]>([])
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [currentUser, setCurrentUser] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState(false)
  const [hasJoinedRoom, setHasJoinedRoom] = useState(false)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!session?.user?.id) return

    const socketClient = getSocketClient()

    // Get current user ID from session (only once)
    if (!currentUser) {
      setCurrentUser(session.user.id)
    }

    // Load message history
    const loadMessageHistory = async () => {
      try {
        const response = await fetch(`/api/chat/messages?roomId=${roomId}&limit=50`)
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.data.messages) {
            setMessages(data.data.messages.map((msg: any) => ({
              id: msg.id,
              userId: msg.userId,
              userName: msg.user.name || 'Unknown',
              message: msg.message,
              type: msg.type,
              timestamp: new Date(msg.createdAt),
              roomId: msg.roomId
            })))
          }
        }
      } catch (error) {
        console.error('Error loading message history:', error)
      }
    }

    loadMessageHistory()

    // Join chat room
    socketClient.joinChatRoom(roomId)

    // Connection status
    const handleConnection = () => {
      setIsConnected(true)
    }

    const handleDisconnection = () => {
      setIsConnected(false)
    }

    // Chat events
    const handleUserJoined = (data: { userId: string, name: string }) => {
      setOnlineUsers(prev => [...prev.filter(id => id !== data.userId), data.userId])
      toast.info(`${data.name} joined the chat`, {
        icon: <UserPlus className="h-4 w-4" />
      })
    }

    const handleMessageReceived = (message: ChatMessage) => {
      // Only handle messages for this specific room
      if (message.roomId !== roomId) {
        return
      }

      setMessages(prev => {
        const newMessages = [...prev, message]
        return newMessages
      })

      // Remove typing indicator for this user
      setTypingUsers(prev => prev.filter(u => u.userId !== message.userId))
    }

    const handleUserTyping = (data: { userId: string, userName: string, isTyping: boolean }) => {
      if (data.userId === currentUser) return // Don't show own typing

      setTypingUsers(prev => {
        if (data.isTyping) {
          return prev.find(u => u.userId === data.userId) 
            ? prev 
            : [...prev, { userId: data.userId, userName: data.userName }]
        } else {
          return prev.filter(u => u.userId !== data.userId)
        }
      })
    }

    // Set up event listeners
    socketClient.on('connection:established', handleConnection)
    socketClient.on('connection:lost', handleDisconnection)
    socketClient.on('chat:user_joined', handleUserJoined)
    socketClient.on('chat:message_received', handleMessageReceived)
    socketClient.on('chat:user_typing', handleUserTyping)

    // Initial connection status
    setIsConnected(socketClient.isConnected())

    // Cleanup
    return () => {
      socketClient.off('connection:established', handleConnection)
      socketClient.off('connection:lost', handleDisconnection)
      socketClient.off('chat:user_joined', handleUserJoined)
      socketClient.off('chat:message_received', handleMessageReceived)
      socketClient.off('chat:user_typing', handleUserTyping)

      // Only leave room in production or when truly unmounting
      // In development mode, React remounts components frequently
      if (process.env.NODE_ENV === 'production') {
        socketClient.leaveChatRoom(roomId)
      }

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [roomId, session?.user?.id])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const sendMessage = () => {
    if (!newMessage.trim() || !isConnected) {
      return
    }

    const socketClient = getSocketClient()
    socketClient.sendChatMessage({
      roomId,
      message: newMessage.trim(),
      type: 'text'
    })

    setNewMessage("")

    // Stop typing indicator
    if (isTyping) {
      socketClient.setTyping(roomId, false)
      setIsTyping(false)
    }
  }

  const handleInputChange = (value: string) => {
    setNewMessage(value)

    if (!isConnected) return

    const socketClient = getSocketClient()

    // Start typing indicator
    if (!isTyping && value.trim()) {
      socketClient.setTyping(roomId, true)
      setIsTyping(true)
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        socketClient.setTyping(roomId, false)
        setIsTyping(false)
      }
    }, 2000)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getRoomTypeColor = () => {
    switch (roomType) {
      case 'private':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'quiz':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  return (
    <div className={`flex flex-col h-[600px] md:h-[500px] lg:h-[600px] ${className}`}>
      <Card className="flex-1 flex flex-col shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        {/* Modern Header */}
        <CardHeader className="pb-4 px-4 md:px-6 border-b bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <MessageCircle className="h-5 w-5 text-white" />
                </div>
                <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-900 ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                  {roomName}
                </CardTitle>
                <CardDescription className="flex items-center gap-2 text-sm">
                  <span className={isConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                  {onlineUsers.length > 0 && (
                    <>
                      <span className="text-gray-400">•</span>
                      <span className="text-gray-600 dark:text-gray-400">{onlineUsers.length} online</span>
                    </>
                  )}
                </CardDescription>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge className={`${getRoomTypeColor()} font-medium px-3 py-1 rounded-full`}>
                {roomType}
              </Badge>
              <Button variant="ghost" size="sm" className="h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Modern Messages Area */}
        <CardContent className="flex-1 flex flex-col p-0 bg-gray-50/30 dark:bg-gray-900/30">
          <ScrollArea className="flex-1 px-3 md:px-6">
            <div className="space-y-3 py-4">
              <AnimatePresence>
                {messages.map((message, index) => {
                    const isOwnMessage = message.userId === currentUser
                    const showAvatar = index === 0 || messages[index - 1]?.userId !== message.userId

                    return (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.4, ease: "easeOut" }}
                    className={`flex gap-3 ${isOwnMessage ? 'flex-row-reverse' : ''} group`}
                  >
                    {/* Avatar */}
                    <div className="flex-shrink-0">
                      {showAvatar ? (
                        <Avatar className="h-9 w-9 ring-2 ring-white dark:ring-gray-800 shadow-sm">
                          <AvatarFallback className={`text-xs font-semibold ${
                            isOwnMessage
                              ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white'
                              : 'bg-gradient-to-br from-gray-500 to-gray-600 text-white'
                          }`}>
                            {message.userName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                      ) : (
                        <div className="h-9 w-9" />
                      )}
                    </div>

                    {/* Message Content */}
                    <div className={`flex-1 max-w-[75%] md:max-w-[60%] ${isOwnMessage ? 'text-right' : ''}`}>
                      {/* User name and timestamp */}
                      {showAvatar && (
                        <div className={`flex items-center gap-2 mb-2 ${isOwnMessage ? 'justify-end' : ''}`}>
                          <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                            {isOwnMessage ? 'You' : message.userName}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {formatTime(message.timestamp)}
                          </span>
                        </div>
                      )}

                      {/* Message bubble */}
                      <div className={`inline-block p-3 md:p-4 rounded-2xl text-sm leading-relaxed shadow-sm transition-all duration-200 group-hover:shadow-md ${
                        isOwnMessage
                          ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-br-md'
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 rounded-bl-md'
                      }`}>
                        <p className="break-words">{message.message}</p>
                      </div>

                      {/* Message status (for own messages) */}
                      {isOwnMessage && (
                        <div className="flex justify-end mt-1">
                          <span className="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                            Sent
                          </span>
                        </div>
                      )}
                    </div>
                  </motion.div>
                    )
                  })}
              </AnimatePresence>

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Modern Message Input */}
          <div className="p-4 md:p-6 border-t bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
            <div className="flex gap-3 items-end">
              <div className="flex-1 relative">
                <div className="relative">
                  <Input
                    value={newMessage}
                    onChange={(e) => handleInputChange(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={isConnected ? "Type your message..." : "Connecting..."}
                    disabled={!isConnected}
                    className="pr-20 py-3 md:py-4 rounded-2xl border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 focus:bg-white dark:focus:bg-gray-700 transition-all duration-200 text-sm md:text-base"
                  />
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      disabled={!isConnected}
                    >
                      <Smile className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      disabled={!isConnected}
                    >
                      <Paperclip className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    </Button>
                  </div>
                </div>

                {/* Typing indicator */}
                {typingUsers.length > 0 && (
                  <div className="absolute -top-8 left-3 text-xs text-gray-500 dark:text-gray-400">
                    {typingUsers.length === 1
                      ? `${typingUsers[0].userName} is typing...`
                      : `${typingUsers.length} people are typing...`
                    }
                  </div>
                )}
              </div>

              <Button
                onClick={sendMessage}
                disabled={!newMessage.trim() || !isConnected}
                className={`h-12 w-12 rounded-2xl transition-all duration-200 ${
                  newMessage.trim() && isConnected
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105'
                    : 'bg-gray-300 dark:bg-gray-600'
                }`}
              >
                <Send className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
