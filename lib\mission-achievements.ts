/**
 * Mission Achievement System
 * 
 * Handles mission-specific achievements and integrates with the existing achievement system.
 */

import { prisma } from '@/lib/prisma'

// Mission-specific achievement definitions
export const MISSION_ACHIEVEMENT_DEFINITIONS = [
  {
    id: 'first_mission',
    title: 'Mission Starter',
    description: 'Complete your first mission',
    icon: '🚀',
    rarity: 'common',
    points: 50,
    condition: (stats: MissionStats) => stats.completedMissions >= 1
  },
  {
    id: 'mission_streak_3',
    title: 'Triple Threat',
    description: 'Complete 3 missions in a row',
    icon: '🔥',
    rarity: 'uncommon',
    points: 100,
    condition: (stats: MissionStats) => stats.completedMissions >= 3
  },
  {
    id: 'mission_streak_5',
    title: 'Mission Master',
    description: 'Complete 5 missions in a row',
    icon: '⭐',
    rarity: 'rare',
    points: 200,
    condition: (stats: MissionStats) => stats.completedMissions >= 5
  },
  {
    id: 'perfect_mission',
    title: 'Perfectionist',
    description: 'Complete a mission with 100% completion rate',
    icon: '💎',
    rarity: 'epic',
    points: 150,
    condition: (stats: MissionStats) => stats.perfectMissions >= 1
  },
  {
    id: 'speed_learner',
    title: 'Speed Learner',
    description: 'Complete a mission in under 1 hour',
    icon: '⚡',
    rarity: 'rare',
    points: 175,
    condition: (stats: MissionStats) => stats.fastestMissionTime <= 60
  },
  {
    id: 'roadmap_explorer',
    title: 'Roadmap Explorer',
    description: 'Start missions from 3 different courses',
    icon: '🗺️',
    rarity: 'uncommon',
    points: 125,
    condition: (stats: MissionStats) => stats.coursesWithMissions >= 3
  },
  {
    id: 'point_collector',
    title: 'Point Collector',
    description: 'Earn 1000 points from missions',
    icon: '🏆',
    rarity: 'epic',
    points: 250,
    condition: (stats: MissionStats) => stats.totalPointsEarned >= 1000
  },
  {
    id: 'mission_completionist',
    title: 'Completionist',
    description: 'Complete all missions in a course',
    icon: '🎯',
    rarity: 'legendary',
    points: 500,
    condition: (stats: MissionStats) => stats.completedCourses >= 1
  },
  {
    id: 'daily_mission',
    title: 'Daily Achiever',
    description: 'Complete missions for 7 consecutive days',
    icon: '📅',
    rarity: 'rare',
    points: 300,
    condition: (stats: MissionStats) => stats.consecutiveDays >= 7
  },
  {
    id: 'mission_mentor',
    title: 'Mission Mentor',
    description: 'Complete 20 missions across all courses',
    icon: '🎓',
    rarity: 'legendary',
    points: 750,
    condition: (stats: MissionStats) => stats.completedMissions >= 20
  }
]

export interface MissionStats {
  completedMissions: number
  perfectMissions: number
  fastestMissionTime: number // in minutes
  coursesWithMissions: number
  totalPointsEarned: number
  completedCourses: number
  consecutiveDays: number
  averageCompletionRate: number
}

export interface AchievementResult {
  id: string
  title: string
  description: string
  icon: string
  rarity: string
  points: number
  unlockedAt: Date
  isNew: boolean
}

export class MissionAchievementService {
  /**
   * Check and award achievements for a user based on their mission progress
   */
  static async checkAndAwardAchievements(userId: string): Promise<AchievementResult[]> {
    try {
      // Get user's mission statistics
      const stats = await this.getUserMissionStats(userId)
      
      // Get existing achievements
      const existingAchievements = await prisma.userAchievement.findMany({
        where: { userId },
        select: { achievementId: true }
      })
      
      const existingIds = new Set(existingAchievements.map(a => a.achievementId))
      const newAchievements: AchievementResult[] = []
      
      // Check each achievement definition
      for (const achievement of MISSION_ACHIEVEMENT_DEFINITIONS) {
        if (!existingIds.has(achievement.id) && achievement.condition(stats)) {
          // Award new achievement
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id,
              unlockedAt: new Date()
            }
          })
          
          newAchievements.push({
            id: achievement.id,
            title: achievement.title,
            description: achievement.description,
            icon: achievement.icon,
            rarity: achievement.rarity,
            points: achievement.points,
            unlockedAt: new Date(),
            isNew: true
          })
        }
      }
      
      return newAchievements
    } catch (error) {
      console.error('Error checking mission achievements:', error)
      return []
    }
  }
  
  /**
   * Get user's mission statistics for achievement calculation
   */
  static async getUserMissionStats(userId: string): Promise<MissionStats> {
    try {
      // Get all mission progress for the user
      const missionProgress = await prisma.missionProgress.findMany({
        where: { userId },
        include: {
          mission: {
            select: {
              courseId: true,
              pointsReward: true
            }
          }
        }
      })
      
      const completedMissions = missionProgress.filter(p => p.isCompleted).length
      const perfectMissions = missionProgress.filter(p => p.completionRate >= 100).length
      const totalPointsEarned = missionProgress.reduce((sum, p) => sum + p.pointsEarned, 0)
      
      // Calculate fastest mission time (simplified - in production, you'd track actual time)
      const fastestMissionTime = completedMissions > 0 ? 45 : Infinity // Mock value
      
      // Count unique courses with missions
      const coursesWithMissions = new Set(
        missionProgress.map(p => p.mission.courseId)
      ).size
      
      // Count completed courses (all missions in course completed)
      const completedCourses = await this.getCompletedCoursesCount(userId)
      
      // Calculate consecutive days (simplified)
      const consecutiveDays = await this.getConsecutiveDays(userId)
      
      // Calculate average completion rate
      const averageCompletionRate = missionProgress.length > 0
        ? missionProgress.reduce((sum, p) => sum + p.completionRate, 0) / missionProgress.length
        : 0
      
      return {
        completedMissions,
        perfectMissions,
        fastestMissionTime,
        coursesWithMissions,
        totalPointsEarned,
        completedCourses,
        consecutiveDays,
        averageCompletionRate
      }
    } catch (error) {
      console.error('Error getting mission stats:', error)
      return {
        completedMissions: 0,
        perfectMissions: 0,
        fastestMissionTime: Infinity,
        coursesWithMissions: 0,
        totalPointsEarned: 0,
        completedCourses: 0,
        consecutiveDays: 0,
        averageCompletionRate: 0
      }
    }
  }
  
  /**
   * Get count of courses where user has completed all missions
   */
  private static async getCompletedCoursesCount(userId: string): Promise<number> {
    try {
      // Get courses with missions that the user is enrolled in
      const coursesWithMissions = await prisma.course.findMany({
        where: {
          hasRoadmap: true,
          enrollments: {
            some: { userId }
          }
        },
        include: {
          missions: {
            include: {
              progress: {
                where: { userId },
                take: 1
              }
            }
          }
        }
      })
      
      let completedCourses = 0
      
      for (const course of coursesWithMissions) {
        const totalMissions = course.missions.length
        const completedMissions = course.missions.filter(m => 
          m.progress[0]?.isCompleted
        ).length
        
        if (totalMissions > 0 && completedMissions === totalMissions) {
          completedCourses++
        }
      }
      
      return completedCourses
    } catch (error) {
      console.error('Error getting completed courses count:', error)
      return 0
    }
  }
  
  /**
   * Calculate consecutive days with mission activity
   */
  private static async getConsecutiveDays(userId: string): Promise<number> {
    try {
      // Get recent mission completions
      const recentCompletions = await prisma.missionProgress.findMany({
        where: {
          userId,
          isCompleted: true,
          completedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        },
        orderBy: { completedAt: 'desc' },
        select: { completedAt: true }
      })
      
      if (recentCompletions.length === 0) return 0
      
      // Calculate consecutive days
      let streak = 0
      let currentDate = new Date()
      currentDate.setHours(0, 0, 0, 0)
      
      const completionDates = recentCompletions
        .map(c => {
          const date = new Date(c.completedAt!)
          date.setHours(0, 0, 0, 0)
          return date.getTime()
        })
        .filter((date, index, arr) => arr.indexOf(date) === index) // Remove duplicates
        .sort((a, b) => b - a) // Sort descending
      
      for (const completionTime of completionDates) {
        if (completionTime === currentDate.getTime()) {
          streak++
          currentDate.setDate(currentDate.getDate() - 1)
        } else if (completionTime === currentDate.getTime() + 24 * 60 * 60 * 1000) {
          // Allow for yesterday if today has no activity
          streak++
          currentDate.setDate(currentDate.getDate() - 1)
        } else {
          break
        }
      }
      
      return streak
    } catch (error) {
      console.error('Error calculating consecutive days:', error)
      return 0
    }
  }
  
  /**
   * Get all achievements for a user (both earned and available)
   */
  static async getUserAchievements(userId: string) {
    try {
      const stats = await this.getUserMissionStats(userId)
      const userAchievements = await prisma.userAchievement.findMany({
        where: { userId },
        select: { achievementId: true, unlockedAt: true }
      })
      
      const earnedIds = new Set(userAchievements.map(a => a.achievementId))
      const earned = []
      const available = []
      
      for (const achievement of MISSION_ACHIEVEMENT_DEFINITIONS) {
        const isEarned = earnedIds.has(achievement.id)
        const userAchievement = userAchievements.find(a => a.achievementId === achievement.id)
        
        if (isEarned) {
          earned.push({
            ...achievement,
            unlockedAt: userAchievement?.unlockedAt,
            progress: 100
          })
        } else {
          // Calculate progress towards achievement
          let progress = 0
          // This would need specific progress calculation for each achievement
          // For now, we'll use a simple check
          progress = achievement.condition(stats) ? 100 : 0
          
          available.push({
            ...achievement,
            progress
          })
        }
      }
      
      return { earned, available }
    } catch (error) {
      console.error('Error getting user achievements:', error)
      return { earned: [], available: [] }
    }
  }
}
