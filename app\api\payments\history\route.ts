import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { razorpayService } from '@/lib/razorpay-service'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['all', 'created', 'paid', 'failed', 'cancelled']).optional().default('all')
})

// GET /api/payments/history - Get user's payment history
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, status } = validatedQuery

      // Get user's payments
      const payments = await razorpayService.getUserPayments(user.id)

      // Filter by status if specified
      const filteredPayments = status === 'all' 
        ? payments 
        : payments.filter(payment => payment.status === status)

      // Paginate results
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedPayments = filteredPayments.slice(startIndex, endIndex)

      // Calculate summary statistics
      const summary = {
        totalPayments: filteredPayments.length,
        totalAmount: filteredPayments
          .filter(p => p.status === 'paid')
          .reduce((sum, p) => sum + p.amount, 0),
        successfulPayments: filteredPayments.filter(p => p.status === 'paid').length,
        failedPayments: filteredPayments.filter(p => p.status === 'failed').length,
        pendingPayments: filteredPayments.filter(p => p.status === 'created').length
      }

      // Format payment data for response
      const formattedPayments = paginatedPayments.map(payment => ({
        id: payment.id,
        orderId: payment.razorpayOrderId,
        paymentId: payment.razorpayPaymentId,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        createdAt: payment.createdAt,
        paidAt: payment.paidAt,
        failureReason: payment.failureReason,
        course: payment.course ? {
          id: payment.course.id,
          title: payment.course.title,
          slug: payment.course.slug,
          thumbnailImage: payment.course.thumbnailImage
        } : null,
        enrollment: payment.enrollments?.[0] ? {
          id: payment.enrollments[0].id,
          status: payment.enrollments[0].status,
          progress: payment.enrollments[0].progress
        } : null
      }))

      return APIResponse.success({
        payments: formattedPayments,
        summary,
        pagination: {
          page,
          limit,
          total: filteredPayments.length,
          totalPages: Math.ceil(filteredPayments.length / limit),
          hasNext: endIndex < filteredPayments.length,
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching payment history:', error)
      return APIResponse.error('Failed to fetch payment history', 500)
    }
  }
)
