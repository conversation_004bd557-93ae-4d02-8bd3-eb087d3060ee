'use client'

import { useState, useEffect } from 'react'
import { Heart } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface QuizFavoriteButtonProps {
  quizId: string
  className?: string
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  showText?: boolean
}

export function QuizFavoriteButton({ 
  quizId, 
  className,
  variant = 'outline',
  size = 'default',
  showText = true
}: QuizFavoriteButtonProps) {
  const [isFavorited, setIsFavorited] = useState(false)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)

  useEffect(() => {
    checkFavoriteStatus()
  }, [quizId])

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/favorite`)
      if (response.ok) {
        const data = await response.json()
        setIsFavorited(data.isFavorited)
      } else {
        console.error('Failed to check favorite status:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error checking favorite status:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = async () => {
    setUpdating(true)
    try {
      const method = isFavorited ? 'DELETE' : 'POST'

      const response = await fetch(`/api/student/quizzes/${quizId}/favorite`, {
        method
      })

      if (response.ok) {
        const newFavoriteState = !isFavorited
        setIsFavorited(newFavoriteState)

        // Add a small animation effect
        const button = document.querySelector(`[data-quiz-id="${quizId}"]`)
        if (button) {
          button.classList.add('animate-bounce')
          setTimeout(() => button.classList.remove('animate-bounce'), 600)
        }

        toast.success(
          newFavoriteState
            ? '❤️ Quiz added to favorites'
            : '💔 Quiz removed from favorites'
        )
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `Failed to update favorites (${response.status})`
        console.error('Favorite API Error:', response.status, errorData)
        toast.error(errorMessage)
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('Failed to update favorites')
    } finally {
      setUpdating(false)
    }
  }

  if (loading) {
    return (
      <Button
        variant={variant}
        size={size}
        disabled
        className={cn('animate-pulse', className)}
      >
        <Heart className="h-4 w-4 animate-pulse text-gray-400" />
        {showText && size !== 'icon' && (
          <span className="ml-2">Loading...</span>
        )}
      </Button>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleFavorite}
      disabled={updating}
      data-quiz-id={quizId}
      className={cn(
        'transition-all duration-300 hover:scale-105',
        isFavorited && variant === 'outline' && 'border-red-500 bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950/30 dark:text-red-400 dark:hover:bg-red-950/40',
        isFavorited && variant === 'ghost' && 'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950/30 dark:text-red-400 dark:hover:bg-red-950/40',
        isFavorited && variant === 'default' && 'bg-red-500 text-white hover:bg-red-600 border-red-500',
        updating && 'animate-pulse',
        className
      )}
    >
      <Heart
        className={cn(
          'h-4 w-4 transition-all duration-300',
          isFavorited
            ? 'fill-red-500 text-red-500 scale-110 drop-shadow-sm'
            : 'text-current hover:text-red-400 hover:scale-105',
          updating && 'animate-pulse'
        )}
      />
      {showText && size !== 'icon' && (
        <span className="ml-2 font-medium">
          {updating
            ? 'Updating...'
            : isFavorited
              ? 'Favorited'
              : 'Add to Favorites'
          }
        </span>
      )}
    </Button>
  )
}
