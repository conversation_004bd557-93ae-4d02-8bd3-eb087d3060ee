import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/student/live-quiz/sessions/[id]/current-question - Get current question for student
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { params, user }) => {
    const resolvedParams = await params
    const sessionId = resolvedParams?.id as string

    if (!sessionId) {
      return APIResponse.error('Session ID is required', 400)
    }

    try {
      // Get session details
      const session = await prisma.liveQuizSession.findUnique({
        where: { id: sessionId },
        include: {
          quiz: {
            select: {
              id: true,
              title: true,
              questions: {
                select: { id: true, order: true },
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!session) {
        return APIResponse.error('Live quiz session not found', 404, 'SESSION_NOT_FOUND')
      }

      // Verify user is participating
      const participation = await prisma.liveQuizParticipant.findUnique({
        where: {
          sessionId_userId: {
            sessionId,
            userId: user.id
          }
        },
        select: {
          id: true,
          isActive: true,
          currentQuestion: true,
          answers: true,
          score: true,
          rank: true
        }
      })

      if (!participation) {
        return APIResponse.error('Not participating in this session', 400, 'NOT_PARTICIPATING')
      }

      if (!participation.isActive) {
        return APIResponse.error('Participation is not active', 400, 'PARTICIPATION_INACTIVE')
      }

      // Check session status
      if (session.status === 'WAITING') {
        return APIResponse.success({
          session: {
            id: session.id,
            title: session.title,
            status: session.status,
            currentQuestion: session.currentQuestion,
            totalQuestions: session.quiz.questions.length
          },
          message: 'Waiting for session to start',
          currentQuestionData: null,
          userProgress: {
            currentQuestion: participation.currentQuestion,
            score: participation.score,
            rank: participation.rank
          }
        }, 'Session is waiting to start')
      }

      if (session.status === 'PAUSED') {
        return APIResponse.success({
          session: {
            id: session.id,
            title: session.title,
            status: session.status,
            currentQuestion: session.currentQuestion,
            totalQuestions: session.quiz.questions.length
          },
          message: 'Session is paused',
          currentQuestionData: null,
          userProgress: {
            currentQuestion: participation.currentQuestion,
            score: participation.score,
            rank: participation.rank
          }
        }, 'Session is paused')
      }

      if (session.status === 'COMPLETED') {
        return APIResponse.success({
          session: {
            id: session.id,
            title: session.title,
            status: session.status,
            currentQuestion: session.currentQuestion,
            totalQuestions: session.quiz.questions.length
          },
          message: 'Session has completed',
          currentQuestionData: null,
          userProgress: {
            currentQuestion: participation.currentQuestion,
            score: participation.score,
            rank: participation.rank
          }
        }, 'Session has completed')
      }

      // Session is active - get current question
      const currentQuestion = await prisma.question.findFirst({
        where: {
          quizId: session.quizId,
          order: session.currentQuestion
        },
        select: {
          id: true,
          type: true,
          text: true,
          options: true,
          points: true,
          order: true,
          image: true,
          difficulty: true,
          tags: true
        }
      })

      if (!currentQuestion) {
        return APIResponse.error('Current question not found', 404, 'QUESTION_NOT_FOUND')
      }

      // Check if user has already answered this question
      const userAnswers = participation.answers as Record<string, any>
      const userAnswer = userAnswers[currentQuestion.id]
      const hasAnswered = userAnswer !== undefined

      // Get question statistics (how many participants have answered)
      const participantAnswers = await prisma.liveQuizParticipant.findMany({
        where: { 
          sessionId,
          isActive: true
        },
        select: {
          answers: true
        }
      })

      const questionStats = {
        totalParticipants: participantAnswers.length,
        answeredCount: 0,
        answerDistribution: {} as Record<string, number>
      }

      for (const participant of participantAnswers) {
        const answers = participant.answers as Record<string, any>
        const answer = answers[currentQuestion.id]
        
        if (answer !== undefined) {
          questionStats.answeredCount++
          
          // Track answer distribution (for admin analytics, not shown to students)
          const answerKey = String(answer)
          questionStats.answerDistribution[answerKey] = (questionStats.answerDistribution[answerKey] || 0) + 1
        }
      }

      // Calculate progress
      const progress = {
        current: session.currentQuestion + 1,
        total: session.quiz.questions.length,
        percentage: Math.round(((session.currentQuestion + 1) / session.quiz.questions.length) * 100)
      }

      return APIResponse.success({
        session: {
          id: session.id,
          title: session.title,
          status: session.status,
          currentQuestion: session.currentQuestion,
          totalQuestions: session.quiz.questions.length,
          questionTimeLimit: session.questionTimeLimit,
          autoAdvance: session.autoAdvance,
          showLeaderboard: session.showLeaderboard
        },
        currentQuestionData: {
          ...currentQuestion,
          hasAnswered,
          userAnswer: hasAnswered ? userAnswer : null
        },
        userProgress: {
          currentQuestion: participation.currentQuestion,
          score: participation.score,
          rank: participation.rank,
          answeredCurrentQuestion: hasAnswered
        },
        progress,
        questionStats: {
          totalParticipants: questionStats.totalParticipants,
          answeredCount: questionStats.answeredCount,
          answerRate: questionStats.totalParticipants > 0 
            ? Math.round((questionStats.answeredCount / questionStats.totalParticipants) * 100)
            : 0
        }
      }, 'Current question retrieved successfully')

    } catch (error) {
      console.error('Error fetching current question:', error)
      return APIResponse.error('Failed to fetch current question', 500)
    }
  }
)
