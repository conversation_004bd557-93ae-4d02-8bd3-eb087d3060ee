'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  CreditCard, 
  Calendar, 
  Filter, 
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  ArrowUpRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { PaymentStatus } from '@/components/payments/payment-status'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

interface Payment {
  id: string
  orderId: string
  paymentId?: string
  amount: number
  currency: string
  status: 'created' | 'paid' | 'failed' | 'cancelled'
  createdAt: string
  paidAt?: string
  failureReason?: string
  course: {
    id: string
    title: string
    slug: string
    thumbnailImage?: string
  } | null
  enrollment: {
    id: string
    status: string
    progress: number
  } | null
}

interface PaymentSummary {
  totalPayments: number
  totalAmount: number
  successfulPayments: number
  failedPayments: number
  pendingPayments: number
}

export default function PaymentHistoryPage() {
  const router = useRouter()
  const [payments, setPayments] = useState<Payment[]>([])
  const [summary, setSummary] = useState<PaymentSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchPaymentHistory()
  }, [statusFilter])

  const fetchPaymentHistory = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        status: statusFilter,
        limit: '50'
      })

      const response = await fetch(`/api/payments/history?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch payment history')
      }

      const data = await response.json()
      setPayments(data.data.payments)
      setSummary(data.data.summary)
    } catch (error) {
      console.error('Error fetching payment history:', error)
      toast.error('Failed to load payment history')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const handleViewCourse = (courseSlug: string) => {
    router.push(`/student/courses/${courseSlug}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-white dark:via-gray-100 dark:to-white bg-clip-text text-transparent mb-2">
            Payment History
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Track all your course purchases and payment transactions
          </p>
        </motion.div>

        {/* Summary Cards */}
        {summary && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          >
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Total Spent</p>
                    <p className="text-2xl font-bold text-green-600">
                      {formatAmount(summary.totalAmount, 'INR')}
                    </p>
                  </div>
                  <CreditCard className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Successful</p>
                    <p className="text-2xl font-bold text-blue-600">{summary.successfulPayments}</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Failed</p>
                    <p className="text-2xl font-bold text-red-600">{summary.failedPayments}</p>
                  </div>
                  <XCircle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Pending</p>
                    <p className="text-2xl font-bold text-yellow-600">{summary.pendingPayments}</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center gap-4">
            <Filter className="h-5 w-5 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Payments</SelectItem>
                <SelectItem value="paid">Successful</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="created">Pending</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </motion.div>

        {/* Payment List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-4"
        >
          {payments.length === 0 ? (
            <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
                  No payments found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {statusFilter === 'all' 
                    ? "You haven't made any course purchases yet."
                    : `No ${statusFilter} payments found.`
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            payments.map((payment, index) => (
              <motion.div
                key={payment.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {payment.course?.thumbnailImage ? (
                          <img
                            src={payment.course.thumbnailImage}
                            alt={payment.course.title}
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <CreditCard className="h-8 w-8 text-white" />
                          </div>
                        )}

                        <div>
                          <h3 className="font-semibold text-gray-800 dark:text-white mb-1">
                            {payment.course?.title || 'Course Purchase'}
                          </h3>
                          <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                            <span>Order #{payment.orderId.slice(-8)}</span>
                            <span>•</span>
                            <span>{formatDate(payment.createdAt)}</span>
                            {payment.paidAt && (
                              <>
                                <span>•</span>
                                <span>Paid {formatDate(payment.paidAt)}</span>
                              </>
                            )}
                          </div>
                          {payment.failureReason && (
                            <p className="text-sm text-red-600 mt-1">{payment.failureReason}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="text-lg font-bold text-gray-800 dark:text-white">
                            {formatAmount(payment.amount, payment.currency)}
                          </p>
                          <PaymentStatus status={payment.status} size="sm" />
                        </div>

                        {payment.course && payment.status === 'paid' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewCourse(payment.course!.slug)}
                            className="flex items-center gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            View Course
                            <ArrowUpRight className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </motion.div>
      </div>
    </div>
  )
}
