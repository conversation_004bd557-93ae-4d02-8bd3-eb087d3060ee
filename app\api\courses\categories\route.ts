import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/courses/categories - Get all course categories
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get distinct categories from courses
      const categories = await prisma.course.findMany({
        where: {
          isActive: true,
          category: { not: null }
        },
        select: {
          category: true,
          _count: {
            select: { enrollments: true }
          }
        },
        distinct: ['category']
      })

      // Get course count per category
      const categoryStats = await prisma.course.groupBy({
        by: ['category'],
        where: {
          isActive: true,
          category: { not: null }
        },
        _count: {
          id: true
        },
        _avg: {
          rating: true,
          price: true
        }
      })

      // Combine data
      const categoriesWithStats = categoryStats.map(stat => ({
        name: stat.category,
        courseCount: stat._count.id,
        averageRating: Math.round((stat._avg.rating || 0) * 10) / 10,
        averagePrice: Math.round((stat._avg.price || 0) * 100) / 100
      })).filter(cat => cat.name) // Filter out null categories

      // Add predefined category metadata
      const categoryMetadata: Record<string, { icon: string; color: string; description: string }> = {
        'engineering': {
          icon: 'Calculator',
          color: 'blue',
          description: 'Engineering entrance exams and technical courses'
        },
        'medical': {
          icon: 'Stethoscope',
          color: 'pink',
          description: 'Medical entrance exams and healthcare courses'
        },
        'government': {
          icon: 'Building',
          color: 'green',
          description: 'Government job preparation and civil services'
        },
        'teaching': {
          icon: 'Users',
          color: 'orange',
          description: 'Teaching exams and education courses'
        },
        'business': {
          icon: 'Briefcase',
          color: 'purple',
          description: 'Business and management courses'
        },
        'technology': {
          icon: 'Laptop',
          color: 'indigo',
          description: 'Technology and programming courses'
        },
        'language': {
          icon: 'Globe',
          color: 'teal',
          description: 'Language learning and communication'
        },
        'finance': {
          icon: 'DollarSign',
          color: 'yellow',
          description: 'Finance and accounting courses'
        }
      }

      // Enhance categories with metadata
      const enhancedCategories = categoriesWithStats.map(category => ({
        ...category,
        ...categoryMetadata[category.name?.toLowerCase() || ''] || {
          icon: 'BookOpen',
          color: 'gray',
          description: 'General courses'
        }
      }))

      // Sort by course count descending
      enhancedCategories.sort((a, b) => b.courseCount - a.courseCount)

      return APIResponse.success({
        categories: enhancedCategories,
        totalCategories: enhancedCategories.length
      })

    } catch (error) {
      console.error('Error fetching course categories:', error)
      return APIResponse.error('Failed to fetch course categories', 500)
    }
  }
)
